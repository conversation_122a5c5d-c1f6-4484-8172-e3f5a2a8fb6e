"""
认证相关的 Pydantic 模型
"""
from typing import Optional
from pydantic import BaseModel, EmailStr, Field, validator
from app.models.user import UserRole


class UserRegister(BaseModel):
    """用户注册模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=6, max_length=100, description="密码")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    
    @validator('username')
    def validate_username(cls, v):
        if not v.isalnum() and '_' not in v:
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v.lower()


class UserLogin(BaseModel):
    """用户登录模型"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class Token(BaseModel):
    """令牌响应模型"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenRefresh(BaseModel):
    """令牌刷新模型"""
    refresh_token: str


class UserProfile(BaseModel):
    """用户资料模型"""
    id: int
    username: str
    email: str
    full_name: Optional[str]
    role: UserRole
    is_active: bool
    is_verified: bool
    translation_quota: int
    used_quota: int
    remaining_quota: int
    preferred_source_lang: str
    preferred_target_lang: str
    created_at: str
    last_login: Optional[str]
    
    class Config:
        from_attributes = True


class UserUpdate(BaseModel):
    """用户更新模型"""
    full_name: Optional[str] = Field(None, max_length=100)
    preferred_source_lang: Optional[str] = Field(None, max_length=10)
    preferred_target_lang: Optional[str] = Field(None, max_length=10)


class PasswordChange(BaseModel):
    """密码修改模型"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")


class UserCreate(BaseModel):
    """管理员创建用户模型"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=6, max_length=100)
    full_name: Optional[str] = Field(None, max_length=100)
    role: UserRole = UserRole.USER
    translation_quota: int = Field(default=1000, ge=0)
    is_active: bool = True


class UserAdminUpdate(BaseModel):
    """管理员更新用户模型"""
    full_name: Optional[str] = Field(None, max_length=100)
    role: Optional[UserRole] = None
    translation_quota: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
