/**
 * 明道云API服务
 * 用于获取翻译文档列表和预览
 */
import { MINGDAO_CONFIG } from '../config/api.config';

// 明道云API响应接口
export interface MingdaoResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error_code?: string;
}

// 明道云文档记录接口
export interface MingdaoDocument {
  rowid: string;
  [key: string]: any;
}

// 获取文档列表的请求参数
export interface GetDocumentsRequest {
  pageSize?: number;
  pageIndex?: number;
  filters?: any[];
}

// 明道云API服务类
export class MingdaoService {
  private baseUrl = MINGDAO_CONFIG.baseUrl;
  private appKey = MINGDAO_CONFIG.appKey;
  private sign = MINGDAO_CONFIG.sign;
  private worksheetId = MINGDAO_CONFIG.worksheetId;

  /**
   * 发送请求到明道云API
   */
  private async makeRequest<T>(endpoint: string, data: any): Promise<MingdaoResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const requestData = {
      appKey: this.appKey,
      sign: this.sign,
      worksheetId: this.worksheetId,
      ...data
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('明道云API请求失败:', error);
      throw error;
    }
  }

  /**
   * 获取文档列表
   */
  async getDocuments(params: GetDocumentsRequest = {}): Promise<MingdaoResponse<MingdaoDocument[]>> {
    const data = {
      pageSize: params.pageSize || 100,
      pageIndex: params.pageIndex || 1,
      listType: 0,
      controls: [],
      filters: params.filters || []
    };

    return this.makeRequest<MingdaoDocument[]>(MINGDAO_CONFIG.endpoints.getRows, data);
  }

  /**
   * 获取文档总数
   */
  async getDocumentsTotal(): Promise<number> {
    try {
      const data = {
        pageSize: 1,
        pageIndex: 1,
        listType: 0,
        controls: []
      };

      const result = await this.makeRequest<string | number>(MINGDAO_CONFIG.endpoints.getTotalNum, data);
      
      if (result.success) {
        const total = result.data;
        return typeof total === 'string' ? parseInt(total, 10) : total;
      }
      
      return 0;
    } catch (error) {
      console.error('获取文档总数失败:', error);
      return 0;
    }
  }

  /**
   * 解析明道云文档记录，提取文件信息
   */
  parseDocumentRecord(record: MingdaoDocument) {
    const originalFileControl = MINGDAO_CONFIG.controlIds.originalFile;
    const translatedFileControl = MINGDAO_CONFIG.controlIds.translatedFile;
    const remarkControl = MINGDAO_CONFIG.controlIds.remark;

    // 提取原文件信息
    const originalFileData = record[originalFileControl];
    let originalFile = null;
    if (originalFileData && Array.isArray(originalFileData) && originalFileData.length > 0) {
      const fileInfo = originalFileData[0];
      originalFile = {
        name: fileInfo.originalFilename || fileInfo.name,
        url: fileInfo.previewUrl || fileInfo.url,
        downloadUrl: fileInfo.downloadUrl || fileInfo.url,
        size: fileInfo.size,
        ext: fileInfo.ext
      };
    }

    // 提取翻译文件信息
    const translatedFileData = record[translatedFileControl];
    let translatedFile = null;
    if (translatedFileData && Array.isArray(translatedFileData) && translatedFileData.length > 0) {
      const fileInfo = translatedFileData[0];
      translatedFile = {
        name: fileInfo.originalFilename || fileInfo.name,
        url: fileInfo.previewUrl || fileInfo.url,
        downloadUrl: fileInfo.downloadUrl || fileInfo.url,
        size: fileInfo.size,
        ext: fileInfo.ext
      };
    }

    // 提取备注信息
    const remark = record[remarkControl] || '';

    return {
      id: record.rowid,
      originalFile,
      translatedFile,
      remark,
      createdAt: record.ctime,
      updatedAt: record.utime,
      hasTranslation: !!translatedFile
    };
  }

  /**
   * 获取文件预览URL
   */
  getFilePreviewUrl(fileUrl: string): string {
    // 如果是明道云文件URL，可以直接用于OnlyOffice预览
    return fileUrl;
  }

  /**
   * 获取文件下载URL
   */
  getFileDownloadUrl(fileUrl: string): string {
    return fileUrl;
  }
}

// 创建全局实例
export const mingdaoService = new MingdaoService();

// 导出类型
export type { MingdaoDocument, GetDocumentsRequest, MingdaoResponse };
