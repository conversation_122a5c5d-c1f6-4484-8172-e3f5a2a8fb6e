// 单个区域的翻译设置接口
export interface TranslationFormatSettings {
  // 字体设置
  fontFamily: string          // 字体名称
  fontSize: number            // 字体大小 (pt)

  // 字体样式
  bold: boolean              // 粗体
  italic: boolean            // 斜体
  underline: boolean         // 下划线

  // 段落格式
  textAlign: string          // 对齐方式: inherit|left|center|right|justify
  textIndent: number         // 首行缩进 (字符)
  lineHeight: string         // 行间距: inherit|1|1.15|1.5|2
  marginTop: number          // 段前间距 (pt)

  // 颜色设置
  color: string              // 文字颜色
  backgroundColor: string    // 背景颜色
}

// 完整的翻译设置接口（包含三个区域）
export interface TranslationSettings {
  // 正文段落设置
  paragraph: TranslationFormatSettings

  // 表格设置
  table: TranslationFormatSettings

  // 页眉设置
  header: TranslationFormatSettings

  // 全局设置
  enableParagraph: boolean   // 是否启用正文翻译
  enableTable: boolean       // 是否启用表格翻译
  enableHeader: boolean      // 是否启用页眉翻译
}

// 默认格式设置
const DEFAULT_FORMAT_SETTINGS: TranslationFormatSettings = {
  fontFamily: 'Arial',
  fontSize: 10.5,
  bold: false,
  italic: false,
  underline: false,
  textAlign: 'inherit',
  textIndent: 0,
  lineHeight: 'inherit',
  marginTop: 0,
  color: '#000000',
  backgroundColor: '#ffffff'
}

// 默认翻译设置（分区域）
export const DEFAULT_TRANSLATION_SETTINGS: TranslationSettings = {
  // 正文段落设置（Arial 10.5pt）
  paragraph: {
    ...DEFAULT_FORMAT_SETTINGS,
    fontSize: 10.5
  },

  // 表格设置（Arial 6pt）
  table: {
    ...DEFAULT_FORMAT_SETTINGS,
    fontSize: 6
  },

  // 页眉设置（Arial 10.5pt）
  header: {
    ...DEFAULT_FORMAT_SETTINGS,
    fontSize: 10.5
  },

  // 全局开关
  enableParagraph: true,
  enableTable: true,
  enableHeader: true
}

// 预设翻译样式
export const PRESET_TRANSLATION_STYLES = {
  // 经典样式（类似VBA宏）
  classic: {
    fontFamily: 'Arial',
    fontSize: 10.5,
    bold: false,
    italic: false,
    underline: false,
    textAlign: 'inherit',
    textIndent: 0,
    lineHeight: 'inherit',
    marginTop: 0,
    color: '#000000',
    backgroundColor: '#ffffff'
  } as TranslationSettings,
  
  // 现代样式
  modern: {
    fontFamily: 'Calibri',
    fontSize: 11,
    bold: false,
    italic: false,
    underline: false,
    textAlign: 'inherit',
    textIndent: 0,
    lineHeight: '1.15',
    marginTop: 3,
    color: '#2c3e50',
    backgroundColor: '#ffffff'
  } as TranslationSettings,
  
  // 突出样式
  highlight: {
    fontFamily: 'Arial',
    fontSize: 10,
    bold: false,
    italic: true,
    underline: false,
    textAlign: 'inherit',
    textIndent: 0,
    lineHeight: 'inherit',
    marginTop: 0,
    color: '#3498db',
    backgroundColor: '#f8f9fa'
  } as TranslationSettings,
  
  // 表格专用样式
  table: {
    fontFamily: 'Arial',
    fontSize: 6,
    bold: false,
    italic: false,
    underline: false,
    textAlign: 'inherit',
    textIndent: 0,
    lineHeight: '1',
    marginTop: 0,
    color: '#000000',
    backgroundColor: '#ffffff'
  } as TranslationSettings
}

// 翻译设置工具函数
export class TranslationSettingsUtils {
  // 保存设置到localStorage和后端
  static async saveSettings(settings: TranslationSettings): Promise<boolean> {
    try {
      // 1. 保存到localStorage（本地备份）
      localStorage.setItem('translationSettings', JSON.stringify(settings))

      // 2. 保存到后端明道云
      const success = await this.saveToBackend(settings)

      return success
    } catch (error) {
      console.error('保存翻译设置失败:', error)
      return false
    }
  }

  // 保存到后端API
  static async saveToBackend(settings: TranslationSettings): Promise<boolean> {
    try {
      console.log('🔍 开始保存翻译设置到后端...')

      // 获取当前用户信息
      const currentUser = this.getCurrentUser()
      console.log('当前用户信息:', currentUser)

      if (!currentUser || !currentUser.id) {
        console.warn('❌ 用户未登录，无法保存到云端')
        return false
      }

      const apiUrl = `http://localhost:8000/api/translation-settings/${currentUser.id}`
      console.log('📤 API请求URL:', apiUrl)
      console.log('📤 请求数据:', settings)

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
      })

      console.log('📥 响应状态码:', response.status)

      if (response.ok) {
        const result = await response.json()
        console.log('✅ 翻译设置已保存到云端:', result)
        return true
      } else {
        const errorText = await response.text()
        console.error('❌ 保存到云端失败:', response.status, response.statusText, errorText)
        return false
      }
    } catch (error) {
      console.error('❌ 保存到后端异常:', error)
      return false
    }
  }

  // 获取当前用户信息
  static getCurrentUser(): { id: string } | null {
    try {
      // 方案1：从localStorage获取（主要方案）
      const userStr = localStorage.getItem('current_user') || localStorage.getItem('currentUser')
      if (userStr) {
        const user = JSON.parse(userStr)
        console.log('获取到用户信息:', user)
        return user
      }

      console.warn('未找到用户信息，请确保用户已登录')
    } catch (error) {
      console.error('获取当前用户失败:', error)
    }
    return null
  }
  
  // 从后端和localStorage加载设置
  static async loadSettings(): Promise<TranslationSettings> {
    try {
      // 1. 尝试从后端加载
      const backendSettings = await this.loadFromBackend()
      if (backendSettings) {
        // 同时更新localStorage
        localStorage.setItem('translationSettings', JSON.stringify(backendSettings))
        return backendSettings
      }

      // 2. 后端加载失败，从localStorage加载
      const saved = localStorage.getItem('translationSettings')
      if (saved) {
        const parsed = JSON.parse(saved)
        return { ...DEFAULT_TRANSLATION_SETTINGS, ...parsed }
      }
    } catch (error) {
      console.error('加载翻译设置失败:', error)
    }
    return { ...DEFAULT_TRANSLATION_SETTINGS }
  }

  // 从后端API加载设置
  static async loadFromBackend(): Promise<TranslationSettings | null> {
    try {
      // 获取当前用户信息
      const currentUser = this.getCurrentUser()
      if (!currentUser || !currentUser.id) {
        console.warn('用户未登录，无法从云端加载设置')
        return null
      }

      const response = await fetch(`http://localhost:8000/api/translation-settings/${currentUser.id}`)

      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          console.log('从云端加载翻译设置成功')
          return result.data
        }
      } else {
        console.warn('从云端加载设置失败:', response.status)
      }
    } catch (error) {
      console.warn('从后端加载设置异常:', error)
    }
    return null
  }

  // 同步版本的加载（兼容现有代码）
  static loadSettingsSync(): TranslationSettings {
    try {
      const saved = localStorage.getItem('translationSettings')
      if (saved) {
        const parsed = JSON.parse(saved)
        return { ...DEFAULT_TRANSLATION_SETTINGS, ...parsed }
      }
    } catch (error) {
      console.error('加载翻译设置失败:', error)
    }
    return { ...DEFAULT_TRANSLATION_SETTINGS }
  }
  
  // 转换单个格式设置为CSS样式对象
  static formatToCSSStyle(settings: TranslationFormatSettings): Record<string, string> {
    const style: Record<string, string> = {
      fontFamily: settings.fontFamily,
      fontSize: `${settings.fontSize}pt`,
      fontWeight: settings.bold ? 'bold' : 'normal',
      fontStyle: settings.italic ? 'italic' : 'normal',
      textDecoration: settings.underline ? 'underline' : 'none',
      color: settings.color,
      backgroundColor: settings.backgroundColor,
      marginTop: `${settings.marginTop}pt`
    }

    if (settings.textAlign !== 'inherit') {
      style.textAlign = settings.textAlign
    }

    if (settings.lineHeight !== 'inherit') {
      style.lineHeight = settings.lineHeight
    }

    if (settings.textIndent > 0) {
      style.textIndent = `${settings.textIndent}em`
    }

    return style
  }

  // 转换为CSS样式对象（兼容旧接口）
  static toCSSStyle(settings: TranslationSettings, type: 'paragraph' | 'table' | 'header' = 'paragraph'): Record<string, string> {
    return this.formatToCSSStyle(settings[type])
  }

  // 转换为Word文档样式参数
  static toWordStyle(settings: TranslationSettings, type: 'paragraph' | 'table' | 'header' = 'paragraph') {
    const formatSettings = settings[type]
    return {
      fontFamily: formatSettings.fontFamily,
      fontSize: formatSettings.fontSize,
      bold: formatSettings.bold,
      italic: formatSettings.italic,
      underline: formatSettings.underline,
      textAlign: formatSettings.textAlign,
      textIndent: formatSettings.textIndent,
      lineHeight: formatSettings.lineHeight,
      marginTop: formatSettings.marginTop,
      color: formatSettings.color,
      backgroundColor: formatSettings.backgroundColor
    }
  }
  
  // 验证设置有效性
  static validateSettings(settings: Partial<TranslationSettings>): boolean {
    try {
      // 检查必需字段
      if (typeof settings.fontFamily !== 'string' || !settings.fontFamily) {
        return false
      }
      
      if (typeof settings.fontSize !== 'number' || settings.fontSize < 6 || settings.fontSize > 72) {
        return false
      }
      
      // 检查颜色格式
      if (settings.color && !/^#[0-9A-Fa-f]{6}$/.test(settings.color)) {
        return false
      }
      
      if (settings.backgroundColor && !/^#[0-9A-Fa-f]{6}$/.test(settings.backgroundColor)) {
        return false
      }
      
      return true
    } catch (error) {
      return false
    }
  }
  
  // 合并设置（用于继承原文格式）
  static mergeWithOriginal(originalStyle: any, translationSettings: TranslationSettings): any {
    const merged = { ...originalStyle }
    
    // 应用翻译设置，但保留原文的某些属性
    merged.fontFamily = translationSettings.fontFamily
    merged.fontSize = translationSettings.fontSize
    merged.bold = translationSettings.bold
    merged.italic = translationSettings.italic
    merged.underline = translationSettings.underline
    merged.color = translationSettings.color
    merged.backgroundColor = translationSettings.backgroundColor
    
    // 段落格式：如果设置为inherit则保留原文格式
    if (translationSettings.textAlign !== 'inherit') {
      merged.textAlign = translationSettings.textAlign
    }
    
    if (translationSettings.lineHeight !== 'inherit') {
      merged.lineHeight = translationSettings.lineHeight
    }
    
    if (translationSettings.textIndent > 0) {
      merged.textIndent = translationSettings.textIndent
    }
    
    if (translationSettings.marginTop > 0) {
      merged.marginTop = translationSettings.marginTop
    }
    
    return merged
  }
}

// 翻译格式应用接口
export interface TranslationFormatRequest {
  rowId: string                           // 记录ID
  settings: TranslationSettings           // 翻译设置
  applyToParagraphs: boolean             // 是否应用到段落
  applyToTables: boolean                 // 是否应用到表格
  applyToHeaders: boolean                // 是否应用到页眉
}

// 翻译格式响应接口
export interface TranslationFormatResponse {
  success: boolean                        // 是否成功
  message: string                        // 响应消息
  rowId: string                          // 记录ID
  appliedSettings: TranslationSettings   // 实际应用的设置
}
