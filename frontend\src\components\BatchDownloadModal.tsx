import React, { useState } from 'react';
import { Download, X, FileText, Globe } from 'lucide-react';

interface BatchDownloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (downloadType: 'text' | 'docx') => void;
  selectedCount: number;
}

const BatchDownloadModal: React.FC<BatchDownloadModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  selectedCount
}) => {
  const [selectedType, setSelectedType] = useState<'text' | 'docx'>('docx');

  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm(selectedType);
    onClose();
  };

  return (
    <div className="batch-download-modal">
      <div className="batch-download-container">
        <div className="batch-download-header">
          <h3>批量下载选择</h3>
          <button
            className="close-btn"
            onClick={onClose}
            title="关闭"
          >
            <X size={20} />
          </button>
        </div>

        <div className="batch-download-content">
          <div className="download-info">
            <p>已选择 <strong>{selectedCount}</strong> 条翻译记录</p>
            <p>请选择要下载的文件格式：</p>
          </div>

          <div className="download-options">
            <label className={`download-option ${selectedType === 'text' ? 'selected' : ''}`}>
              <input
                type="radio"
                name="downloadType"
                value="text"
                checked={selectedType === 'text'}
                onChange={(e) => setSelectedType(e.target.value as 'text' | 'docx')}
              />
              <div className="option-content">
                <FileText size={24} className="option-icon" />
                <div className="option-text">
                  <h4>纯文本格式</h4>
                  <p>下载翻译结果的纯文本文件 (.txt)</p>
                </div>
              </div>
            </label>

            <label className={`download-option ${selectedType === 'docx' ? 'selected' : ''}`}>
              <input
                type="radio"
                name="downloadType"
                value="docx"
                checked={selectedType === 'docx'}
                onChange={(e) => setSelectedType(e.target.value as 'text' | 'docx')}
              />
              <div className="option-content">
                <Globe size={24} className="option-icon" />
                <div className="option-text">
                  <h4>Word文档格式</h4>
                  <p>下载双语对照的Word文档 (.docx)</p>
                </div>
              </div>
            </label>
          </div>
        </div>

        <div className="batch-download-footer">
          <button
            className="btn btn-secondary"
            onClick={onClose}
          >
            取消
          </button>
          <button
            className="btn btn-primary"
            onClick={handleConfirm}
          >
            <Download size={16} />
            开始下载
          </button>
        </div>
      </div>
    </div>
  );
};

export default BatchDownloadModal;
