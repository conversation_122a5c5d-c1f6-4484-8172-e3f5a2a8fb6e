# 🔧 后端启动问题解决方案

## 🚨 **问题现象**
```
ModuleNotFoundError: No module named 'jose'
```

## 🔍 **问题分析**
这个错误表明Python无法找到`jose`模块，可能的原因：
1. **虚拟环境没有正确激活**
2. **依赖没有安装在正确的Python环境中**
3. **Python版本不匹配**

## 🔧 **立即解决方案**

### **方案1：使用正确的Python环境启动**

```bash
# 1. 进入项目目录
cd d:\mywork\Translate

# 2. 激活虚拟环境
.venv\Scripts\activate

# 3. 确认jose已安装
pip show python-jose

# 4. 启动服务器
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **方案2：重新安装依赖**

```bash
cd d:\mywork\Translate
.venv\Scripts\activate
pip install python-jose[cryptography]
pip install passlib[bcrypt]
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **方案3：使用批处理文件（最简单）**

我已经创建了 `start_server.bat`，直接双击运行即可。

## 🧪 **测试翻译设置功能**

服务器启动后，运行：
```bash
python test_translation_api.py
```

这将测试：
- ✅ 服务器连接
- ✅ 保存翻译设置到明道云
- ✅ 读取翻译设置

## 🎯 **预期结果**

成功启动后应该看到：
```
INFO:     Uvicorn running on http://0.0.0.0:8000
INFO:     Application startup complete.
```

然后可以：
- 访问 http://localhost:8000/docs 查看API文档
- 测试翻译设置API
- 在明道云表单中看到保存的记录

## 📞 **如果仍有问题**

请提供：
1. 完整的错误信息
2. `python --version` 的输出
3. `pip list` 的输出
4. 虚拟环境是否正确激活（命令行前是否有 `(.venv)`）

## 🎊 **成功后的下一步**

1. **测试前端保存功能** - 在前端点击保存，检查Network标签
2. **验证明道云记录** - 登录明道云查看 yhfygssz 表单
3. **完整功能测试** - 测试翻译时是否应用了用户设置
