.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.preview-container {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  height: 90%;
  max-width: 1200px;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #dee2e6;
  background-color: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.preview-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  word-break: break-all;
}

.file-info {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.preview-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.preview-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-loading,
.preview-error,
.preview-unsupported {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #6c757d;
}

.preview-loading .spinner {
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
  color: #007bff;
}

.preview-error,
.preview-unsupported {
  color: #dc3545;
}

.preview-error h4,
.preview-unsupported h4 {
  margin: 16px 0 8px 0;
  font-size: 20px;
  color: #495057;
}

.preview-error p,
.preview-unsupported p {
  margin: 0 0 24px 0;
  font-size: 16px;
  max-width: 400px;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

.preview-html {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background-color: white;
  font-family: 'Times New Roman', serif;
  line-height: 1.6;
}

.preview-html h1,
.preview-html h2,
.preview-html h3,
.preview-html h4,
.preview-html h5,
.preview-html h6 {
  color: #495057;
  margin-top: 24px;
  margin-bottom: 12px;
}

.preview-html p {
  margin-bottom: 12px;
  text-align: justify;
}

.preview-html table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.preview-html table th,
.preview-html table td {
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  text-align: left;
}

.preview-html table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.preview-html img {
  max-width: 100%;
  height: auto;
  margin: 16px 0;
  border-radius: 4px;
}

.preview-html ul,
.preview-html ol {
  padding-left: 24px;
  margin: 12px 0;
}

.preview-html li {
  margin-bottom: 4px;
}

.preview-html blockquote {
  border-left: 4px solid #007bff;
  padding-left: 16px;
  margin: 16px 0;
  color: #6c757d;
  font-style: italic;
}

.preview-html code {
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.preview-html pre {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
}

.preview-html pre code {
  background-color: transparent;
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-modal {
    padding: 10px;
  }
  
  .preview-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
  }
  
  .preview-header {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .preview-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .preview-title h3 {
    font-size: 16px;
  }
  
  .preview-html {
    padding: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
