import React, { useState } from 'react';
import { Cloud, Database, Users, FileText, DollarSign, Activity, CheckCircle } from 'lucide-react';
import MingdaoPricing from './MingdaoPricing';
import MingdaoUserInfo from './MingdaoUserInfo';
import MingdaoSystemStatus from './MingdaoSystemStatus';
import SimpleRegisterTest from './SimpleRegisterTest';
import { UserInfo } from '../types/document';
import './MingdaoDemo.css';

const MingdaoDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'pricing' | 'userinfo' | 'status'>('overview');

  // 模拟用户数据
  const mockUser: UserInfo = {
    id: '93235f77-767c-41ce-b4df-65a3c58ba828',
    username: 'testuser',
    email: '<EMAIL>',
    full_name: '测试用户',
    phone: '13800138000',
    user_type: 'b4e9a50e-e83a-4e1e-ac29-619572a67265', // 免费用户
    status: '0f7a48f5-9dc2-496d-96ca-c4a3b49e6d00', // 正常
    balance: 50.0,
    total_quota: 10000,
    used_quota: 2000,
    monthly_quota: 100000,
    monthly_used: 30000,
    monthly_expire_date: '2024-02-15 23:59:59',
    last_login: '2024-01-15 14:30:00',
    created_at: '2024-01-01 10:00:00',
    updated_at: '2024-01-15 14:30:00'
  };

  const tabs = [
    { id: 'overview', label: '系统概览', icon: Cloud },
    { id: 'pricing', label: '收费规则', icon: DollarSign },
    { id: 'userinfo', label: '用户信息', icon: Users },
    { id: 'status', label: '系统状态', icon: Activity },
    { id: 'test', label: '注册测试', icon: Users }
  ];

  const features = [
    {
      icon: Database,
      title: '明道云数据存储',
      description: '所有数据存储在明道云中，无需本地数据库维护',
      benefits: ['企业级安全', '自动备份', '高可用性', '易于扩展']
    },
    {
      icon: FileText,
      title: '文件管理',
      description: '利用明道云的文件存储和OnlyOffice预览功能',
      benefits: ['在线预览', '文件下载', '格式保持', '版本管理']
    },
    {
      icon: DollarSign,
      title: '灵活收费',
      description: '多种收费方式，精确的配额管理和自动扣费',
      benefits: ['按次付费', '月付套餐', '批量优惠', '余额管理']
    },
    {
      icon: Users,
      title: '用户管理',
      description: '完整的用户注册、登录、配额管理系统',
      benefits: ['JWT认证', '配额跟踪', '使用统计', '账户管理']
    }
  ];

  return (
    <div className="mingdao-demo">
      {/* 头部介绍 */}
      <div className="demo-header">
        <div className="header-content">
          <div className="header-text">
            <h1>
              <Cloud size={32} />
              明道云翻译系统
            </h1>
            <p>基于明道云的企业级翻译解决方案，无需本地数据库，开箱即用</p>
            <div className="header-badges">
              <span className="badge badge-success">
                <CheckCircle size={16} />
                企业级安全
              </span>
              <span className="badge badge-info">
                <Database size={16} />
                云端存储
              </span>
              <span className="badge badge-warning">
                <DollarSign size={16} />
                灵活收费
              </span>
            </div>
          </div>
          <div className="header-stats">
            <div className="stat-item">
              <div className="stat-number">3</div>
              <div className="stat-label">数据表</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">20+</div>
              <div className="stat-label">字段</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">4</div>
              <div className="stat-label">收费方式</div>
            </div>
          </div>
        </div>
      </div>

      {/* 功能特性 */}
      <div className="features-section">
        <h2>核心特性</h2>
        <div className="features-grid">
          {features.map((feature, index) => (
            <div key={index} className="feature-card">
              <div className="feature-icon">
                <feature.icon size={32} />
              </div>
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
              <ul className="feature-benefits">
                {feature.benefits.map((benefit, idx) => (
                  <li key={idx}>{benefit}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* 选项卡导航 */}
      <div className="demo-tabs">
        <div className="tab-nav">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id as any)}
            >
              <tab.icon size={20} />
              {tab.label}
            </button>
          ))}
        </div>

        {/* 选项卡内容 */}
        <div className="tab-content">
          {activeTab === 'overview' && (
            <div className="overview-content">
              <h3>明道云表结构</h3>
              <div className="tables-overview">
                <div className="table-card">
                  <h4>
                    <Users size={20} />
                    用户表 (users)
                  </h4>
                  <p>ID: 6886e20ba849420e13f69b23</p>
                  <div className="table-fields">
                    <span>用户名</span>
                    <span>邮箱</span>
                    <span>密码哈希</span>
                    <span>用户类型</span>
                    <span>账户余额</span>
                    <span>配额信息</span>
                    <span>+10个字段</span>
                  </div>
                </div>

                <div className="table-card">
                  <h4>
                    <FileText size={20} />
                    翻译记录表 (translations)
                  </h4>
                  <p>ID: 6886f053a849420e13f69b61</p>
                  <div className="table-fields">
                    <span>原文件</span>
                    <span>翻译文件</span>
                    <span>翻译状态</span>
                    <span>字符数</span>
                    <span>翻译进度</span>
                    <span>费用信息</span>
                    <span>+7个字段</span>
                  </div>
                </div>

                <div className="table-card">
                  <h4>
                    <DollarSign size={20} />
                    消费记录表 (consumption_records)
                  </h4>
                  <p>ID: 6886f9ffa849420e13f69bc5</p>
                  <div className="table-fields">
                    <span>消费类型</span>
                    <span>使用字符数</span>
                    <span>消费金额</span>
                    <span>扣费来源</span>
                    <span>余额变化</span>
                    <span>配额变化</span>
                    <span>+5个字段</span>
                  </div>
                </div>
              </div>

              <div className="architecture-flow">
                <h3>系统架构流程</h3>
                <div className="flow-steps">
                  <div className="flow-step">
                    <div className="step-number">1</div>
                    <div className="step-content">
                      <h4>用户注册/登录</h4>
                      <p>通过明道云用户表进行身份验证</p>
                    </div>
                  </div>
                  <div className="flow-arrow">→</div>
                  <div className="flow-step">
                    <div className="step-number">2</div>
                    <div className="step-content">
                      <h4>文件上传</h4>
                      <p>文件存储到明道云附件字段</p>
                    </div>
                  </div>
                  <div className="flow-arrow">→</div>
                  <div className="flow-step">
                    <div className="step-number">3</div>
                    <div className="step-content">
                      <h4>配额检查</h4>
                      <p>检查用户配额和余额</p>
                    </div>
                  </div>
                  <div className="flow-arrow">→</div>
                  <div className="flow-step">
                    <div className="step-number">4</div>
                    <div className="step-content">
                      <h4>翻译处理</h4>
                      <p>调用Azure翻译API</p>
                    </div>
                  </div>
                  <div className="flow-arrow">→</div>
                  <div className="flow-step">
                    <div className="step-number">5</div>
                    <div className="step-content">
                      <h4>结果存储</h4>
                      <p>翻译结果存储到明道云</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'pricing' && (
            <MingdaoPricing 
              onCalculate={(charCount, cost, paymentType) => {
                console.log('费用计算:', { charCount, cost, paymentType });
              }}
            />
          )}

          {activeTab === 'userinfo' && (
            <MingdaoUserInfo 
              user={mockUser}
              onRefresh={() => {
                console.log('刷新用户信息');
              }}
            />
          )}

          {activeTab === 'status' && (
            <MingdaoSystemStatus
              onStatusChange={(isHealthy) => {
                console.log('系统状态变化:', isHealthy);
              }}
            />
          )}

          {activeTab === 'test' && (
            <SimpleRegisterTest />
          )}
        </div>
      </div>
    </div>
  );
};

export default MingdaoDemo;
