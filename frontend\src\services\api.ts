// 翻译系统API服务
import { API_CONFIG, AUTH_CONFIG, TRANSLATION_STATUS, MINGDAO_CONFIG } from '../config/api.config';
import { mingdaoService, MingdaoDocument } from './mingdao';

// 认证相关接口
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  user: UserInfo;
}

export interface UserInfo {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  role: string;
  is_active: boolean;
  translation_quota: number;
  used_quota: number;
  remaining_quota: number;
}

// 文件上传相关接口
export interface UploadedFile {
  id: number;
  user_id: number;
  filename: string;
  original_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  status: string;
  error_message?: string;
  total_paragraphs?: number;
  total_characters?: number;
  extracted_text?: string;
  created_at: string;
  updated_at: string;
}

export interface UploadResponse {
  file: UploadedFile;
  message: string;
}

// 翻译相关接口
export interface TranslationRequest {
  file_id: number;
  source_language: string;
  target_language: string;
  include_original?: boolean;
}

export interface TranslationHistory {
  id: number;
  user_id: number;
  file_id: number;
  source_language: string;
  target_language: string;
  status: keyof typeof TRANSLATION_STATUS;
  progress: number;
  original_text?: string;
  translated_text?: string;
  total_characters: number;
  translated_characters?: number;
  confidence_score?: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  file?: UploadedFile;
}

export interface TranslationResponse {
  translation: TranslationHistory;
  message: string;
}

export interface TranslationListResponse {
  translations: TranslationHistory[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface ProgressResponse {
  translation_id: number;
  status: keyof typeof TRANSLATION_STATUS;
  progress: number;
  error_message?: string;
}

// API 响应基础接口
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// HTTP错误类
export class ApiError extends Error {
  constructor(
    public status: number,
    public message: string,
    public data?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Token管理工具类
export class TokenManager {
  static getToken(): string | null {
    return localStorage.getItem(AUTH_CONFIG.tokenKey);
  }

  static setToken(token: string): void {
    localStorage.setItem(AUTH_CONFIG.tokenKey, token);
  }

  static removeToken(): void {
    localStorage.removeItem(AUTH_CONFIG.tokenKey);
  }

  static getAuthHeaders(): Record<string, string> {
    const token = this.getToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }
}

// HTTP请求工具类
export class HttpClient {
  private static async request<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    const fullUrl = `${API_CONFIG.baseUrl}${url}`;

    const defaultHeaders = {
      'Content-Type': 'application/json',
      ...TokenManager.getAuthHeaders(),
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(fullUrl, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(response.status, errorData.detail || response.statusText, errorData);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(0, error instanceof Error ? error.message : 'Network error');
    }
  }

  static async get<T>(url: string, params?: Record<string, any>): Promise<T> {
    const searchParams = params ? new URLSearchParams(params).toString() : '';
    const fullUrl = searchParams ? `${url}?${searchParams}` : url;
    return this.request<T>(fullUrl, { method: 'GET' });
  }

  static async post<T>(url: string, data?: any): Promise<T> {
    return this.request<T>(url, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  static async put<T>(url: string, data?: any): Promise<T> {
    return this.request<T>(url, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  static async delete<T>(url: string): Promise<T> {
    return this.request<T>(url, { method: 'DELETE' });
  }

  static async uploadFile<T>(url: string, file: File, additionalData?: Record<string, any>): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    const fullUrl = `${API_CONFIG.baseUrl}${url}`;

    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: TokenManager.getAuthHeaders(),
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(response.status, errorData.detail || response.statusText, errorData);
    }

    return await response.json();
  }
}

// ==================== 认证相关API ====================

/**
 * 用户登录
 */
export const login = async (credentials: LoginRequest): Promise<AuthResponse> => {
  return HttpClient.post<AuthResponse>(API_CONFIG.endpoints.login, credentials);
};

/**
 * 用户注册
 */
export const register = async (userData: RegisterRequest): Promise<AuthResponse> => {
  return HttpClient.post<AuthResponse>(API_CONFIG.endpoints.register, userData);
};

/**
 * 获取用户信息
 */
export const getUserProfile = async (): Promise<UserInfo> => {
  return HttpClient.get<UserInfo>(API_CONFIG.endpoints.profile);
};

/**
 * 登出
 */
export const logout = (): void => {
  TokenManager.removeToken();
};

// ==================== 文件上传相关API ====================

/**
 * 上传文件
 */
export const uploadFile = async (file: File): Promise<UploadResponse> => {
  return HttpClient.uploadFile<UploadResponse>(API_CONFIG.endpoints.upload, file);
};

/**
 * 获取上传文件列表
 */
export const getUploadedFiles = async (params?: {
  page?: number;
  page_size?: number;
}): Promise<{ files: UploadedFile[]; total: number }> => {
  return HttpClient.get<{ files: UploadedFile[]; total: number }>(
    API_CONFIG.endpoints.uploadList,
    params
  );
};

/**
 * 删除上传的文件
 */
export const deleteUploadedFile = async (fileId: number): Promise<{ message: string }> => {
  return HttpClient.delete<{ message: string }>(`${API_CONFIG.endpoints.upload}/${fileId}`);
};

// ==================== 翻译相关API ====================

/**
 * 创建翻译任务
 */
export const createTranslation = async (request: TranslationRequest): Promise<TranslationResponse> => {
  return HttpClient.post<TranslationResponse>(API_CONFIG.endpoints.translate, request);
};

/**
 * 获取翻译历史列表
 */
export const getTranslationHistory = async (params?: {
  page?: number;
  page_size?: number;
  status?: string;
}): Promise<TranslationListResponse> => {
  return HttpClient.get<TranslationListResponse>(API_CONFIG.endpoints.translateList, params);
};

/**
 * 获取翻译进度
 */
export const getTranslationProgress = async (translationId: number): Promise<ProgressResponse> => {
  const url = API_CONFIG.endpoints.translateProgress.replace('{id}', String(translationId));
  return HttpClient.get<ProgressResponse>(url);
};

/**
 * 获取翻译结果
 */
export const getTranslationResult = async (translationId: number): Promise<TranslationHistory> => {
  const url = API_CONFIG.endpoints.translateResult.replace('{id}', String(translationId));
  return HttpClient.get<TranslationHistory>(url);
};

/**
 * 下载翻译文本
 */
export const downloadTranslationText = async (translationId: number): Promise<Blob> => {
  const url = API_CONFIG.endpoints.translateDownload.replace('{id}', String(translationId));
  const fullUrl = `${API_CONFIG.baseUrl}${url}`;

  const response = await fetch(fullUrl, {
    headers: TokenManager.getAuthHeaders(),
  });

  if (!response.ok) {
    throw new ApiError(response.status, 'Download failed');
  }

  return response.blob();
};

/**
 * 下载翻译Word文档
 */
export const downloadTranslationDocx = async (translationId: number): Promise<Blob> => {
  const url = API_CONFIG.endpoints.translateDownloadDocx.replace('{id}', String(translationId));
  const fullUrl = `${API_CONFIG.baseUrl}${url}`;

  const response = await fetch(fullUrl, {
    headers: TokenManager.getAuthHeaders(),
  });

  if (!response.ok) {
    throw new ApiError(response.status, 'Download failed');
  }

  return response.blob();
};

/**
 * 获取HTML预览
 */
export const getHtmlPreview = async (translationId: number): Promise<string> => {
  const url = API_CONFIG.endpoints.translatePreviewHtml.replace('{id}', String(translationId));
  const fullUrl = `${API_CONFIG.baseUrl}${url}`;

  const response = await fetch(fullUrl, {
    headers: TokenManager.getAuthHeaders(),
  });

  if (!response.ok) {
    throw new ApiError(response.status, 'Preview failed');
  }

  return response.text();
};

// ==================== 明道云集成API ====================

/**
 * 获取明道云翻译历史（用于预览和下载）
 */
export const getMingdaoTranslationHistory = async (params: {
  page?: number;
  page_size?: number;
} = {}): Promise<{
  translations: any[];
  total: number;
  total_pages: number;
}> => {
  try {
    // 从明道云获取数据
    const result = await mingdaoService.getDocuments({
      pageIndex: params.page || 1,
      pageSize: params.page_size || 20
    });

    if (!result.success) {
      throw new Error(result.message || '获取明道云数据失败');
    }

    // 解析明道云数据为前端格式
    const translations = (result.data || []).map((record: MingdaoDocument) => {
      const parsed = mingdaoService.parseDocumentRecord(record);

      return {
        id: parsed.id,
        source_language: '自动检测',
        target_language: '中文',
        status: parsed.hasTranslation ? 'COMPLETED' : 'PENDING',
        progress: parsed.hasTranslation ? 100 : 0,
        total_characters: 0,
        translated_characters: 0,
        confidence_score: null,
        cost: 0,
        error_message: null,
        terms_checked: false,
        term_corrections: parsed.remark,
        created_at: parsed.createdAt,
        updated_at: parsed.updatedAt,
        completed_at: parsed.hasTranslation ? parsed.updatedAt : null,

        // 文件信息
        file: {
          id: parsed.id,
          filename: parsed.originalFile?.name || '未知文件',
          original_filename: parsed.originalFile?.name || '未知文件',
          file_size: parsed.originalFile?.size || 0,
          content_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          file_path: parsed.originalFile?.url || '',
          preview_url: parsed.originalFile?.url || '',
          download_url: parsed.originalFile?.downloadUrl || ''
        },

        // 翻译文件信息
        translated_file: parsed.translatedFile ? {
          filename: parsed.translatedFile.name,
          url: parsed.translatedFile.url,
          download_url: parsed.translatedFile.downloadUrl,
          preview_url: parsed.translatedFile.url
        } : null,

        // 明道云特有字段
        mingdao_row_id: parsed.id,
        is_mingdao_record: true
      };
    });

    // 获取总数
    const total = await mingdaoService.getDocumentsTotal();
    const pageSize = params.page_size || 20;
    const totalPages = Math.ceil(total / pageSize);

    return {
      translations,
      total,
      total_pages: totalPages
    };
  } catch (error) {
    console.error('获取明道云翻译历史失败:', error);
    throw error;
  }
};

// ==================== 批量操作API ====================

/**
 * 批量下载翻译文档
 */
export const batchDownloadTranslations = async (
  translationIds: number[],
  format: 'text' | 'docx' = 'docx'
): Promise<Blob> => {
  // 创建批量下载请求
  const downloadPromises = translationIds.map(id => {
    if (format === 'docx') {
      return downloadTranslationDocx(id);
    } else {
      return downloadTranslationText(id);
    }
  });

  // 等待所有下载完成
  const blobs = await Promise.all(downloadPromises);

  // 如果只有一个文件，直接返回
  if (blobs.length === 1) {
    return blobs[0];
  }

  // 多个文件需要打包成ZIP（这里简化处理，实际可能需要JSZip库）
  // 暂时返回第一个文件，后续可以扩展
  return blobs[0];
};


// ==================== 工具函数 ====================

/**
 * 将文件转换为 base64
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const result = reader.result as string;
      // 移除 data:xxx;base64, 前缀，只保留 base64 字符串
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = error => reject(error);
  });
};

/**
 * 下载文件
 */
export const downloadFile = (blob: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化日期
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};
