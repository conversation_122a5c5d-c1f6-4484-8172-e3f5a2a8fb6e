"""
测试翻译结果解析
"""
import sqlite3
from app.services.docx_processor import docx_processor

def test_translation_parsing():
    """测试翻译结果解析功能"""
    print("🔍 测试翻译结果解析")
    print("=" * 50)
    
    # 从数据库获取翻译结果
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, translated_text
        FROM translation_histories
        WHERE status = 'COMPLETED'
        ORDER BY id DESC
        LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if not result:
        print("❌ 没有找到已完成的翻译记录")
        return
    
    translation_id, translated_text = result
    print(f"📝 翻译ID: {translation_id}")
    print(f"📄 翻译文本长度: {len(translated_text)} 字符")
    
    # 显示翻译文本的前几行
    print("\n📖 翻译文本预览:")
    lines = translated_text.split('\n')[:10]
    for i, line in enumerate(lines, 1):
        if line.strip():
            print(f"  {i:2d}. {line[:80]}...")
    
    # 解析翻译结果
    print("\n🔧 解析翻译结果...")
    translation_dict = docx_processor.parse_translation_result(translated_text)
    
    print(f"✅ 解析完成，共 {len(translation_dict)} 个翻译对")
    
    # 显示解析结果
    print("\n📋 解析结果示例:")
    count = 0
    for original, translated in translation_dict.items():
        if count >= 5:  # 只显示前5个
            break
        print(f"  原文: {original[:50]}...")
        print(f"  译文: {translated[:50]}...")
        print()
        count += 1
    
    # 检查重复
    print("🔍 检查重复翻译:")
    original_texts = list(translation_dict.keys())
    translated_texts = list(translation_dict.values())
    
    # 检查原文重复
    original_duplicates = []
    for i, text in enumerate(original_texts):
        if original_texts.count(text) > 1:
            original_duplicates.append(text)
    
    # 检查译文重复
    translated_duplicates = []
    for i, text in enumerate(translated_texts):
        if translated_texts.count(text) > 1:
            translated_duplicates.append(text)
    
    if original_duplicates:
        print(f"⚠️  发现 {len(set(original_duplicates))} 个重复的原文:")
        for dup in set(original_duplicates)[:3]:
            print(f"     {dup[:60]}...")
    else:
        print("✅ 没有发现重复的原文")
    
    if translated_duplicates:
        print(f"⚠️  发现 {len(set(translated_duplicates))} 个重复的译文:")
        for dup in set(translated_duplicates)[:3]:
            print(f"     {dup[:60]}...")
    else:
        print("✅ 没有发现重复的译文")
    
    conn.close()
    
    return translation_dict

if __name__ == "__main__":
    test_translation_parsing()
