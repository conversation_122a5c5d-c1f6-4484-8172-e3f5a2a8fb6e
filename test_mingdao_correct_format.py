#!/usr/bin/env python3
"""
使用正确格式测试明道云API
"""

import requests
import json
from datetime import datetime

def test_mingdao_correct_format():
    """使用您提供的正确格式测试明道云API"""
    
    print("🔍 使用正确格式测试明道云API")
    print("=" * 60)
    
    # 测试数据
    test_user_rowid = "e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa"  # 请替换为您的新测试账号rowid
    owner_id = "2499c06b-cecc-484d-ae58-16271bfa70ce"
    
    test_settings = {
        "paragraph": {
            "font_family": "微软雅黑",
            "font_size": 12,
            "bold": False,
            "italic": False,
            "underline": False,
            "text_align": "inherit",
            "color": "#000000"
        },
        "table": {
            "font_family": "宋体",
            "font_size": 8,
            "bold": True,
            "italic": False,
            "underline": False,
            "text_align": "center",
            "color": "#333333"
        },
        "header": {
            "font_family": "黑体",
            "font_size": 14,
            "bold": True,
            "italic": False,
            "underline": True,
            "text_align": "center",
            "color": "#000080"
        },
        "enable_paragraph": True,
        "enable_table": True,
        "enable_header": True
    }
    
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    settings_json = json.dumps(test_settings, ensure_ascii=False)
    
    print(f"🆔 测试用户rowid: {test_user_rowid}")
    print(f"👤 拥有者ID: {owner_id}")
    print(f"⏰ 当前时间: {current_time}")
    
    # 1. 测试查询现有记录（使用您提供的正确格式）
    print("\n1️⃣ 测试查询现有记录")
    try:
        query_url = "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows"
        query_data = {
            "appKey": "d88c1d2329c42504",
            "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
            "worksheetId": "yhfygssz",
            "pageSize": 50,
            "pageIndex": 1,
            "listType": 0,
            "controls": [],
            "filters": [
                {
                    "controlId": "6888a7c2a849420e13f69e4a",
                    "dataType": 29,
                    "spliceType": 1,
                    "filterType": 24,
                    "value": test_user_rowid
                }
            ]
        }
        
        print("📥 发送查询请求...")
        response = requests.post(
            query_url,
            json=query_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        print("查询结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        existing_record = None
        if result.get("success"):
            rows = result.get("data", {}).get("rows", [])
            print(f"✅ 查询成功，找到 {len(rows)} 条记录")
            if rows:
                existing_record = rows[0]
                print(f"现有记录ID: {existing_record.get('rowid')}")
        else:
            print(f"❌ 查询失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        existing_record = None
    
    # 2. 根据是否存在记录决定创建或更新
    if existing_record:
        print("\n2️⃣ 记录已存在，测试更新记录")
        try:
            update_url = "https://dmit.duoningbio.com/api/v2/open/worksheet/editRow"
            update_data = {
                "appKey": "d88c1d2329c42504",
                "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
                "worksheetId": "yhfygssz",
                "triggerWorkflow": True,
                "controls": [
                    {
                        "controlId": "6888a761a849420e13f69e40",
                        "value": settings_json
                    },
                    {
                        "controlId": "6888a7c2a849420e13f69e4a",
                        "value": test_user_rowid
                    },
                    {
                        "controlId": "6888a7c2a849420e13f69e4c",
                        "value": current_time
                    },
                    {
                        "controlId": "6888a7c2a849420e13f69e4d",
                        "value": current_time
                    },
                    {
                        "controlId": "ownerid",
                        "value": owner_id
                    }
                ],
                "rowId": existing_record.get('rowid')
            }
            
            print("📤 发送更新请求...")
            response = requests.post(
                update_url,
                json=update_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            result = response.json()
            print("更新结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if result.get("success"):
                print("✅ 更新记录成功！")
            else:
                print(f"❌ 更新记录失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 更新异常: {e}")
    
    else:
        print("\n2️⃣ 记录不存在，测试创建新记录")
        try:
            create_url = "https://dmit.duoningbio.com/api/v2/open/worksheet/addRow"
            create_data = {
                "appKey": "d88c1d2329c42504",
                "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
                "worksheetId": "yhfygssz",
                "triggerWorkflow": True,
                "controls": [
                    {
                        "controlId": "6888a761a849420e13f69e40",
                        "value": settings_json
                    },
                    {
                        "controlId": "6888a7c2a849420e13f69e4a",
                        "value": test_user_rowid
                    },
                    {
                        "controlId": "6888a7c2a849420e13f69e4c",
                        "value": current_time
                    },
                    {
                        "controlId": "6888a7c2a849420e13f69e4d",
                        "value": current_time
                    },
                    {
                        "controlId": "ownerid",
                        "value": owner_id
                    }
                ]
            }
            
            print("📤 发送创建请求...")
            response = requests.post(
                create_url,
                json=create_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            result = response.json()
            print("创建结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if result.get("success"):
                print("✅ 创建记录成功！")
                new_record_id = result.get("data")
                print(f"新记录ID: {new_record_id}")
            else:
                print(f"❌ 创建记录失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 创建异常: {e}")
    
    # 3. 再次查询验证
    print("\n3️⃣ 验证操作结果")
    try:
        response = requests.post(
            query_url,
            json=query_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        
        if result.get("success"):
            rows = result.get("data", {}).get("rows", [])
            print(f"✅ 验证成功，当前共有 {len(rows)} 条记录")
            
            if rows:
                for i, row in enumerate(rows):
                    print(f"\n记录 {i+1}:")
                    print(f"  记录ID: {row.get('rowid')}")
                    print(f"  用户: {row.get('6888a7c2a849420e13f69e4a')}")
                    print(f"  用户名: {row.get('6888a7c2a849420e13f69e49')}")
                    settings_data = row.get('6888a761a849420e13f69e40', '')
                    print(f"  设置JSON: {settings_data[:100]}..." if len(settings_data) > 100 else f"  设置JSON: {settings_data}")
                    print(f"  创建时间: {row.get('6888a7c2a849420e13f69e4c')}")
                    print(f"  更新时间: {row.get('6888a7c2a849420e13f69e4d')}")
        else:
            print(f"❌ 验证失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 验证异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎊 测试完成！")
    print("\n💡 请到明道云后台查看 yhfygssz 表单确认记录已保存")

if __name__ == "__main__":
    test_mingdao_correct_format()
