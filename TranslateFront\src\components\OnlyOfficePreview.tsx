import React, { useEffect, useRef } from 'react';
import { PREVIEW_CONFIG } from '../config/api.config';
// import { generateOnlyOfficeConfig } from '../utils/preview'; // 已禁用

interface OnlyOfficePreviewProps {
  rowid: string;
  fileName: string;
  onClose: () => void;
}

// 声明全局 DocsAPI
declare global {
  interface Window {
    DocsAPI: any;
  }
}

const OnlyOfficePreview: React.FC<OnlyOfficePreviewProps> = ({
  rowid,
  fileName,
  onClose
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<any>(null);

  useEffect(() => {
    // 动态加载 OnlyOffice API 脚本
    const loadOnlyOfficeAPI = () => {
      return new Promise<void>((resolve, reject) => {
        // 检查是否已经加载
        if (window.DocsAPI) {
          resolve();
          return;
        }

        const script = document.createElement('script');
        script.src = `${PREVIEW_CONFIG.onlyoffice.serverUrl}web-apps/apps/api/documents/api.js`;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load OnlyOffice API'));
        document.head.appendChild(script);
      });
    };

    const initializeEditor = async () => {
      try {
        await loadOnlyOfficeAPI();

        if (containerRef.current && window.DocsAPI) {
          // 清空容器
          containerRef.current.innerHTML = '';

          // 生成配置（OnlyOffice 已禁用）
          const config = {
            document: { title: fileName },
            height: '100%',
            width: '100%'
          };

          // 创建编辑器实例
          editorRef.current = new window.DocsAPI.DocEditor(containerRef.current, config);
        }
      } catch (error) {
        console.error('Failed to initialize OnlyOffice editor:', error);
      }
    };

    initializeEditor();

    // 清理函数
    return () => {
      if (editorRef.current && editorRef.current.destroyEditor) {
        editorRef.current.destroyEditor();
      }
    };
  }, [rowid, fileName]);

  return (
    <div className="onlyoffice-preview-modal">
      <div className="onlyoffice-preview-container">
        <div className="onlyoffice-preview-header">
          <div className="preview-title">
            <h3>{fileName}</h3>
            <span className="preview-info">OnlyOffice 预览</span>
          </div>
          <button
            className="btn btn-secondary close-btn"
            onClick={onClose}
            title="关闭预览"
          >
            ✕
          </button>
        </div>
        <div className="onlyoffice-preview-content">
          <div ref={containerRef} className="onlyoffice-editor-container" />
        </div>
      </div>
    </div>
  );
};

export default OnlyOfficePreview;
