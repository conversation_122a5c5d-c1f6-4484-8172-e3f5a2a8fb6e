"""
文件相关的 Pydantic 模型
"""
from typing import Optional, List
from pydantic import BaseModel, Field
from app.models.file import FileStatus


class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    id: int
    filename: str
    original_filename: str
    file_size: int
    mime_type: str
    status: FileStatus
    total_paragraphs: int
    total_characters: int
    created_at: str
    
    class Config:
        from_attributes = True


class FileInfo(BaseModel):
    """文件信息模型"""
    id: int
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    mime_type: str
    status: FileStatus
    total_paragraphs: int
    total_characters: int
    error_message: Optional[str]
    created_at: str
    updated_at: Optional[str]
    
    class Config:
        from_attributes = True


class FileListResponse(BaseModel):
    """文件列表响应模型"""
    files: List[FileInfo]
    total: int
    page: int
    page_size: int


class DocumentContent(BaseModel):
    """文档内容模型"""
    paragraphs: List[str]
    tables: List[List[List[str]]]  # 表格数据：[表格][行][列]
    total_characters: int
    total_paragraphs: int


class FileProcessingStatus(BaseModel):
    """文件处理状态模型"""
    id: int
    status: FileStatus
    progress: float = Field(ge=0.0, le=1.0, description="处理进度 (0.0-1.0)")
    error_message: Optional[str]
    total_paragraphs: int
    total_characters: int
