"""
FastAPI 翻译服务主应用
"""
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time

from app.config import settings
from app.utils.logger import logger
from app.db.database import engine, Base
# from app.routes import auth, upload, translate, admin, terms
from app.routes import upload, translate, admin, terms
# from app.api.mingdao_routes import router as mingdao_router
from app.api.simple_mingdao import router as simple_mingdao_router
# from app.api.user_settings import router as user_settings_router
from app.api.translation_settings import router as translation_settings_router


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于 FastAPI 的文档翻译服务",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.debug else ["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"Request: {request.method} {request.url}")
    
    response = await call_next(request)
    
    # 记录响应信息
    process_time = time.time() - start_time
    logger.info(f"Response: {response.status_code} - {process_time:.4f}s")
    
    return response


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"Global exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


# 注册路由
# app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(upload.router, prefix="/api/v1/upload", tags=["文件上传"])
app.include_router(translate.router, prefix="/api/v1/translate", tags=["翻译"])
app.include_router(terms.router, prefix="/api/v1/terms", tags=["术语管理"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["管理"])
# app.include_router(user_settings_router, prefix="/api/v1", tags=["用户设置"])
app.include_router(translation_settings_router, tags=["翻译设置"])

# 明道云API路由
# app.include_router(mingdao_router, tags=["明道云API"])
app.include_router(simple_mingdao_router, tags=["简化明道云API"])


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    
    # 创建数据库表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("Database tables created successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("Shutting down application")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else "Documentation disabled in production"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.app_version
    }
