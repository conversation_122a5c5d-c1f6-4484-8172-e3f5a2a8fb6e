.status-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid;
  animation: slideInDown 0.3s ease-out;
}

.status-message__content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.status-message__icon {
  flex-shrink: 0;
}

.status-message__text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.status-message__close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
  margin-left: 12px;
}

.status-message__close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* 成功消息 */
.status-message--success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.status-message--success .status-message__icon {
  color: #28a745;
}

/* 错误消息 */
.status-message--error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.status-message--error .status-message__icon {
  color: #dc3545;
}

/* 警告消息 */
.status-message--warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.status-message--warning .status-message__icon {
  color: #ffc107;
}

/* 信息消息 */
.status-message--info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.status-message--info .status-message__icon {
  color: #17a2b8;
}

/* 动画 */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 消息容器 */
.status-messages-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1050;
  max-width: 400px;
  width: 100%;
}

@media (max-width: 768px) {
  .status-messages-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .status-message {
    padding: 10px 12px;
  }
  
  .status-message__text {
    font-size: 13px;
  }
}
