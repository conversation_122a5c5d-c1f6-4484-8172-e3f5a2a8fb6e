import React, { useState } from 'react';

const BasicRegisterTest: React.FC = () => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [message, setMessage] = useState('');

  // 最基础的按钮点击测试
  const handleBasicClick = () => {
    console.log('🔥 基础按钮被点击了！');
    alert('基础按钮工作正常！');
    setMessage('基础按钮点击成功！');
  };

  // 表单提交处理
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🚀 表单提交被触发！');
    console.log('📝 表单数据:', { username, email, password });
    
    setMessage('表单提交成功！数据已打印到控制台');
    
    // 模拟注册过程
    setTimeout(() => {
      console.log('✅ 模拟注册完成');
      setMessage('注册完成！');
    }, 1000);
  };

  // 直接的注册按钮点击
  const handleRegisterClick = () => {
    console.log('📝 注册按钮被直接点击！');
    console.log('📋 当前表单数据:', { username, email, password });
    
    if (!username || !email || !password) {
      setMessage('请填写所有字段！');
      return;
    }
    
    setMessage('开始注册...');
    
    setTimeout(() => {
      console.log('🎉 注册成功！');
      setMessage('注册成功！用户: ' + username);
    }, 1500);
  };

  return (
    <div style={{
      padding: '20px',
      maxWidth: '500px',
      margin: '20px auto',
      border: '2px solid #007bff',
      borderRadius: '10px',
      backgroundColor: '#f8f9fa'
    }}>
      <h2 style={{ color: '#007bff', textAlign: 'center' }}>🧪 基础注册测试</h2>
      
      {/* 消息显示 */}
      {message && (
        <div style={{
          padding: '10px',
          margin: '10px 0',
          backgroundColor: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '5px',
          color: '#155724'
        }}>
          {message}
        </div>
      )}

      {/* 基础测试按钮 */}
      <div style={{ marginBottom: '20px', textAlign: 'center' }}>
        <button
          onClick={handleBasicClick}
          style={{
            padding: '15px 30px',
            fontSize: '16px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >
          🔥 点击测试基础功能
        </button>
      </div>

      <hr style={{ margin: '20px 0' }} />

      {/* 表单测试 */}
      <form onSubmit={handleFormSubmit} style={{ marginBottom: '20px' }}>
        <h3>表单提交测试</h3>
        
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            用户名:
          </label>
          <input
            type="text"
            value={username}
            onChange={(e) => {
              console.log('📝 用户名输入:', e.target.value);
              setUsername(e.target.value);
            }}
            placeholder="输入用户名"
            style={{
              width: '100%',
              padding: '10px',
              border: '2px solid #ced4da',
              borderRadius: '5px',
              fontSize: '14px'
            }}
          />
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            邮箱:
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => {
              console.log('📧 邮箱输入:', e.target.value);
              setEmail(e.target.value);
            }}
            placeholder="输入邮箱"
            style={{
              width: '100%',
              padding: '10px',
              border: '2px solid #ced4da',
              borderRadius: '5px',
              fontSize: '14px'
            }}
          />
        </div>

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            密码:
          </label>
          <input
            type="password"
            value={password}
            onChange={(e) => {
              console.log('🔒 密码输入长度:', e.target.value.length);
              setPassword(e.target.value);
            }}
            placeholder="输入密码"
            style={{
              width: '100%',
              padding: '10px',
              border: '2px solid #ced4da',
              borderRadius: '5px',
              fontSize: '14px'
            }}
          />
        </div>

        {/* 表单提交按钮 */}
        <button
          type="submit"
          style={{
            width: '100%',
            padding: '15px',
            fontSize: '16px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontWeight: 'bold',
            marginBottom: '10px'
          }}
        >
          📤 表单提交测试
        </button>
      </form>

      <hr style={{ margin: '20px 0' }} />

      {/* 直接注册按钮 */}
      <div style={{ textAlign: 'center' }}>
        <h3>直接注册测试</h3>
        <button
          type="button"
          onClick={handleRegisterClick}
          className="login-submit-btn"
          style={{
            width: '100%',
            padding: '15px',
            fontSize: '16px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >
          🚀 直接注册按钮 (login-submit-btn)
        </button>
      </div>

      {/* 调试信息 */}
      <div style={{
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#e9ecef',
        borderRadius: '5px',
        fontSize: '12px'
      }}>
        <h4>🔍 调试信息:</h4>
        <p><strong>用户名:</strong> {username || '(空)'}</p>
        <p><strong>邮箱:</strong> {email || '(空)'}</p>
        <p><strong>密码长度:</strong> {password.length}</p>
        <p><strong>时间:</strong> {new Date().toLocaleTimeString()}</p>
      </div>

      {/* 使用说明 */}
      <div style={{
        marginTop: '15px',
        padding: '15px',
        backgroundColor: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '5px',
        fontSize: '14px'
      }}>
        <h4>📋 测试步骤:</h4>
        <ol>
          <li>点击 "🔥 点击测试基础功能" 按钮</li>
          <li>填写表单字段并点击 "📤 表单提交测试"</li>
          <li>点击 "🚀 直接注册按钮" (这个有login-submit-btn类名)</li>
          <li>查看浏览器控制台的输出</li>
        </ol>
      </div>
    </div>
  );
};

export default BasicRegisterTest;
