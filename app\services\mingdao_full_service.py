"""
完全基于明道云的服务类
"""
import base64
import json
import aiohttp
import asyncio
import hashlib
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class MingdaoFullService:
    """完全基于明道云的服务类"""
    
    def __init__(self):
        self.base_url = "https://dmit.duoningbio.com/api/v2/open"
        self.app_key = "d88c1d2329c42504"
        self.sign = "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA=="
        
        # 工作表ID
        self.worksheets = {
            "users": "6886e20ba849420e13f69b23",
            "translations": "6886f053a849420e13f69b61", 
            "consumption_records": "6886f9ffa849420e13f69bc5"
        }
        
        # 用户表字段ID
        self.user_fields = {
            "username": "6886e20ba849420e13f69b24",
            "email": "6886e4c8a849420e13f69b30",
            "password_hash": "6886e4c8a849420e13f69b31",
            "full_name": "6886e4c8a849420e13f69b32",
            "phone": "6886e4c8a849420e13f69b33",
            "user_type": "6886e4c8a849420e13f69b34",
            "status": "6886e4c8a849420e13f69b35",
            "balance": "6886e4c8a849420e13f69b36",
            "total_quota": "6886e4c8a849420e13f69b37",
            "used_quota": "6886e4c8a849420e13f69b38",
            "monthly_quota": "6886e4c8a849420e13f69b39",
            "monthly_used": "6886e4c8a849420e13f69b3a",
            "monthly_expire_date": "6886e4c8a849420e13f69b3b",
            "last_login": "6886e4c8a849420e13f69b3c",
            "created_at": "6886e4c8a849420e13f69b3d",
            "updated_at": "6886e4c8a849420e13f69b3e"
        }
        
        # 翻译记录表字段ID
        self.translation_fields = {
            "original_file": "6886f7a4a849420e13f69b6f",
            "translated_file": "6886f7a4a849420e13f69b70",
            "status": "6886f7a4a849420e13f69b71",
            "total_characters": "6886f7a4a849420e13f69b72",
            "progress": "6886f7a4a849420e13f69b73",
            "cost": "6886f7a4a849420e13f69b74",
            "payment_type": "6886f7a4a849420e13f69b75",
            "confidence_score": "6886f7a4a849420e13f69b76",
            "error_message": "6886f7a4a849420e13f69b77",
            "remark": "6886f7a4a849420e13f69b78",
            "created_at": "6886f7a4a849420e13f69b79",
            "updated_at": "6886f7a4a849420e13f69b7a",
            "completed_at": "6886f7a4a849420e13f69b7b",
            "user_id": "6886f887a849420e13f69b8f"
        }
        
        # 消费记录表字段ID
        self.consumption_fields = {
            "consumption_id": "6886fbeda849420e13f69bd2",
            "user_id": "6886fbeda849420e13f69bd3",
            "translation_id": "6886fbeda849420e13f69bd5",
            "consumption_type": "6886fbeda849420e13f69bd7",
            "characters_used": "6886fbeda849420e13f69bd8",
            "amount": "6886fbeda849420e13f69bd9",
            "payment_source": "6886fbeda849420e13f69bda",
            "before_balance": "6886fbeda849420e13f69bdb",
            "after_balance": "6886fbeda849420e13f69bdc",
            "before_quota": "6886fbeda849420e13f69bdd",
            "after_quota": "6886fbeda849420e13f69bde",
            "remark": "6886fbeda849420e13f69bdf"
        }
        
        # 收费规则配置
        self.pricing_rules = {
            "monthly": {"price": 20, "quota": 100000, "duration_days": 30},
            "per_translation_small": {"min_chars": 1, "max_chars": 5000, "price": 5},
            "per_translation_large": {"min_chars": 5001, "price_per_5k": 5},
            "bulk_discount": {"min_chars": 300000, "price": 100}
        }
    
    async def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送请求到明道云API"""
        url = f"{self.base_url}{endpoint}"
        
        request_data = {
            "appKey": self.app_key,
            "sign": self.sign,
            **data
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    json=request_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    logger.info(f"明道云API响应状态: {response.status}")
                    logger.info(f"明道云API响应头: {response.headers}")

                    # 先获取响应文本
                    response_text = await response.text()
                    logger.info(f"明道云API响应内容: {response_text[:500]}...")

                    if response.status != 200:
                        logger.error(f"明道云API请求失败: {response.status}, {response_text}")
                        raise Exception(f"API请求失败: HTTP {response.status}")

                    # 尝试解析JSON
                    try:
                        result = json.loads(response_text) if response_text else {}
                        return result
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {e}, 响应内容: {response_text}")
                        # 如果不是JSON，尝试返回文本内容
                        return {"success": False, "error": "响应不是有效的JSON格式", "raw_response": response_text}

        except Exception as e:
            logger.error(f"明道云API请求异常: {e}")
            raise
    
    def _file_to_base64(self, file_path: str) -> str:
        """将文件转换为base64编码"""
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
                return base64.b64encode(file_content).decode('utf-8')
        except Exception as e:
            logger.error(f"文件转base64失败: {e}")
            raise
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _calculate_translation_cost(self, char_count: int, user_type: str) -> Dict[str, Any]:
        """计算翻译费用"""
        if char_count <= 5000:
            return {
                "cost": self.pricing_rules["per_translation_small"]["price"],
                "payment_type": "按次付费",
                "description": f"{char_count}字符，按次收费"
            }
        elif char_count >= 300000:
            return {
                "cost": self.pricing_rules["bulk_discount"]["price"],
                "payment_type": "批量优惠",
                "description": f"{char_count}字符，批量优惠"
            }
        else:
            # 超过5000字，每5000字5元
            cost = ((char_count - 1) // 5000 + 1) * self.pricing_rules["per_translation_large"]["price_per_5k"]
            return {
                "cost": cost,
                "payment_type": "按次付费",
                "description": f"{char_count}字符，分段收费"
            }

    # ==================== 用户管理 ====================

    async def create_user(self, username: str, email: str, password: str, full_name: str = "") -> Dict[str, Any]:
        """创建用户"""
        try:
            password_hash = self._hash_password(password)
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            controls = [
                {"controlId": self.user_fields["username"], "value": username},
                {"controlId": self.user_fields["email"], "value": email},
                {"controlId": self.user_fields["password_hash"], "value": password_hash},
                {"controlId": self.user_fields["full_name"], "value": full_name},
                {"controlId": self.user_fields["user_type"], "value": "b4e9a50e-e83a-4e1e-ac29-619572a67265"},  # 免费用户
                {"controlId": self.user_fields["status"], "value": "0f7a48f5-9dc2-496d-96ca-c4a3b49e6d00"},  # 正常
                {"controlId": self.user_fields["balance"], "value": 0},
                {"controlId": self.user_fields["total_quota"], "value": 1000},  # 免费1000字符
                {"controlId": self.user_fields["used_quota"], "value": 0},
                {"controlId": self.user_fields["monthly_quota"], "value": 0},
                {"controlId": self.user_fields["monthly_used"], "value": 0},
                {"controlId": self.user_fields["created_at"], "value": current_time},
                {"controlId": self.user_fields["updated_at"], "value": current_time}
            ]

            data = {
                "worksheetId": self.worksheets["users"],
                "triggerWorkflow": True,
                "controls": controls
            }

            result = await self._make_request("/worksheet/addRow", data)

            if result.get("success"):
                logger.info(f"用户创建成功: {username}")
                return {"success": True, "user_id": result.get("data"), "message": "用户创建成功"}
            else:
                raise Exception(f"创建用户失败: {result.get('message', '未知错误')}")

        except Exception as e:
            logger.error(f"创建用户异常: {e}")
            raise

    async def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        """用户认证"""
        try:
            password_hash = self._hash_password(password)

            # 查询用户
            filters = [
                {
                    "controlId": self.user_fields["username"],
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 1,  # 等于
                    "value": username
                }
            ]

            data = {
                "worksheetId": self.worksheets["users"],
                "pageSize": 1,
                "pageIndex": 1,
                "listType": 0,
                "controls": [],
                "filters": filters
            }

            result = await self._make_request("/worksheet/getFilterRows", data)

            if result.get("success") and result.get("data", {}).get("rows"):
                user_row = result["data"]["rows"][0]
                stored_hash = user_row.get(self.user_fields["password_hash"])

                if stored_hash == password_hash:
                    # 更新最后登录时间
                    await self._update_user_last_login(user_row["rowid"])

                    return {
                        "success": True,
                        "user": self._parse_user_data(user_row),
                        "message": "登录成功"
                    }
                else:
                    return {"success": False, "message": "密码错误"}
            else:
                return {"success": False, "message": "用户不存在"}

        except Exception as e:
            logger.error(f"用户认证异常: {e}")
            raise

    async def _update_user_last_login(self, user_id: str):
        """更新用户最后登录时间"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            controls = [
                {"controlId": self.user_fields["last_login"], "value": current_time},
                {"controlId": self.user_fields["updated_at"], "value": current_time}
            ]

            data = {
                "worksheetId": self.worksheets["users"],
                "triggerWorkflow": False,
                "controls": controls,
                "rowId": user_id
            }

            await self._make_request("/worksheet/editRow", data)

        except Exception as e:
            logger.error(f"更新最后登录时间失败: {e}")

    def _parse_user_data(self, user_row: Dict) -> Dict[str, Any]:
        """解析用户数据"""
        return {
            "id": user_row["rowid"],
            "username": user_row.get(self.user_fields["username"], ""),
            "email": user_row.get(self.user_fields["email"], ""),
            "full_name": user_row.get(self.user_fields["full_name"], ""),
            "phone": user_row.get(self.user_fields["phone"], ""),
            "user_type": user_row.get(self.user_fields["user_type"], ""),
            "status": user_row.get(self.user_fields["status"], ""),
            "balance": float(user_row.get(self.user_fields["balance"], 0)),
            "total_quota": int(user_row.get(self.user_fields["total_quota"], 0)),
            "used_quota": int(user_row.get(self.user_fields["used_quota"], 0)),
            "monthly_quota": int(user_row.get(self.user_fields["monthly_quota"], 0)),
            "monthly_used": int(user_row.get(self.user_fields["monthly_used"], 0)),
            "monthly_expire_date": user_row.get(self.user_fields["monthly_expire_date"], ""),
            "last_login": user_row.get(self.user_fields["last_login"], ""),
            "created_at": user_row.get(self.user_fields["created_at"], ""),
            "updated_at": user_row.get(self.user_fields["updated_at"], "")
        }

    # ==================== 翻译管理 ====================

    async def create_translation(self, user_id: str, file_path: str, filename: str, char_count: int) -> Dict[str, Any]:
        """创建翻译记录"""
        try:
            # 获取用户信息
            user = await self.get_user_by_id(user_id)
            if not user:
                raise Exception("用户不存在")

            # 计算费用
            cost_info = self._calculate_translation_cost(char_count, user["user_type"])

            # 检查用户配额和余额
            can_translate, message = await self._check_user_quota(user, char_count, cost_info["cost"])
            if not can_translate:
                raise Exception(message)

            # 上传原文件
            base64_content = self._file_to_base64(file_path)
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            controls = [
                {
                    "controlId": self.translation_fields["original_file"],
                    "valueType": 2,
                    "controlFiles": [{"baseFile": base64_content, "fileName": filename}]
                },
                {"controlId": self.translation_fields["status"], "value": "1784937f-c546-43f5-9d78-02b326a72bde"},  # 待处理
                {"controlId": self.translation_fields["total_characters"], "value": char_count},
                {"controlId": self.translation_fields["progress"], "value": 0},
                {"controlId": self.translation_fields["cost"], "value": cost_info["cost"]},
                {"controlId": self.translation_fields["payment_type"], "value": cost_info["payment_type"]},
                {"controlId": self.translation_fields["remark"], "value": cost_info["description"]},
                {"controlId": self.translation_fields["created_at"], "value": current_time},
                {"controlId": self.translation_fields["updated_at"], "value": current_time},
                {"controlId": self.translation_fields["user_id"], "value": user_id}
            ]

            data = {
                "worksheetId": self.worksheets["translations"],
                "triggerWorkflow": True,
                "controls": controls
            }

            result = await self._make_request("/worksheet/addRow", data)

            if result.get("success"):
                translation_id = result.get("data")
                logger.info(f"翻译记录创建成功: {translation_id}")

                # 扣除用户配额/余额
                await self._deduct_user_quota(user, char_count, cost_info["cost"], translation_id)

                return {
                    "success": True,
                    "translation_id": translation_id,
                    "cost": cost_info["cost"],
                    "message": "翻译任务创建成功"
                }
            else:
                raise Exception(f"创建翻译记录失败: {result.get('message', '未知错误')}")

        except Exception as e:
            logger.error(f"创建翻译记录异常: {e}")
            raise

    async def update_translation_progress(self, translation_id: str, progress: int, status: str = None):
        """更新翻译进度"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            controls = [
                {"controlId": self.translation_fields["progress"], "value": progress},
                {"controlId": self.translation_fields["updated_at"], "value": current_time}
            ]

            if status:
                # 状态映射
                status_map = {
                    "PENDING": "1784937f-c546-43f5-9d78-02b326a72bde",  # 待处理
                    "PROCESSING": "d9a70a50-3369-4e62-96c8-4e183996368c",  # 翻译中
                    "COMPLETED": "7c60c3c6-c56e-4ab8-8979-278f1b359e80",  # 已完成
                    "FAILED": "80fa2f81-3381-4228-b0a6-0ec3517fe205",  # 失败
                    "CANCELLED": "99b06107-282a-437e-8625-7a60c921cf3f"  # 已取消
                }
                if status in status_map:
                    controls.append({"controlId": self.translation_fields["status"], "value": status_map[status]})

            if progress == 100 and status == "COMPLETED":
                controls.append({"controlId": self.translation_fields["completed_at"], "value": current_time})

            data = {
                "worksheetId": self.worksheets["translations"],
                "triggerWorkflow": False,
                "controls": controls,
                "rowId": translation_id
            }

            await self._make_request("/worksheet/editRow", data)
            logger.info(f"翻译进度更新成功: {translation_id}, 进度: {progress}%")

        except Exception as e:
            logger.error(f"更新翻译进度异常: {e}")
            raise

    async def upload_translated_file(self, translation_id: str, file_path: str, filename: str):
        """上传翻译后的文件"""
        try:
            base64_content = self._file_to_base64(file_path)
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            controls = [
                {
                    "controlId": self.translation_fields["translated_file"],
                    "valueType": 2,
                    "controlFiles": [{"baseFile": base64_content, "fileName": filename}]
                },
                {"controlId": self.translation_fields["status"], "value": "7c60c3c6-c56e-4ab8-8979-278f1b359e80"},  # 已完成
                {"controlId": self.translation_fields["progress"], "value": 100},
                {"controlId": self.translation_fields["completed_at"], "value": current_time},
                {"controlId": self.translation_fields["updated_at"], "value": current_time}
            ]

            data = {
                "worksheetId": self.worksheets["translations"],
                "triggerWorkflow": True,
                "controls": controls,
                "rowId": translation_id
            }

            result = await self._make_request("/worksheet/editRow", data)

            if result.get("success"):
                logger.info(f"翻译文件上传成功: {translation_id}")
                return {"success": True, "message": "翻译文件上传成功"}
            else:
                raise Exception(f"上传翻译文件失败: {result.get('message', '未知错误')}")

        except Exception as e:
            logger.error(f"上传翻译文件异常: {e}")
            raise

    # ==================== 配额管理 ====================

    async def _check_user_quota(self, user: Dict, char_count: int, cost: float) -> Tuple[bool, str]:
        """检查用户配额"""
        try:
            # 检查月付配额
            if user["monthly_quota"] > 0:
                remaining_monthly = user["monthly_quota"] - user["monthly_used"]
                if remaining_monthly >= char_count:
                    return True, "使用月付配额"

            # 检查总配额（免费额度）
            remaining_total = user["total_quota"] - user["used_quota"]
            if remaining_total >= char_count:
                return True, "使用免费配额"

            # 检查余额
            if user["balance"] >= cost:
                return True, "使用余额付费"

            return False, f"配额不足。需要 {char_count} 字符，费用 {cost} 元"

        except Exception as e:
            logger.error(f"检查用户配额异常: {e}")
            return False, "配额检查失败"

    async def _deduct_user_quota(self, user: Dict, char_count: int, cost: float, translation_id: str):
        """扣除用户配额"""
        try:
            before_balance = user["balance"]
            before_quota = user["total_quota"] - user["used_quota"]
            before_monthly = user["monthly_quota"] - user["monthly_used"]

            payment_source = ""
            new_balance = before_balance
            new_used_quota = user["used_quota"]
            new_monthly_used = user["monthly_used"]

            # 优先使用月付配额
            if user["monthly_quota"] > 0 and before_monthly >= char_count:
                new_monthly_used += char_count
                payment_source = "月付配额"
            # 其次使用免费配额
            elif before_quota >= char_count:
                new_used_quota += char_count
                payment_source = "免费额度"
            # 最后使用余额
            else:
                new_balance -= cost
                payment_source = "余额扣费"

            # 更新用户信息
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            controls = [
                {"controlId": self.user_fields["balance"], "value": new_balance},
                {"controlId": self.user_fields["used_quota"], "value": new_used_quota},
                {"controlId": self.user_fields["monthly_used"], "value": new_monthly_used},
                {"controlId": self.user_fields["updated_at"], "value": current_time}
            ]

            data = {
                "worksheetId": self.worksheets["users"],
                "triggerWorkflow": False,
                "controls": controls,
                "rowId": user["id"]
            }

            await self._make_request("/worksheet/editRow", data)

            # 记录消费记录
            await self._create_consumption_record(
                user["id"], translation_id, char_count, cost, payment_source,
                before_balance, new_balance, before_quota, before_quota - char_count if payment_source == "免费额度" else before_quota
            )

            logger.info(f"用户配额扣除成功: {user['id']}, 字符数: {char_count}, 费用: {cost}")

        except Exception as e:
            logger.error(f"扣除用户配额异常: {e}")
            raise

    async def _create_consumption_record(self, user_id: str, translation_id: str, char_count: int,
                                       cost: float, payment_source: str, before_balance: float,
                                       after_balance: float, before_quota: int, after_quota: int):
        """创建消费记录"""
        try:
            controls = [
                {"controlId": self.consumption_fields["user_id"], "value": user_id},
                {"controlId": self.consumption_fields["translation_id"], "value": translation_id},
                {"controlId": self.consumption_fields["consumption_type"], "value": "49c31558-8462-447a-91e5-e170f264943e"},  # 翻译扣费
                {"controlId": self.consumption_fields["characters_used"], "value": char_count},
                {"controlId": self.consumption_fields["amount"], "value": cost},
                {"controlId": self.consumption_fields["payment_source"], "value": self._get_payment_source_id(payment_source)},
                {"controlId": self.consumption_fields["before_balance"], "value": before_balance},
                {"controlId": self.consumption_fields["after_balance"], "value": after_balance},
                {"controlId": self.consumption_fields["before_quota"], "value": before_quota},
                {"controlId": self.consumption_fields["after_quota"], "value": after_quota},
                {"controlId": self.consumption_fields["remark"], "value": f"翻译 {char_count} 字符，费用 {cost} 元"}
            ]

            data = {
                "worksheetId": self.worksheets["consumption_records"],
                "triggerWorkflow": True,
                "controls": controls
            }

            await self._make_request("/worksheet/addRow", data)
            logger.info(f"消费记录创建成功: 用户 {user_id}, 翻译 {translation_id}")

        except Exception as e:
            logger.error(f"创建消费记录异常: {e}")

    def _get_payment_source_id(self, payment_source: str) -> str:
        """获取付费来源ID"""
        source_map = {
            "月付配额": "010c3565-a43b-43bd-8115-25e7d47728f2",
            "按次付费": "9637b246-2076-4413-8dd8-97a7bd693838",
            "余额扣费": "e28a392d-1b9d-4f4d-9c49-0e1c37b60394",
            "免费额度": "a824f3a7-51a5-4ddf-ae67-08233fd9066a"
        }
        return source_map.get(payment_source, source_map["免费额度"])

    # ==================== 查询功能 ====================

    async def get_record_detail(self, row_id: str, worksheet_id: str = None) -> Optional[Dict[str, Any]]:
        """获取记录详情"""
        try:
            if not worksheet_id:
                worksheet_id = self.worksheets["translations"]

            data = {
                "worksheetId": worksheet_id,
                "rowId": row_id,
                "getSystemControl": True  # 获取系统字段
            }

            result = await self._make_request("/worksheet/getRowByIdPost", data)

            if result.get("success") and result.get("data"):
                return result["data"]
            else:
                logger.warning(f"获取记录详情失败: {result}")
                return None

        except Exception as e:
            logger.error(f"获取记录详情异常: {e}")
            return None

    async def download_file(self, file_url: str) -> Optional[bytes]:
        """下载文件内容"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(file_url) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        logger.error(f"下载文件失败: HTTP {response.status}")
                        return None
        except Exception as e:
            logger.error(f"下载文件异常: {e}")
            return None

    async def upload_text_as_file(self, text_content: str, filename: str) -> Dict[str, Any]:
        """将文本内容作为文件上传到明道云"""
        try:
            # 将文本转换为base64
            text_bytes = text_content.encode('utf-8')
            base64_content = base64.b64encode(text_bytes).decode('utf-8')

            # 构建文件上传数据
            file_data = {
                "baseFile": base64_content,
                "fileName": filename
            }

            # 这里返回文件信息，实际上传会在更新记录时进行
            return {
                "originalFilename": filename,
                "size": len(text_bytes),
                "baseFile": base64_content
            }

        except Exception as e:
            logger.error(f"文本文件处理异常: {e}")
            raise

    async def upload_docx_file(self, docx_content: bytes, filename: str) -> Dict[str, Any]:
        """将Word文档内容作为文件上传到明道云"""
        try:
            # 将Word文档bytes转换为base64
            base64_content = base64.b64encode(docx_content).decode('utf-8')

            # 构建文件上传数据
            file_data = {
                "baseFile": base64_content,
                "fileName": filename
            }

            # 这里返回文件信息，实际上传会在更新记录时进行
            return {
                "originalFilename": filename,
                "size": len(docx_content),
                "baseFile": base64_content
            }

        except Exception as e:
            logger.error(f"Word文档文件处理异常: {e}")
            raise

    async def update_record(self, row_id: str, field_updates: Dict[str, Any], worksheet_id: str = None) -> bool:
        """更新记录"""
        try:
            if not worksheet_id:
                worksheet_id = self.worksheets["translations"]

            # 构建控件数据
            controls = []
            for field_id, value in field_updates.items():
                control = {"controlId": field_id, "value": value}

                # 如果是文件字段且包含baseFile，设置为文件流模式
                if isinstance(value, str) and value.startswith('[') and 'baseFile' in value:
                    try:
                        file_data = json.loads(value)
                        if isinstance(file_data, list) and len(file_data) > 0:
                            file_info = file_data[0]
                            if 'baseFile' in file_info:
                                control["valueType"] = 2  # 文件流模式
                                control["controlFiles"] = [{
                                    "baseFile": file_info["baseFile"],
                                    "fileName": file_info["originalFilename"]
                                }]
                                control["value"] = ""  # 文件流模式时value为空
                    except:
                        pass  # 如果解析失败，使用默认方式

                controls.append(control)

            data = {
                "worksheetId": worksheet_id,
                "rowId": row_id,
                "controls": controls
            }

            result = await self._make_request("/worksheet/editRow", data)

            if result.get("success"):
                logger.info(f"记录更新成功: {row_id}")
                return True
            else:
                logger.error(f"记录更新失败: {result}")
                return False

        except Exception as e:
            logger.error(f"更新记录异常: {e}")
            return False

    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取用户"""
        try:
            filters = [
                {
                    "controlId": "rowid",
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 1,
                    "value": user_id
                }
            ]

            data = {
                "worksheetId": self.worksheets["users"],
                "pageSize": 1,
                "pageIndex": 1,
                "listType": 0,
                "controls": [],
                "filters": filters
            }

            result = await self._make_request("/worksheet/getFilterRows", data)

            if result.get("success") and result.get("data", {}).get("rows"):
                return self._parse_user_data(result["data"]["rows"][0])

            return None

        except Exception as e:
            logger.error(f"获取用户信息异常: {e}")
            return None

    async def get_translation_history(self, user_id: str = None, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取翻译历史"""
        try:
            filters = []
            if user_id:
                filters.append({
                    "controlId": self.translation_fields["user_id"],
                    "dataType": 29,
                    "spliceType": 1,
                    "filterType": 1,
                    "value": user_id
                })

            data = {
                "worksheetId": self.worksheets["translations"],
                "pageSize": page_size,
                "pageIndex": page,
                "listType": 0,
                "controls": [],
                "filters": filters,
                "sortId": self.translation_fields["created_at"],
                "isAsc": False
            }

            result = await self._make_request("/worksheet/getFilterRows", data)

            if result.get("success"):
                rows = result.get("data", {}).get("rows", [])
                translations = [self._parse_translation_data(row) for row in rows]

                # 获取总数
                total = await self._get_translation_total(user_id)
                total_pages = (total + page_size - 1) // page_size

                return {
                    "translations": translations,
                    "total": total,
                    "total_pages": total_pages,
                    "current_page": page
                }

            return {"translations": [], "total": 0, "total_pages": 0, "current_page": page}

        except Exception as e:
            logger.error(f"获取翻译历史异常: {e}")
            raise

    async def _get_translation_total(self, user_id: str = None) -> int:
        """获取翻译记录总数"""
        try:
            filters = []
            if user_id:
                filters.append({
                    "controlId": self.translation_fields["user_id"],
                    "dataType": 29,
                    "spliceType": 1,
                    "filterType": 1,
                    "value": user_id
                })

            data = {
                "worksheetId": self.worksheets["translations"],
                "pageSize": 1,
                "pageIndex": 1,
                "listType": 0,
                "controls": [],
                "filters": filters
            }

            result = await self._make_request("/worksheet/getFilterRows", data)

            if result.get("success"):
                return result.get("data", {}).get("total", 0)

            return 0

        except Exception as e:
            logger.error(f"获取翻译总数异常: {e}")
            return 0

    def _parse_translation_data(self, translation_row: Dict) -> Dict[str, Any]:
        """解析翻译数据"""
        # 解析文件信息
        original_file = None
        original_file_data = translation_row.get(self.translation_fields["original_file"])
        if original_file_data and isinstance(original_file_data, list) and len(original_file_data) > 0:
            file_info = original_file_data[0]
            original_file = {
                "filename": file_info.get("originalFilename", file_info.get("name", "")),
                "url": file_info.get("previewUrl", file_info.get("url", "")),
                "download_url": file_info.get("downloadUrl", file_info.get("url", "")),
                "size": file_info.get("size", 0)
            }

        translated_file = None
        translated_file_data = translation_row.get(self.translation_fields["translated_file"])
        if translated_file_data and isinstance(translated_file_data, list) and len(translated_file_data) > 0:
            file_info = translated_file_data[0]
            translated_file = {
                "filename": file_info.get("originalFilename", file_info.get("name", "")),
                "url": file_info.get("previewUrl", file_info.get("url", "")),
                "download_url": file_info.get("downloadUrl", file_info.get("url", "")),
                "size": file_info.get("size", 0)
            }

        # 状态映射
        status_map = {
            "1784937f-c546-43f5-9d78-02b326a72bde": "PENDING",
            "d9a70a50-3369-4e62-96c8-4e183996368c": "PROCESSING",
            "7c60c3c6-c56e-4ab8-8979-278f1b359e80": "COMPLETED",
            "80fa2f81-3381-4228-b0a6-0ec3517fe205": "FAILED",
            "99b06107-282a-437e-8625-7a60c921cf3f": "CANCELLED"
        }

        status_value = translation_row.get(self.translation_fields["status"], "")
        status = status_map.get(status_value, "PENDING")

        return {
            "id": translation_row["rowid"],
            "source_language": "zh",  # 固定中文
            "target_language": "en",  # 固定英文
            "status": status,
            "progress": int(translation_row.get(self.translation_fields["progress"], 0)),
            "total_characters": int(translation_row.get(self.translation_fields["total_characters"], 0)),
            "cost": float(translation_row.get(self.translation_fields["cost"], 0)),
            "payment_type": translation_row.get(self.translation_fields["payment_type"], ""),
            "confidence_score": translation_row.get(self.translation_fields["confidence_score"]),
            "error_message": translation_row.get(self.translation_fields["error_message"], ""),
            "remark": translation_row.get(self.translation_fields["remark"], ""),
            "created_at": translation_row.get(self.translation_fields["created_at"], ""),
            "updated_at": translation_row.get(self.translation_fields["updated_at"], ""),
            "completed_at": translation_row.get(self.translation_fields["completed_at"], ""),
            "file": original_file,
            "translated_file": translated_file,
            "mingdao_row_id": translation_row["rowid"],
            "is_mingdao_record": True
        }

# 创建全局实例
mingdao_full_service = MingdaoFullService()
