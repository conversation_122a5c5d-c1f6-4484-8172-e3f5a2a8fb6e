#!/bin/bash

echo "🔧 修复依赖和路径问题"
echo "===================="

# 1. 停止当前服务
echo "1. ⏹️  停止当前服务..."
pm2 delete translate-front 2>/dev/null || echo "服务已停止"

# 2. 检查当前目录
echo "2. 📍 检查当前目录..."
echo "当前路径: $(pwd)"
ls -la

# 3. 检查是否在正确的项目目录
if [ ! -f "package.json" ]; then
    echo "❌ 未找到 package.json，请确保在正确的项目目录中"
    echo "尝试查找项目目录..."
    find /root -name "package.json" -path "*/TranslateFront/*" 2>/dev/null | head -5
    exit 1
fi

# 4. 清理 node_modules 和重新安装
echo "3. 🧹 清理并重新安装依赖..."
rm -rf node_modules package-lock.json
npm cache clean --force 2>/dev/null || echo "缓存清理完成"

# 5. 安装依赖
echo "4. 📦 安装依赖..."
npm install

# 6. 检查关键依赖是否安装成功
echo "5. ✅ 检查关键依赖..."
if [ -d "node_modules/express" ]; then
    echo "✅ express 已安装"
else
    echo "❌ express 未安装，手动安装..."
    npm install express
fi

if [ -d "node_modules/http-proxy-middleware" ]; then
    echo "✅ http-proxy-middleware 已安装"
else
    echo "❌ http-proxy-middleware 未安装，手动安装..."
    npm install http-proxy-middleware
fi

# 7. 检查服务器文件
echo "6. 📄 检查服务器文件..."
if [ -f "server.cjs" ]; then
    echo "✅ server.cjs 存在"
    echo "检查文件内容..."
    head -10 server.cjs
else
    echo "❌ server.cjs 不存在"
    exit 1
fi

# 8. 测试服务器文件语法
echo "7. 🧪 测试服务器文件语法..."
node -c server.cjs
if [ $? -eq 0 ]; then
    echo "✅ server.cjs 语法正确"
else
    echo "❌ server.cjs 语法错误"
    exit 1
fi

# 9. 构建项目
echo "8. 🔨 构建项目..."
npm run build

# 10. 创建日志目录
echo "9. 📁 创建日志目录..."
mkdir -p logs

# 11. 检查 PM2 配置文件
echo "10. ⚙️  检查 PM2 配置..."
if [ -f "ecosystem.config.cjs" ]; then
    echo "✅ ecosystem.config.cjs 存在"
    # 修复配置文件中的路径
    sed -i "s|cwd: './.*'|cwd: '$(pwd)'|g" ecosystem.config.cjs 2>/dev/null || echo "路径已正确"
else
    echo "📝 创建 PM2 配置文件..."
    cat > ecosystem.config.cjs << EOF
module.exports = {
  apps: [{
    name: 'translate-front',
    script: 'server.cjs',
    cwd: '$(pwd)',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    min_uptime: '10s',
    max_restarts: 10,
    kill_timeout: 5000,
    listen_timeout: 3000,
    health_check_grace_period: 3000
  }]
};
EOF
    echo "✅ PM2 配置文件已创建"
fi

# 12. 手动测试服务器启动
echo "11. 🧪 手动测试服务器启动..."
timeout 10s node server.cjs &
TEST_PID=$!
sleep 3

# 检查进程是否还在运行
if kill -0 $TEST_PID 2>/dev/null; then
    echo "✅ 服务器可以正常启动"
    kill $TEST_PID 2>/dev/null
else
    echo "❌ 服务器启动失败"
    echo "尝试直接运行查看错误:"
    node server.cjs
    exit 1
fi

# 13. 使用 PM2 启动服务
echo "12. 🚀 使用 PM2 启动服务..."
pm2 start ecosystem.config.cjs

# 14. 等待服务启动
echo "13. ⏳ 等待服务启动..."
sleep 5

# 15. 检查服务状态
echo "14. 📊 检查服务状态..."
pm2 status

# 16. 检查端口监听
echo "15. 🔌 检查端口监听..."
if command -v ss &> /dev/null; then
    ss -tlnp | grep :3000 && echo "✅ 端口 3000 正在监听" || echo "❌ 端口 3000 未在监听"
elif command -v netstat &> /dev/null; then
    netstat -tlnp | grep :3000 && echo "✅ 端口 3000 正在监听" || echo "❌ 端口 3000 未在监听"
else
    echo "安装网络工具..."
    yum install -y net-tools 2>/dev/null || apt-get install -y net-tools 2>/dev/null || echo "请手动安装 net-tools"
fi

# 17. 测试 HTTP 响应
echo "16. 🌐 测试 HTTP 响应..."
sleep 2
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null || echo "000")
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ HTTP 服务正常 (状态码: $HTTP_CODE)"
elif [ "$HTTP_CODE" = "000" ]; then
    echo "❌ 无法连接到服务"
else
    echo "⚠️  HTTP 响应异常 (状态码: $HTTP_CODE)"
fi

# 18. 显示日志（如果有错误）
if [ "$HTTP_CODE" != "200" ]; then
    echo "17. 📝 查看错误日志..."
    pm2 logs translate-front --err --lines 5
fi

# 19. 保存配置
echo "18. 💾 保存 PM2 配置..."
pm2 save

echo ""
echo "🎉 修复完成！"
echo "=============="
echo ""
echo "📊 最终状态:"
pm2 list
echo ""
echo "🌐 访问地址:"
echo "   内网: http://$(hostname -I | awk '{print $1}'):3000"
echo "   外网: http://$(curl -s ifconfig.me 2>/dev/null || echo '无法获取'):3000"
echo ""
echo "📝 如果仍有问题，请查看日志:"
echo "   pm2 logs translate-front"
