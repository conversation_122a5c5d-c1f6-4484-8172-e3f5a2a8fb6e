"""
明道云API集成服务
"""
import base64
import json
import aiohttp
import asyncio
from typing import Optional, Dict, Any, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class MingdaoService:
    """明道云API服务类"""
    
    def __init__(self):
        self.base_url = "https://dmit.duoningbio.com"
        self.app_key = "d88c1d2329c42504"
        self.sign = "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA=="
        self.worksheet_id = "fywd"
        
        # 控制项ID
        self.control_ids = {
            "original_file": "YWJ",      # 原文件
            "translated_file": "FYWJ",   # 翻译文件
            "remark": "68789e5fa849420e13f65dcf",  # 备注
            "owner": "ownerid"           # 拥有者
        }
    
    async def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送请求到明道云API"""
        url = f"{self.base_url}{endpoint}"
        
        # 添加基础认证信息
        request_data = {
            "appKey": self.app_key,
            "sign": self.sign,
            "worksheetId": self.worksheet_id,
            **data
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    json=request_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    result = await response.json()
                    
                    if response.status != 200:
                        logger.error(f"明道云API请求失败: {response.status}, {result}")
                        raise Exception(f"API请求失败: {result}")
                    
                    return result
                    
        except Exception as e:
            logger.error(f"明道云API请求异常: {e}")
            raise
    
    def _file_to_base64(self, file_path: str) -> str:
        """将文件转换为base64编码"""
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
                return base64.b64encode(file_content).decode('utf-8')
        except Exception as e:
            logger.error(f"文件转base64失败: {e}")
            raise
    
    async def create_translation_record(
        self,
        original_file_path: str,
        original_filename: str,
        remark: str = "",
        owner_id: str = ""
    ) -> Dict[str, Any]:
        """创建翻译记录（上传原文件）"""
        try:
            # 转换文件为base64
            base64_content = self._file_to_base64(original_file_path)
            
            # 构建控制项
            controls = [
                {
                    "controlId": self.control_ids["original_file"],
                    "valueType": 2,  # 文件流字节编码
                    "controlFiles": [
                        {
                            "baseFile": base64_content,
                            "fileName": original_filename
                        }
                    ]
                }
            ]
            
            # 添加备注
            if remark:
                controls.append({
                    "controlId": self.control_ids["remark"],
                    "value": remark
                })
            
            # 添加拥有者
            if owner_id:
                controls.append({
                    "controlId": self.control_ids["owner"],
                    "value": owner_id
                })
            
            # 发送请求
            data = {
                "triggerWorkflow": True,
                "controls": controls
            }
            
            result = await self._make_request("/worksheet/addRow", data)
            
            if result.get("success"):
                logger.info(f"明道云记录创建成功: {result.get('data')}")
                return result
            else:
                logger.error(f"明道云记录创建失败: {result}")
                raise Exception(f"创建记录失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            logger.error(f"创建明道云记录异常: {e}")
            raise
    
    async def update_translation_record(
        self,
        row_id: str,
        translated_file_path: str,
        translated_filename: str,
        remark: str = ""
    ) -> Dict[str, Any]:
        """更新翻译记录（上传翻译后文件）"""
        try:
            # 转换文件为base64
            base64_content = self._file_to_base64(translated_file_path)
            
            # 构建控制项
            controls = [
                {
                    "controlId": self.control_ids["translated_file"],
                    "valueType": 2,  # 文件流字节编码
                    "controlFiles": [
                        {
                            "baseFile": base64_content,
                            "fileName": translated_filename
                        }
                    ]
                }
            ]
            
            # 更新备注
            if remark:
                controls.append({
                    "controlId": self.control_ids["remark"],
                    "value": remark
                })
            
            # 发送请求
            data = {
                "triggerWorkflow": True,
                "controls": controls,
                "rowId": row_id
            }
            
            result = await self._make_request("/worksheet/editRow", data)
            
            if result.get("success"):
                logger.info(f"明道云记录更新成功: {row_id}")
                return result
            else:
                logger.error(f"明道云记录更新失败: {result}")
                raise Exception(f"更新记录失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            logger.error(f"更新明道云记录异常: {e}")
            raise
    
    async def get_documents(
        self,
        page_size: int = 100,
        page_index: int = 1,
        filters: List[Dict] = None
    ) -> Dict[str, Any]:
        """获取文档列表"""
        try:
            data = {
                "pageSize": page_size,
                "pageIndex": page_index,
                "listType": 0,
                "controls": [],
                "filters": filters or []
            }
            
            result = await self._make_request("/worksheet/getFilterRows", data)
            return result
            
        except Exception as e:
            logger.error(f"获取明道云文档列表异常: {e}")
            raise
    
    async def get_documents_total(self) -> int:
        """获取文档总数"""
        try:
            data = {
                "pageSize": 1,
                "pageIndex": 1,
                "listType": 0,
                "controls": []
            }
            
            result = await self._make_request("/worksheet/getFilterRowsTotalNum", data)
            
            if result.get("success"):
                total = result.get("data", 0)
                return int(total) if isinstance(total, (str, int)) else 0
            else:
                return 0
                
        except Exception as e:
            logger.error(f"获取明道云文档总数异常: {e}")
            return 0

    async def get_user_translation_settings(self, user_rowid: str):
        """获取用户的翻译格式设置"""
        try:
            # 构建过滤条件：根据用户rowid查找设置
            filters = [
                {
                    "controlId": "6888a7c2a849420e13f69e4a",  # 用户字段
                    "dataType": 29,  # 表关联类型
                    "spliceType": 1,  # AND条件
                    "filterType": 24,  # 包含（修正过滤器类型）
                    "value": user_rowid
                }
            ]

            data = {
                "appKey": self.app_key,
                "sign": self.sign,
                "worksheetId": "yhfygssz",  # 翻译设置表
                "pageSize": 50,
                "pageIndex": 1,
                "listType": 0,
                "controls": [],
                "filters": filters
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/v2/open/worksheet/getFilterRows",
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("success"):
                            rows = result.get("data", {}).get("rows", [])
                            if rows:
                                # 获取翻译设置JSON字段
                                settings_json = rows[0].get("6888a761a849420e13f69e40", "")
                                if settings_json:
                                    try:
                                        import json
                                        return json.loads(settings_json)
                                    except json.JSONDecodeError:
                                        logger.warning(f"用户 {user_rowid} 的翻译设置JSON格式错误")
                            return None
                    else:
                        logger.error(f"获取用户翻译设置失败，状态码: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"获取用户翻译设置异常: {e}")
            return None

    async def save_user_translation_settings(self, user_rowid: str, settings: dict, owner_id: str = "2499c06b-cecc-484d-ae58-16271bfa70ce"):
        """保存用户的翻译格式设置"""
        try:
            import json
            from datetime import datetime

            settings_json = json.dumps(settings, ensure_ascii=False)
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 先检查是否已存在设置记录
            existing_settings = await self.get_user_translation_settings(user_rowid)

            if existing_settings is not None:
                # 更新现有记录
                # 首先获取现有记录的rowid
                filters = [
                    {
                        "controlId": "6888a7c2a849420e13f69e4a",  # 用户字段
                        "dataType": 29,
                        "spliceType": 1,
                        "filterType": 24,  # 修正过滤器类型
                        "value": user_rowid
                    }
                ]

                data = {
                    "appKey": self.app_key,
                    "sign": self.sign,
                    "worksheetId": "yhfygssz",
                    "pageSize": 50,
                    "pageIndex": 1,
                    "listType": 0,
                    "controls": [],
                    "filters": filters
                }

                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.base_url}/api/v2/open/worksheet/getFilterRows",
                        json=data,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            if result.get("success"):
                                rows = result.get("data", {}).get("rows", [])
                                if rows:
                                    record_rowid = rows[0].get("rowid")

                                    # 更新记录
                                    update_data = {
                                        "appKey": self.app_key,
                                        "sign": self.sign,
                                        "worksheetId": "yhfygssz",
                                        "triggerWorkflow": True,
                                        "rowId": record_rowid,
                                        "controls": [
                                            {
                                                "controlId": "6888a761a849420e13f69e40",  # 翻译设置JSON
                                                "value": settings_json
                                            },
                                            {
                                                "controlId": "6888a7c2a849420e13f69e4a",  # 用户
                                                "value": user_rowid
                                            },
                                            {
                                                "controlId": "6888a7c2a849420e13f69e4c",  # 创建时间
                                                "value": current_time
                                            },
                                            {
                                                "controlId": "6888a7c2a849420e13f69e4d",  # 更新时间
                                                "value": current_time
                                            },
                                            {
                                                "controlId": "ownerid",  # 拥有者
                                                "value": owner_id
                                            }
                                        ]
                                    }

                                    async with session.post(
                                        f"{self.base_url}/api/v2/open/worksheet/editRow",
                                        json=update_data,
                                        timeout=aiohttp.ClientTimeout(total=30)
                                    ) as update_response:
                                        if update_response.status == 200:
                                            update_result = await update_response.json()
                                            if update_result.get("success"):
                                                logger.info(f"用户 {user_rowid} 翻译设置更新成功")
                                                return True
                                            else:
                                                logger.error(f"更新翻译设置失败: {update_result}")
                                                return False
            else:
                # 创建新记录
                create_data = {
                    "appKey": self.app_key,
                    "sign": self.sign,
                    "worksheetId": "yhfygssz",
                    "triggerWorkflow": True,
                    "controls": [
                        {
                            "controlId": "6888a761a849420e13f69e40",  # 翻译设置JSON
                            "value": settings_json
                        },
                        {
                            "controlId": "6888a7c2a849420e13f69e4a",  # 用户
                            "value": user_rowid
                        },
                        {
                            "controlId": "6888a7c2a849420e13f69e4c",  # 创建时间
                            "value": current_time
                        },
                        {
                            "controlId": "6888a7c2a849420e13f69e4d",  # 更新时间
                            "value": current_time
                        },
                        {
                            "controlId": "ownerid",  # 拥有者
                            "value": owner_id
                        }
                    ]
                }

                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.base_url}/api/v2/open/worksheet/addRow",
                        json=create_data,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            if result.get("success"):
                                logger.info(f"用户 {user_rowid} 翻译设置创建成功")
                                return True
                            else:
                                logger.error(f"创建翻译设置失败: {result}")
                                return False

            return False
        except Exception as e:
            logger.error(f"保存用户翻译设置异常: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

# 创建全局实例
mingdao_service = MingdaoService()
