// 文档文件信息接口
export interface DocumentFile {
  file_id: string;
  thumbnail_path: string;
  thumbnail_name: string;
  large_thumbnail_path: string | null;
  large_thumbnail_name: string | null;
  file_path: string;
  file_name: string;
  file_size: number;
  is_delete: boolean;
  file_type: number;
  original_file_name: string;
  allow_down: boolean;
  original_file_full_path: string;
  origin_link_url: string | null;
  short_link_url: string | null;
  share_folder_url: string | null;
  is_knowledge: boolean;
  node_id: string;
  allow_view: boolean;
  width: number;
  height: number;
  duration: number;
  allow_edit: boolean;
  DownloadUrl: string;
  WaterMarkInfo: any;
  preview_url: string | null;
  createTime: string;
}

// 用户信息接口
export interface UserInfo {
  accountId: string;
  fullname: string;
  avatar: string;
  isPortal: boolean;
  status: number;
}

// 文档行数据接口
export interface DocumentRow {
  _id: string;
  rowid: string;
  ctime: string;
  caid: UserInfo;
  uaid: UserInfo;
  ownerid: UserInfo;
  utime: string;
  autoid: number;
  [key: string]: any; // 用于处理动态字段
}

// API响应接口
export interface DocumentApiResponse {
  data: {
    rows: DocumentRow[];
  };
}

// 文件上传接口
export interface UploadFile {
  file: File;
  id: string;
  name: string;
  size: number;
  type: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

// 预览模式枚举
export enum PreviewMode {
  NONE = 'none',
  MODAL = 'modal',
  INLINE = 'inline'
}

// 文件类型枚举
export enum FileType {
  UNKNOWN = 0,
  IMAGE = 1,
  DOCUMENT = 2,
  VIDEO = 3,
  AUDIO = 4
}

// 用户账户信息接口
export interface UserAccount {
  id: string;
  username: string;
  email: string;
  fullName?: string;
  phone?: string;
  avatar?: string;
  status: 'active' | 'inactive' | 'suspended';
  userType: 'free' | 'premium' | 'enterprise';
  balance: number;
  totalQuota: number;
  usedQuota: number;
  monthlyQuota: number;
  monthlyUsed: number;
  monthlyExpireDate?: string;
  lastLogin?: string;
  createdAt: string;
  updatedAt?: string;
}

// 登录请求接口
export interface LoginRequest {
  username: string;
  password: string;
}

// 注册请求接口
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  fullName?: string;
  phone?: string;
}

// 充值记录接口
export interface RechargeRecord {
  id: string;
  userId: string;
  amount: number;
  type: 'balance' | 'quota';
  method: 'alipay' | 'wechat' | 'bank';
  status: 'pending' | 'success' | 'failed';
  orderId: string;
  description?: string;
  createdAt: string;
  completedAt?: string;
}

// 充值请求接口
export interface RechargeRequest {
  amount: number;
  type: 'balance' | 'quota';
  method: 'alipay' | 'wechat' | 'bank';
}
