"""
文件上传相关路由
"""
from typing import Optional
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_db
from app.schemas.file import (
    FileUploadResponse, FileInfo, FileListResponse, 
    DocumentContent, FileProcessingStatus
)
from app.services.file_service import FileService
from app.utils.dependencies import get_current_active_user
from app.utils.logger import logger


router = APIRouter()


@router.post("/", response_model=FileUploadResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    file: UploadFile = File(..., description="要上传的文档文件"),
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """
    上传文档文件
    
    支持的文件格式：
    - .docx (Microsoft Word 文档)
    - .doc (旧版 Microsoft Word 文档)
    """
    try:
        uploaded_file = await FileService.upload_file(db, file, current_user)
        
        return FileUploadResponse(
            id=uploaded_file.id,
            filename=uploaded_file.filename,
            original_filename=uploaded_file.original_filename,
            file_size=uploaded_file.file_size,
            mime_type=uploaded_file.mime_type,
            status=uploaded_file.status,
            total_paragraphs=uploaded_file.total_paragraphs,
            total_characters=uploaded_file.total_characters,
            created_at=uploaded_file.created_at.isoformat() if uploaded_file.created_at else None
        )
    except Exception as e:
        logger.error(f"File upload error: {e}")
        raise


@router.get("/", response_model=FileListResponse)
async def get_files(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取用户的文件列表"""
    try:
        skip = (page - 1) * page_size
        files, total = await FileService.get_user_files(db, current_user, skip, page_size)
        
        file_list = [
            FileInfo(
                id=file.id,
                filename=file.filename,
                original_filename=file.original_filename,
                file_path=file.file_path,
                file_size=file.file_size,
                mime_type=file.mime_type,
                status=file.status,
                total_paragraphs=file.total_paragraphs,
                total_characters=file.total_characters,
                error_message=file.error_message,
                created_at=file.created_at.isoformat() if file.created_at else None,
                updated_at=file.updated_at.isoformat() if file.updated_at else None
            )
            for file in files
        ]
        
        return FileListResponse(
            files=file_list,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        logger.error(f"Get files error: {e}")
        raise


@router.get("/{file_id}", response_model=FileInfo)
async def get_file(
    file_id: int,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取文件详细信息"""
    try:
        file = await FileService.get_file_by_id(db, file_id, current_user)
        
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        return FileInfo(
            id=file.id,
            filename=file.filename,
            original_filename=file.original_filename,
            file_path=file.file_path,
            file_size=file.file_size,
            mime_type=file.mime_type,
            status=file.status,
            total_paragraphs=file.total_paragraphs,
            total_characters=file.total_characters,
            error_message=file.error_message,
            created_at=file.created_at.isoformat() if file.created_at else None,
            updated_at=file.updated_at.isoformat() if file.updated_at else None
        )
    except Exception as e:
        logger.error(f"Get file error: {e}")
        raise


@router.get("/{file_id}/content", response_model=DocumentContent)
async def get_file_content(
    file_id: int,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取文件内容"""
    try:
        content = await FileService.get_file_content(db, file_id, current_user)
        
        return DocumentContent(
            paragraphs=content["paragraphs"],
            tables=content["tables"],
            total_characters=content["total_characters"],
            total_paragraphs=content["total_paragraphs"]
        )
    except Exception as e:
        logger.error(f"Get file content error: {e}")
        raise


@router.get("/{file_id}/status", response_model=FileProcessingStatus)
async def get_file_status(
    file_id: int,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取文件处理状态"""
    try:
        file = await FileService.get_file_by_id(db, file_id, current_user)
        
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # 计算处理进度
        progress = 0.0
        if file.status == "uploaded":
            progress = 0.2
        elif file.status == "processing":
            progress = 0.5
        elif file.status == "processed":
            progress = 1.0
        elif file.status == "error":
            progress = 0.0
        
        return FileProcessingStatus(
            id=file.id,
            status=file.status,
            progress=progress,
            error_message=file.error_message,
            total_paragraphs=file.total_paragraphs,
            total_characters=file.total_characters
        )
    except Exception as e:
        logger.error(f"Get file status error: {e}")
        raise


@router.delete("/{file_id}")
async def delete_file(
    file_id: int,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """删除文件"""
    try:
        success = await FileService.delete_file(db, file_id, current_user)
        
        if success:
            return {"message": "File deleted successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete file"
            )
    except Exception as e:
        logger.error(f"Delete file error: {e}")
        raise
