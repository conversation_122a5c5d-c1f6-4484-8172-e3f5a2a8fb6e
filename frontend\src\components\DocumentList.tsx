import React, { useState } from 'react';
import { Download, Eye, FileText, Globe, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { TranslationHistory } from '../types/document';
import { API_CONFIG } from '../config/api.config';

interface DocumentListProps {
  documents: TranslationHistory[];
  onPreview: (translationId: number) => void;
  onDownload: (translationId: number, filename: string) => void;
  onDownloadDocx: (translationId: number, filename: string) => void;
  onSelectionChange?: (selectedTranslations: TranslationHistory[]) => void;
  loading?: boolean;
  translationProgress?: {[key: number]: number};
}

const DocumentList: React.FC<DocumentListProps> = ({
  documents,
  onPreview,
  onDownload,
  onDownloadDocx,
  onSelectionChange,
  loading = false,
  translationProgress = {}
}) => {
  const [selectedTranslations, setSelectedTranslations] = useState<Set<number>>(new Set());

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="text-green-500" size={16} />;
      case 'FAILED':
        return <XCircle className="text-red-500" size={16} />;
      case 'IN_PROGRESS':
        return <Clock className="text-blue-500" size={16} />;
      case 'PENDING':
        return <AlertCircle className="text-yellow-500" size={16} />;
      default:
        return <AlertCircle className="text-gray-500" size={16} />;
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'COMPLETED': return '已完成';
      case 'FAILED': return '失败';
      case 'IN_PROGRESS': return '翻译中';
      case 'PENDING': return '等待中';
      default: return '未知';
    }
  };

  const getLanguageName = (code: string): string => {
    return API_CONFIG.translation.supportedLanguages[code as keyof typeof API_CONFIG.translation.supportedLanguages] || code;
  };

  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName?.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'doc':
      case 'docx':
        return <FileText className="text-blue-500" size={20} />;
      default:
        return <FileText className="text-gray-500" size={20} />;
    }
  };
  const toggleTranslationSelection = (translationId: number) => {
    const newSelected = new Set(selectedTranslations);
    if (newSelected.has(translationId)) {
      newSelected.delete(translationId);
    } else {
      newSelected.add(translationId);
    }
    setSelectedTranslations(newSelected);

    // 通知父组件选择变化
    if (onSelectionChange) {
      const selectedDocs = documents.filter(doc => newSelected.has(doc.id));
      onSelectionChange(selectedDocs);
    }
  };

  const selectAllTranslations = () => {
    let newSelected: Set<number>;
    if (selectedTranslations.size === documents.length) {
      newSelected = new Set();
    } else {
      newSelected = new Set(documents.map(doc => doc.id));
    }
    setSelectedTranslations(newSelected);

    // 通知父组件选择变化
    if (onSelectionChange) {
      const selectedDocs = documents.filter(doc => newSelected.has(doc.id));
      onSelectionChange(selectedDocs);
    }
  };

  if (loading) {
    return (
      <div className="document-list loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>加载翻译历史中...</p>
        </div>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="document-list empty">
        <div className="empty-state">
          <Globe size={64} className="empty-icon" />
          <h3>暂无翻译记录</h3>
          <p>请上传文档并创建翻译任务</p>
        </div>
      </div>
    );
  }

  return (
    <div className="document-list">
      <div className="list-header">
        <div className="list-controls">
          <label className="checkbox-container">
            <input
              type="checkbox"
              checked={selectedTranslations.size === documents.length && documents.length > 0}
              onChange={selectAllTranslations}
            />
            <span className="checkmark"></span>
            全选 ({selectedTranslations.size}/{documents.length})
          </label>
        </div>
        <div className="list-stats">
          共 {documents.length} 条翻译记录
        </div>
      </div>

      <div className="document-items">
        {documents.map((translation) => {
          const isSelected = selectedTranslations.has(translation.id);
          const progress = translationProgress[translation.id] || translation.progress;
          const filename = translation.file?.original_filename || `translation_${translation.id}`;

          return (
            <div key={translation.id} className={`document-item ${isSelected ? 'selected' : ''}`}>
              <div className="translation-container">
                {/* 左侧：复选框和基本信息 */}
                <div className="translation-checkbox">
                  <label className="checkbox-container">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => toggleTranslationSelection(translation.id)}
                    />
                    <span className="checkmark"></span>
                  </label>
                </div>

                {/* 中间：翻译信息 */}
                <div className="translation-info">
                  <div className="translation-header">
                    <div className="file-info">
                      <div className="file-icon">
                        {getFileTypeIcon(filename)}
                      </div>
                      <div className="file-details">
                        <h4 className="file-name" title={filename}>
                          {filename}
                        </h4>
                        <div className="translation-meta">
                          <span className="language-pair">
                            {getLanguageName(translation.source_language)} → {getLanguageName(translation.target_language)}
                          </span>
                          <span className="character-count">
                            {translation.total_characters} 字符
                          </span>
                          {translation.file?.file_size && (
                            <span className="file-size">
                              {formatFileSize(translation.file.file_size)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 状态和进度 */}
                  <div className="translation-status">
                    <div className="status-info">
                      {getStatusIcon(translation.status)}
                      <span className="status-text">{getStatusText(translation.status)}</span>
                      {translation.status === 'IN_PROGRESS' && (
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{ width: `${progress}%` }}
                          ></div>
                          <span className="progress-text">{progress}%</span>
                        </div>
                      )}
                    </div>
                    <div className="translation-dates">
                      <span className="created-date">
                        创建: {formatDate(translation.created_at)}
                      </span>
                      {translation.completed_at && (
                        <span className="completed-date">
                          完成: {formatDate(translation.completed_at)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* 右侧：操作按钮 */}
                <div className="translation-actions">
                  {translation.status === 'COMPLETED' && (
                    <>
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={() => onPreview(translation.id)}
                        title="HTML预览"
                      >
                        <Globe size={14} />
                        预览
                      </button>
                      <button
                        className="btn btn-sm btn-info"
                        onClick={() => onDownload(translation.id, filename)}
                        title="下载文本"
                      >
                        <Download size={14} />
                        文本
                      </button>
                      <button
                        className="btn btn-sm btn-warning"
                        onClick={() => onDownloadDocx(translation.id, filename)}
                        title="下载Word文档"
                      >
                        <FileText size={14} />
                        Word
                      </button>
                    </>
                  )}
                  {translation.status === 'FAILED' && translation.error_message && (
                    <div className="error-message" title={translation.error_message}>
                      <XCircle size={14} className="text-red-500" />
                      <span className="error-text">
                        {translation.error_message.length > 30
                          ? `${translation.error_message.substring(0, 30)}...`
                          : translation.error_message}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DocumentList;
