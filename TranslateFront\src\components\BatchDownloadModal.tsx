import React, { useState } from 'react';
import { Download, X, FileText, Languages, Package } from 'lucide-react';

interface BatchDownloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (downloadType: 'original' | 'translated' | 'both') => void;
  selectedCount: number;
}

const BatchDownloadModal: React.FC<BatchDownloadModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  selectedCount
}) => {
  const [selectedType, setSelectedType] = useState<'original' | 'translated' | 'both'>('both');

  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm(selectedType);
    onClose();
  };

  return (
    <div className="batch-download-modal">
      <div className="batch-download-container">
        <div className="batch-download-header">
          <h3>批量下载选择</h3>
          <button
            className="close-btn"
            onClick={onClose}
            title="关闭"
          >
            <X size={20} />
          </button>
        </div>

        <div className="batch-download-content">
          <div className="download-info">
            <p>已选择 <strong>{selectedCount}</strong> 个文档</p>
            <p>请选择要下载的文件类型：</p>
          </div>

          <div className="download-options">
            <label className={`download-option ${selectedType === 'original' ? 'selected' : ''}`}>
              <input
                type="radio"
                name="downloadType"
                value="original"
                checked={selectedType === 'original'}
                onChange={(e) => setSelectedType(e.target.value as 'original')}
              />
              <div className="option-content">
                <FileText size={24} className="option-icon" />
                <div className="option-text">
                  <h4>仅原文档</h4>
                  <p>下载原始上传的文档文件</p>
                </div>
              </div>
            </label>

            <label className={`download-option ${selectedType === 'translated' ? 'selected' : ''}`}>
              <input
                type="radio"
                name="downloadType"
                value="translated"
                checked={selectedType === 'translated'}
                onChange={(e) => setSelectedType(e.target.value as 'translated')}
              />
              <div className="option-content">
                <Languages size={24} className="option-icon" />
                <div className="option-text">
                  <h4>仅翻译文档</h4>
                  <p>下载翻译后的文档文件</p>
                </div>
              </div>
            </label>

            <label className={`download-option ${selectedType === 'both' ? 'selected' : ''}`}>
              <input
                type="radio"
                name="downloadType"
                value="both"
                checked={selectedType === 'both'}
                onChange={(e) => setSelectedType(e.target.value as 'both')}
              />
              <div className="option-content">
                <Package size={24} className="option-icon" />
                <div className="option-text">
                  <h4>原文档 + 翻译文档</h4>
                  <p>下载所有相关文件（推荐）</p>
                </div>
              </div>
            </label>
          </div>
        </div>

        <div className="batch-download-footer">
          <button
            className="btn btn-secondary"
            onClick={onClose}
          >
            取消
          </button>
          <button
            className="btn btn-primary"
            onClick={handleConfirm}
          >
            <Download size={16} />
            开始下载
          </button>
        </div>
      </div>
    </div>
  );
};

export default BatchDownloadModal;
