import React from 'react';
import { X, FileText, AlertTriangle } from 'lucide-react';
import './BatchTranslateModal.css';

interface BatchTranslateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  selectedCount: number;
}

const BatchTranslateModal: React.FC<BatchTranslateModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  selectedCount
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content batch-translate-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <div className="modal-title">
            <FileText size={24} />
            <h3>批量翻译确认</h3>
          </div>
          <button className="modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="modal-body">
          <div className="warning-section">
            <AlertTriangle size={48} className="warning-icon" />
            <div className="warning-content">
              <h4>确认批量翻译</h4>
              <p>您即将开始翻译 <strong>{selectedCount}</strong> 个文档</p>
              <div className="warning-details">
                <ul>
                  <li>批量翻译将同时处理多个文档</li>
                  <li>翻译过程中请勿关闭浏览器</li>
                  <li>您可以在翻译进度区域查看实时进度</li>
                  <li>翻译完成后会自动刷新文档列表</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="translation-info">
            <div className="info-item">
              <span className="info-label">翻译方向:</span>
              <span className="info-value">中文 → English</span>
            </div>
            <div className="info-item">
              <span className="info-label">文档数量:</span>
              <span className="info-value">{selectedCount} 个</span>
            </div>
            <div className="info-item">
              <span className="info-label">预计时间:</span>
              <span className="info-value">根据文档大小而定</span>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-outline" onClick={onClose}>
            取消
          </button>
          <button className="btn btn-primary" onClick={onConfirm}>
            开始翻译
          </button>
        </div>
      </div>
    </div>
  );
};

export default BatchTranslateModal;
