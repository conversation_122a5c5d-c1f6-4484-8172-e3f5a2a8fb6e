import React, { useState, useEffect } from 'react';
import { User, CreditCard, Clock, TrendingUp, Gift, AlertCircle } from 'lucide-react';
import { UserInfo } from '../types/document';
import './MingdaoUserInfo.css';

interface MingdaoUserInfoProps {
  user: UserInfo | null;
  onRefresh?: () => void;
}

const MingdaoUserInfo: React.FC<MingdaoUserInfoProps> = ({ user, onRefresh }) => {
  const [quotaUsagePercent, setQuotaUsagePercent] = useState(0);
  const [monthlyUsagePercent, setMonthlyUsagePercent] = useState(0);
  const [userTypeDisplay, setUserTypeDisplay] = useState('');
  const [statusDisplay, setStatusDisplay] = useState('');

  useEffect(() => {
    if (user) {
      // 计算配额使用百分比
      const totalQuota = user.total_quota || 0;
      const usedQuota = user.used_quota || 0;
      const quotaPercent = totalQuota > 0 ? (usedQuota / totalQuota) * 100 : 0;
      setQuotaUsagePercent(Math.min(quotaPercent, 100));

      // 计算月度配额使用百分比
      const monthlyQuota = user.monthly_quota || 0;
      const monthlyUsed = user.monthly_used || 0;
      const monthlyPercent = monthlyQuota > 0 ? (monthlyUsed / monthlyQuota) * 100 : 0;
      setMonthlyUsagePercent(Math.min(monthlyPercent, 100));

      // 用户类型显示
      setUserTypeDisplay(getUserTypeDisplay(user.user_type));
      
      // 状态显示
      setStatusDisplay(getUserStatusDisplay(user.status));
    }
  }, [user]);

  const getUserTypeDisplay = (userType: string) => {
    const typeMap: { [key: string]: string } = {
      'b4e9a50e-e83a-4e1e-ac29-619572a67265': '免费用户',
      'monthly': '月付用户',
      'pay_per_use': '按次付费用户',
      'vip': 'VIP用户'
    };
    return typeMap[userType] || userType || '未知';
  };

  const getUserStatusDisplay = (status: string) => {
    const statusMap: { [key: string]: string } = {
      '0f7a48f5-9dc2-496d-96ca-c4a3b49e6d00': '正常',
      'disabled': '禁用',
      'pending': '待激活'
    };
    return statusMap[status] || status || '未知';
  };

  const getQuotaStatusColor = (percent: number) => {
    if (percent >= 90) return '#f44336'; // 红色
    if (percent >= 70) return '#ff9800'; // 橙色
    return '#4caf50'; // 绿色
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '未设置';
    try {
      return new Date(dateString).toLocaleString('zh-CN');
    } catch {
      return dateString;
    }
  };

  const getRemainingDays = (expireDate: string) => {
    if (!expireDate) return null;
    try {
      const expire = new Date(expireDate);
      const now = new Date();
      const diffTime = expire.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch {
      return null;
    }
  };

  if (!user) {
    return (
      <div className="mingdao-user-info">
        <div className="user-info-placeholder">
          <User size={48} />
          <p>请先登录查看用户信息</p>
        </div>
      </div>
    );
  }

  const remainingDays = getRemainingDays(user.monthly_expire_date);

  return (
    <div className="mingdao-user-info">
      {/* 用户基本信息 */}
      <div className="user-basic-info">
        <div className="user-avatar">
          <User size={32} />
        </div>
        <div className="user-details">
          <h3>{user.full_name || user.username}</h3>
          <p className="user-email">{user.email}</p>
          <div className="user-badges">
            <span className={`user-type-badge ${user.user_type}`}>
              {userTypeDisplay}
            </span>
            <span className={`user-status-badge ${user.status}`}>
              {statusDisplay}
            </span>
          </div>
        </div>
        {onRefresh && (
          <button onClick={onRefresh} className="refresh-btn">
            刷新
          </button>
        )}
      </div>

      {/* 账户余额 */}
      <div className="account-balance">
        <div className="balance-header">
          <CreditCard size={20} />
          <span>账户余额</span>
        </div>
        <div className="balance-amount">
          ¥{(user.balance || 0).toFixed(2)}
        </div>
        <button className="recharge-btn">充值</button>
      </div>

      {/* 免费配额 */}
      <div className="quota-section">
        <div className="quota-header">
          <Gift size={20} />
          <span>免费配额</span>
        </div>
        <div className="quota-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ 
                width: `${quotaUsagePercent}%`,
                backgroundColor: getQuotaStatusColor(quotaUsagePercent)
              }}
            />
          </div>
          <div className="quota-text">
            {(user.used_quota || 0).toLocaleString()} / {(user.total_quota || 0).toLocaleString()} 字符
          </div>
          <div className="quota-percent">
            {quotaUsagePercent.toFixed(1)}% 已使用
          </div>
        </div>
      </div>

      {/* 月付配额 */}
      {(user.monthly_quota || 0) > 0 && (
        <div className="monthly-quota-section">
          <div className="quota-header">
            <TrendingUp size={20} />
            <span>月付配额</span>
            {remainingDays !== null && (
              <span className={`expire-badge ${remainingDays <= 7 ? 'warning' : ''}`}>
                {remainingDays > 0 ? `${remainingDays}天后到期` : '已到期'}
              </span>
            )}
          </div>
          <div className="quota-progress">
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ 
                  width: `${monthlyUsagePercent}%`,
                  backgroundColor: getQuotaStatusColor(monthlyUsagePercent)
                }}
              />
            </div>
            <div className="quota-text">
              {(user.monthly_used || 0).toLocaleString()} / {(user.monthly_quota || 0).toLocaleString()} 字符
            </div>
            <div className="quota-percent">
              {monthlyUsagePercent.toFixed(1)}% 已使用
            </div>
          </div>
        </div>
      )}

      {/* 使用统计 */}
      <div className="usage-stats">
        <h4>使用统计</h4>
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-label">总使用字符</div>
            <div className="stat-value">{(user.used_quota || 0).toLocaleString()}</div>
          </div>
          <div className="stat-item">
            <div className="stat-label">本月使用</div>
            <div className="stat-value">{(user.monthly_used || 0).toLocaleString()}</div>
          </div>
          <div className="stat-item">
            <div className="stat-label">剩余配额</div>
            <div className="stat-value">
              {Math.max(0, (user.total_quota || 0) - (user.used_quota || 0)).toLocaleString()}
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-label">账户余额</div>
            <div className="stat-value">¥{(user.balance || 0).toFixed(2)}</div>
          </div>
        </div>
      </div>

      {/* 账户信息 */}
      <div className="account-info">
        <h4>账户信息</h4>
        <div className="info-list">
          <div className="info-item">
            <span className="info-label">用户ID:</span>
            <span className="info-value">{user.id}</span>
          </div>
          <div className="info-item">
            <span className="info-label">注册时间:</span>
            <span className="info-value">{formatDate(user.created_at)}</span>
          </div>
          <div className="info-item">
            <span className="info-label">最后登录:</span>
            <span className="info-value">{formatDate(user.last_login)}</span>
          </div>
          {user.monthly_expire_date && (
            <div className="info-item">
              <span className="info-label">月付到期:</span>
              <span className="info-value">{formatDate(user.monthly_expire_date)}</span>
            </div>
          )}
        </div>
      </div>

      {/* 配额不足警告 */}
      {quotaUsagePercent >= 90 && (
        <div className="quota-warning">
          <AlertCircle size={20} />
          <div>
            <h4>配额即将用完</h4>
            <p>您的免费配额已使用 {quotaUsagePercent.toFixed(1)}%，建议及时充值或购买月付套餐。</p>
          </div>
        </div>
      )}

      {/* 月付到期警告 */}
      {remainingDays !== null && remainingDays <= 7 && remainingDays > 0 && (
        <div className="expire-warning">
          <Clock size={20} />
          <div>
            <h4>月付套餐即将到期</h4>
            <p>您的月付套餐将在 {remainingDays} 天后到期，请及时续费。</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default MingdaoUserInfo;
