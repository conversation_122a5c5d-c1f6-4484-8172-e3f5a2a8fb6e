.register-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.register-modal {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 模态框头部 */
.register-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border-radius: 15px 15px 0 0;
}

.register-modal-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.close-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.close-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 结果消息 */
.result-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 25px;
  font-size: 0.9rem;
  font-weight: 500;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-message.success {
  background: #d4edda;
  border-bottom: 1px solid #c3e6cb;
  color: #155724;
}

.result-message.error {
  background: #f8d7da;
  border-bottom: 1px solid #f5c6cb;
  color: #721c24;
}

/* 注册表单 */
.register-form {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
  background: white;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #28a745;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.form-group input.error {
  border-color: #dc3545;
}

.form-group input:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

/* 密码输入框 */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  padding-right: 45px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.password-toggle:hover:not(:disabled) {
  color: #333;
}

.password-toggle:disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* 错误信息 */
.error-message {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 4px;
  display: block;
}

/* 表单操作 */
.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 25px;
}

.register-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 20px;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, opacity 0.2s;
}

.register-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.register-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancel-btn {
  padding: 14px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn:hover:not(:disabled) {
  background: #5a6268;
}

.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 登录链接 */
.login-link {
  text-align: center;
  padding: 15px 25px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.login-link p {
  margin: 0;
  color: #333;
  font-size: 0.9rem;
}

.link-btn {
  background: none;
  border: none;
  color: #28a745;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  margin-left: 5px;
  transition: color 0.2s;
}

.link-btn:hover:not(:disabled) {
  color: #218838;
}

.link-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 注册说明 */
.register-info {
  padding: 20px 25px;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
}

.register-info h4 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.register-info h4:before {
  content: "🎁";
  font-size: 1.2rem;
}

.register-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.register-info li {
  font-size: 0.9rem;
  color: #555;
  padding: 6px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-modal-overlay {
    padding: 10px;
  }
  
  .register-modal {
    max-height: 95vh;
    border-radius: 10px;
  }
  
  .register-modal-header {
    padding: 15px 20px;
    border-radius: 10px 10px 0 0;
  }
  
  .register-modal-header h2 {
    font-size: 1.2rem;
  }
  
  .close-btn {
    width: 32px;
    height: 32px;
  }
  
  .register-form {
    padding: 20px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .register-btn,
  .cancel-btn {
    width: 100%;
  }
  
  .login-link {
    padding: 12px 20px;
  }
  
  .register-info {
    padding: 15px 20px;
    border-radius: 0 0 10px 10px;
  }
}

@media (max-width: 480px) {
  .register-modal {
    margin: 5px;
    border-radius: 8px;
  }
  
  .form-group input {
    padding: 10px 12px;
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  .password-input input {
    padding-right: 40px;
  }
  
  .password-toggle {
    right: 10px;
  }
  
  .register-btn,
  .cancel-btn {
    padding: 12px 16px;
    font-size: 14px;
  }
}

/* 焦点状态增强 */
.form-group input:focus,
.register-btn:focus,
.cancel-btn:focus,
.password-toggle:focus,
.close-btn:focus,
.link-btn:focus {
  outline: 2px solid #28a745;
  outline-offset: 2px;
}

/* 成功状态样式 */
.register-btn.success {
  background: #28a745;
}

.register-btn.success:hover {
  background: #218838;
}

/* 表单验证视觉反馈 */
.form-group input:valid:not(:placeholder-shown) {
  border-color: #28a745;
}

.form-group input:invalid:not(:placeholder-shown):not(:focus) {
  border-color: #dc3545;
}
