# 🎨 翻译格式设置功能说明

## 🎯 **功能概述**

### **核心特性**
- ✅ **用户自定义译文格式** - 字体、大小、样式、对齐等
- ✅ **实时预览效果** - 所见即所得的格式预览
- ✅ **格式继承机制** - 先继承原文格式，再应用用户设置
- ✅ **设置持久化** - 自动保存用户偏好设置

### **设计理念**
```
原文格式 + 用户设置 = 最终译文格式
```

## 🔧 **前端组件**

### **1. TranslationSettings.tsx**
完整的翻译格式设置界面组件

#### **功能特点**
- 📝 **字体设置** - 字体名称、大小
- ✨ **字体样式** - 粗体、斜体、下划线
- 📐 **段落格式** - 对齐方式、首行缩进
- 📏 **间距设置** - 行间距、段前间距
- 🎨 **颜色设置** - 文字颜色、背景颜色

#### **实时预览**
```tsx
const previewStyle = useMemo(() => {
  const style: React.CSSProperties = {
    fontFamily: settings.fontFamily,
    fontSize: `${settings.fontSize}pt`,
    fontWeight: settings.bold ? 'bold' : 'normal',
    // ... 其他样式
  };
  return style;
}, [settings]);
```

### **2. 设置选项详解**

#### **字体设置**
| 选项 | 类型 | 范围 | 默认值 |
|------|------|------|--------|
| 字体名称 | select | Arial, Times New Roman, Calibri, 宋体, 微软雅黑, 黑体 | Arial |
| 字体大小 | number | 6-72pt | 10.5pt |

#### **字体样式**
| 选项 | 类型 | 默认值 |
|------|------|--------|
| 粗体 | checkbox | false |
| 斜体 | checkbox | false |
| 下划线 | checkbox | false |

#### **段落格式**
| 选项 | 类型 | 选项值 | 默认值 |
|------|------|--------|--------|
| 对齐方式 | select | 跟随原文, 左对齐, 居中, 右对齐, 两端对齐 | 跟随原文 |
| 首行缩进 | number | 0-10字符 | 0 |

#### **间距设置**
| 选项 | 类型 | 选项值 | 默认值 |
|------|------|--------|--------|
| 行间距 | select | 跟随原文, 单倍, 1.15倍, 1.5倍, 双倍 | 跟随原文 |
| 段前间距 | number | 0-50pt | 0pt |

#### **颜色设置**
| 选项 | 类型 | 默认值 |
|------|------|--------|
| 文字颜色 | color | #000000 |
| 背景颜色 | color | #ffffff |

## 🔧 **后端实现**

### **1. 修改翻译服务**
需要在Word文档翻译过程中应用用户设置：

```python
# 在translation_service.py中添加
async def apply_translation_format(
    doc: Document, 
    settings: TranslationSettings,
    paragraph: Paragraph = None,
    is_table_cell: bool = False
):
    """应用翻译格式设置"""
    
    # 获取原文格式
    original_style = get_original_style(paragraph)
    
    # 合并用户设置
    final_style = merge_styles(original_style, settings)
    
    # 应用到译文
    apply_style_to_translation(paragraph, final_style)
```

### **2. 格式继承逻辑**

#### **继承原文的格式**
```python
def get_original_style(paragraph):
    """获取原文段落的格式"""
    return {
        'alignment': paragraph.alignment,
        'left_indent': paragraph.paragraph_format.left_indent,
        'right_indent': paragraph.paragraph_format.right_indent,
        'space_before': paragraph.paragraph_format.space_before,
        'space_after': paragraph.paragraph_format.space_after,
        # ... 其他原文格式
    }
```

#### **应用用户设置**
```python
def merge_styles(original_style, user_settings):
    """合并原文格式和用户设置"""
    merged = original_style.copy()
    
    # 字体设置（覆盖原文）
    merged['font_family'] = user_settings.fontFamily
    merged['font_size'] = user_settings.fontSize
    merged['bold'] = user_settings.bold
    merged['italic'] = user_settings.italic
    
    # 段落格式（inherit时保留原文）
    if user_settings.textAlign != 'inherit':
        merged['alignment'] = user_settings.textAlign
    
    if user_settings.lineHeight != 'inherit':
        merged['line_spacing'] = user_settings.lineHeight
    
    return merged
```

### **3. API接口扩展**

#### **翻译请求接口**
```python
class TranslationRequest:
    row_id: str
    source_language: str
    target_language: str
    translation_settings: Optional[TranslationSettings] = None
```

#### **翻译设置接口**
```python
class TranslationSettings:
    font_family: str = "Arial"
    font_size: float = 10.5
    bold: bool = False
    italic: bool = False
    underline: bool = False
    text_align: str = "inherit"
    text_indent: float = 0
    line_height: str = "inherit"
    margin_top: float = 0
    color: str = "#000000"
    background_color: str = "#ffffff"
```

## 🎨 **用户体验设计**

### **1. 设置界面布局**
```
┌─────────────────────────────────────────────────────┐
│                🎨 翻译格式设置                [✕]    │
├─────────────────────────────────────────────────────┤
│ 📝 字体设置                                         │
│   字体名称: [Arial ▼]    字体大小: [10.5] pt       │
│                                                     │
│ ✨ 字体样式                                         │
│   [☐] 粗体  [☐] 斜体  [☐] 下划线                   │
│                                                     │
│ 📐 段落格式                                         │
│   对齐方式: [跟随原文 ▼]  首行缩进: [0] 字符        │
│                                                     │
│ 📏 间距设置                                         │
│   行间距: [跟随原文 ▼]    段前间距: [0] pt          │
│                                                     │
│ 🎨 颜色设置                                         │
│   文字颜色: [⬛]          背景颜色: [⬜]             │
├─────────────────────────────────────────────────────┤
│ 📖 效果预览                                         │
│ ┌─────────────────┐ ┌─────────────────┐             │
│ │ 原文示例：      │ │ 译文示例：      │             │
│ │ 这是一段中文... │ │ This is an...   │             │
│ └─────────────────┘ └─────────────────┘             │
├─────────────────────────────────────────────────────┤
│           [🔄 恢复默认]  [💾 保存设置]              │
└─────────────────────────────────────────────────────┘
```

### **2. 实时预览效果**
- ✅ **即时反馈** - 用户修改设置时立即看到效果
- ✅ **对比显示** - 原文和译文并排显示
- ✅ **真实样式** - 预览使用实际的CSS样式

### **3. 响应式设计**
- 📱 **移动端适配** - 在手机上也能正常使用
- 💻 **桌面端优化** - 充分利用大屏幕空间
- 🖥️ **平板端兼容** - 适应中等屏幕尺寸

## 🔄 **完整工作流程**

### **1. 用户设置格式**
```
用户点击"翻译设置" → 打开设置界面 → 调整各项参数 → 实时预览效果 → 保存设置
```

### **2. 翻译处理流程**
```
开始翻译 → 读取用户设置 → 解析原文格式 → 翻译文本 → 应用格式继承 → 生成最终文档
```

### **3. 格式应用逻辑**
```
原文段落格式 + 用户字体设置 + 用户段落设置 = 译文最终格式
```

## 🚀 **实现步骤**

### **阶段1：前端界面** ✅
- [x] 创建TranslationSettings组件
- [x] 实现设置界面UI
- [x] 添加实时预览功能
- [x] 实现设置保存/加载

### **阶段2：后端集成** 🔄
- [ ] 修改翻译API接口
- [ ] 实现格式继承逻辑
- [ ] 更新Word文档处理
- [ ] 添加设置验证

### **阶段3：前端集成** 🔄
- [ ] 在主界面添加设置按钮
- [ ] 集成设置组件
- [ ] 实现设置传递
- [ ] 添加错误处理

### **阶段4：测试优化** 🔄
- [ ] 功能测试
- [ ] 格式效果验证
- [ ] 用户体验优化
- [ ] 性能优化

## 🎯 **预期效果**

### **用户体验**
- ✅ **个性化定制** - 用户可以按需设置译文格式
- ✅ **格式一致性** - 译文保持统一的格式风格
- ✅ **原文兼容性** - 译文格式与原文协调
- ✅ **设置便捷性** - 一次设置，多次使用

### **翻译质量**
- ✅ **视觉效果** - 译文具有良好的视觉呈现
- ✅ **阅读体验** - 格式设置提升阅读舒适度
- ✅ **专业外观** - 翻译文档具有专业水准
- ✅ **品牌一致性** - 可以设置符合企业标准的格式

现在前端组件已经完成，接下来需要：
1. 在主界面集成设置按钮
2. 修改后端翻译逻辑支持格式设置
3. 实现完整的格式继承和应用机制

这个功能将大大提升翻译系统的实用性和专业性！🎊
