#!/usr/bin/env python3
"""
简单的后端测试脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    try:
        print("🔍 测试基础导入...")
        import fastapi
        print(f"✅ FastAPI版本: {fastapi.__version__}")
        
        import uvicorn
        print(f"✅ Uvicorn可用")
        
        import sqlalchemy
        print(f"✅ SQLAlchemy版本: {sqlalchemy.__version__}")
        
        print("\n🔍 测试应用导入...")
        from app.main import app
        print("✅ 应用导入成功")
        
        print("\n🔍 测试数据库...")
        from app.database import engine
        print("✅ 数据库引擎创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """测试数据库连接"""
    try:
        print("\n🔍 测试数据库连接...")
        from app.database import get_db
        from app.models.user import User
        
        # 简单的数据库测试
        print("✅ 数据库模型导入成功")
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    print("🚀 开始后端测试...")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查依赖")
        return False
    
    # 测试数据库
    if not test_database():
        print("\n❌ 数据库测试失败")
        return False
    
    print("\n✅ 所有测试通过！")
    print("\n🚀 尝试启动服务器...")
    
    try:
        import uvicorn
        from app.main import app
        
        print("📡 启动服务器在 http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("🛑 按 Ctrl+C 停止服务器")
        
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000, 
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
