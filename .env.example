# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/translate_db
DATABASE_URL_SYNC=postgresql://username:password@localhost:5432/translate_db

# JWT 配置
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Azure 翻译 API
AZURE_TRANSLATOR_KEY=your-azure-translator-key
AZURE_TRANSLATOR_ENDPOINT=https://api.cognitive.microsofttranslator.com
AZURE_TRANSLATOR_REGION=your-region

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=.docx,.doc

# 应用配置
APP_NAME=Translation Service
APP_VERSION=1.0.0
DEBUG=True
HOST=0.0.0.0
PORT=8000

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Redis 配置 (可选，用于缓存和限流)
REDIS_URL=redis://localhost:6379/0

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_EMAIL=<EMAIL>
