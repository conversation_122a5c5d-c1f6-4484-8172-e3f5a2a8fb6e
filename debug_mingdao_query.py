#!/usr/bin/env python3
"""
调试明道云查询问题
"""

import requests
import json

def debug_mingdao_query():
    """调试明道云查询"""
    
    print("🔍 调试明道云查询问题")
    print("=" * 60)
    
    # 1. 查询所有记录
    print("\n1️⃣ 查询所有翻译设置记录")
    try:
        url = "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows"
        data = {
            "appKey": "d88c1d2329c42504",
            "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
            "worksheetId": "yhfygssz",
            "pageSize": 50,
            "pageIndex": 1,
            "listType": 0,
            "controls": []
        }
        
        response = requests.post(url, json=data, headers={"Content-Type": "application/json"}, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                rows = result.get("data", {}).get("rows", [])
                total = result.get("data", {}).get("total", 0)
                print(f"✅ 查询成功，总共 {total} 条记录")
                
                for i, row in enumerate(rows):
                    print(f"\n记录 {i+1}:")
                    print(f"  记录ID: {row.get('rowid')}")
                    print(f"  用户字段: {row.get('6888a7c2a849420e13f69e4a')}")
                    print(f"  用户名: {row.get('6888a7c2a849420e13f69e49')}")
                    settings_json = row.get('6888a761a849420e13f69e40', '')
                    print(f"  设置JSON: {settings_json[:100]}..." if len(settings_json) > 100 else f"  设置JSON: {settings_json}")
                    print(f"  创建时间: {row.get('6888a7c2a849420e13f69e4c')}")
                    print(f"  更新时间: {row.get('6888a7c2a849420e13f69e4d')}")
            else:
                print(f"❌ 查询失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
    
    # 2. 测试特定用户查询
    test_users = [
        "e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa",  # 原有用户
        "test-user-new-123456"  # 新测试用户
    ]
    
    for user_rowid in test_users:
        print(f"\n2️⃣ 测试查询用户: {user_rowid}")
        try:
            # 使用filterType 24 (包含)
            filters_24 = [
                {
                    "controlId": "6888a7c2a849420e13f69e4a",
                    "dataType": 29,
                    "spliceType": 1,
                    "filterType": 24,
                    "value": user_rowid
                }
            ]
            
            data_24 = {
                "appKey": "d88c1d2329c42504",
                "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
                "worksheetId": "yhfygssz",
                "pageSize": 50,
                "pageIndex": 1,
                "listType": 0,
                "controls": [],
                "filters": filters_24
            }
            
            response = requests.post(url, json=data_24, headers={"Content-Type": "application/json"}, timeout=30)
            print(f"  filterType 24 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    rows = result.get("data", {}).get("rows", [])
                    print(f"  filterType 24 找到 {len(rows)} 条记录")
                    if rows:
                        for row in rows:
                            print(f"    记录ID: {row.get('rowid')}")
                            print(f"    用户字段: {row.get('6888a7c2a849420e13f69e4a')}")
                else:
                    print(f"  filterType 24 失败: {result.get('message', '未知错误')}")
            
            # 使用filterType 2 (等于)
            filters_2 = [
                {
                    "controlId": "6888a7c2a849420e13f69e4a",
                    "dataType": 29,
                    "spliceType": 1,
                    "filterType": 2,
                    "value": user_rowid
                }
            ]
            
            data_2 = {
                "appKey": "d88c1d2329c42504",
                "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
                "worksheetId": "yhfygssz",
                "pageSize": 50,
                "pageIndex": 1,
                "listType": 0,
                "controls": [],
                "filters": filters_2
            }
            
            response = requests.post(url, json=data_2, headers={"Content-Type": "application/json"}, timeout=30)
            print(f"  filterType 2 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    rows = result.get("data", {}).get("rows", [])
                    print(f"  filterType 2 找到 {len(rows)} 条记录")
                    if rows:
                        for row in rows:
                            print(f"    记录ID: {row.get('rowid')}")
                            print(f"    用户字段: {row.get('6888a7c2a849420e13f69e4a')}")
                else:
                    print(f"  filterType 2 失败: {result.get('message', '未知错误')}")
                    
        except Exception as e:
            print(f"  查询用户 {user_rowid} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎊 调试完成！")
    print("\n💡 分析结果:")
    print("   1. 检查是否有记录被成功保存")
    print("   2. 检查用户字段的实际格式")
    print("   3. 确定正确的查询条件")

if __name__ == "__main__":
    debug_mingdao_query()
