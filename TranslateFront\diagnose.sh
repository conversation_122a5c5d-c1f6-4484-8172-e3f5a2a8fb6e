#!/bin/bash

echo "🔍 SMILE TRANS 服务诊断工具"
echo "================================"

# 1. 检查 PM2 状态
echo "1. 📊 PM2 服务状态:"
pm2 status
echo ""

# 2. 检查端口监听
echo "2. 🔌 端口监听状态:"
netstat -tlnp | grep :3000
if [ $? -eq 0 ]; then
    echo "✅ 端口 3000 正在监听"
else
    echo "❌ 端口 3000 未在监听"
fi
echo ""

# 3. 检查进程
echo "3. 🔄 Node.js 进程:"
ps aux | grep node | grep -v grep
echo ""

# 4. 检查防火墙状态
echo "4. 🛡️  防火墙状态:"
if command -v ufw &> /dev/null; then
    echo "Ubuntu/Debian 防火墙 (ufw):"
    sudo ufw status
elif command -v firewall-cmd &> /dev/null; then
    echo "CentOS/RHEL 防火墙 (firewalld):"
    sudo firewall-cmd --list-ports
else
    echo "未检测到常见防火墙工具"
fi
echo ""

# 5. 检查本地连接
echo "5. 🌐 本地连接测试:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000
if [ $? -eq 0 ]; then
    echo "✅ 本地连接正常"
else
    echo "❌ 本地连接失败"
fi
echo ""

# 6. 获取服务器 IP
echo "6. 🌍 服务器网络信息:"
echo "内网 IP: $(hostname -I | awk '{print $1}')"
echo "外网 IP: $(curl -s ifconfig.me 2>/dev/null || echo '无法获取')"
echo ""

# 7. 检查日志
echo "7. 📝 最近的错误日志:"
if [ -f "logs/err.log" ]; then
    echo "PM2 错误日志 (最后10行):"
    tail -10 logs/err.log
else
    echo "PM2 错误日志:"
    pm2 logs translate-front --err --lines 10
fi
echo ""

# 8. 建议的解决方案
echo "8. 💡 建议的解决方案:"
echo ""

if ! netstat -tlnp | grep :3000 > /dev/null; then
    echo "❌ 服务未启动，请运行:"
    echo "   ./fix-and-restart.sh"
    echo ""
fi

echo "🔧 如果服务已启动但无法访问，请检查:"
echo "   1. 防火墙设置:"
echo "      sudo ufw allow 3000  # Ubuntu/Debian"
echo "      sudo firewall-cmd --permanent --add-port=3000/tcp && sudo firewall-cmd --reload  # CentOS/RHEL"
echo ""
echo "   2. 云服务器安全组:"
echo "      在云服务器控制台开放 3000 端口"
echo ""
echo "   3. 查看详细日志:"
echo "      pm2 logs translate-front"
echo ""
echo "   4. 重启服务:"
echo "      pm2 restart translate-front"
echo ""

echo "🌐 访问地址: http://$(hostname -I | awk '{print $1}'):3000"
