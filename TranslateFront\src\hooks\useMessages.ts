import { useState, useCallback } from 'react';
import { MessageType } from '../components/StatusMessage';

export interface Message {
  id: string;
  type: MessageType;
  message: string;
  duration?: number;
}

export const useMessages = () => {
  const [messages, setMessages] = useState<Message[]>([]);

  const addMessage = useCallback((type: MessageType, message: string, duration?: number) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newMessage: Message = {
      id,
      type,
      message,
      duration
    };

    setMessages(prev => [...prev, newMessage]);

    // 自动移除消息
    if (duration !== 0) {
      setTimeout(() => {
        removeMessage(id);
      }, duration || 5000);
    }

    return id;
  }, []);

  const removeMessage = useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // 便捷方法
  const showSuccess = useCallback((message: string, duration?: number) => {
    return addMessage('success', message, duration);
  }, [addMessage]);

  const showError = useCallback((message: string, duration?: number) => {
    return addMessage('error', message, duration);
  }, [addMessage]);

  const showWarning = useCallback((message: string, duration?: number) => {
    return addMessage('warning', message, duration);
  }, [addMessage]);

  const showInfo = useCallback((message: string, duration?: number) => {
    return addMessage('info', message, duration);
  }, [addMessage]);

  return {
    messages,
    addMessage,
    removeMessage,
    clearMessages,
    showSuccess,
    showError,
    showWarning,
    showInfo
  };
};
