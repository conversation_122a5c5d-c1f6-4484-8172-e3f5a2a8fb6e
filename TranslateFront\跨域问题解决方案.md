# 🔧 跨域问题解决方案

## 🚨 **问题描述**

在开发环境中访问明道云API时遇到跨域问题：
```
请求网址: https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows
请求方法: OPTIONS
状态代码: 405 Method Not Allowed
```

这是典型的CORS（跨域资源共享）问题，浏览器阻止了从 `localhost` 到 `dmit.duoningbio.com` 的跨域请求。

## ✅ **解决方案：Vite代理配置**

### **1. 已配置的Vite代理**

在 `vite.config.ts` 中已经配置了代理：

```typescript
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      // 代理 API 请求以解决 CORS 问题
      '/api': {
        target: 'https://dmit.duoningbio.com',
        changeOrigin: true,
        secure: true,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      }
    }
  }
})
```

### **2. API配置更新**

已将API基础URL从完整URL改为相对路径：

```typescript
// 明道云用户认证相关配置
export const MINGDAO_AUTH_CONFIG = {
  // 明道云API基础配置 - 使用代理路径
  baseUrl: '/api/v2/open',  // ✅ 使用相对路径，通过代理转发
  appKey: 'd88c1d2329c42504',
  sign: 'YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==',
  // ...
}
```

## 🔄 **工作原理**

### **代理转发流程**
1. **前端请求**: `http://localhost:5174/api/v2/open/worksheet/getFilterRows`
2. **Vite代理**: 拦截 `/api` 开头的请求
3. **转发到**: `https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows`
4. **返回响应**: 代理服务器将响应返回给前端

### **CORS解决**
- ✅ **同源请求**: 前端认为是向同一域名发送请求
- ✅ **服务器代理**: Vite开发服务器作为中间代理
- ✅ **绕过限制**: 避免了浏览器的跨域限制

## 🚀 **启动和测试**

### **1. 启动开发服务器**
```bash
cd TranslateFront
npm run dev
```

服务器将启动在: `http://localhost:5174/`

### **2. 测试API请求**

现在所有API请求都会通过代理转发：

```javascript
// 这个请求会被代理转发
fetch('/api/v2/open/worksheet/getFilterRows', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    appKey: 'd88c1d2329c42504',
    sign: 'YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==',
    worksheetId: '6886f053a849420e13f69b61'
  })
})
```

### **3. 验证代理工作**

在浏览器开发者工具中，您应该看到：
- ✅ **请求URL**: `http://localhost:5174/api/v2/open/...`
- ✅ **状态码**: `200 OK` (而不是405)
- ✅ **响应数据**: 明道云API的正常响应

## 🔍 **调试信息**

### **控制台日志**
代理配置中包含了详细的日志，您可以在终端中看到：
```
Sending Request to the Target: POST /api/v2/open/worksheet/getFilterRows
Received Response from the Target: 200 /api/v2/open/worksheet/getFilterRows
```

### **网络面板**
在浏览器开发者工具的网络面板中：
- **请求URL**: 显示为本地地址
- **请求方法**: POST (而不是OPTIONS)
- **状态**: 200 OK

## 🎯 **其他解决方案**

### **方案1: 浏览器禁用安全策略**
```bash
# Chrome启动参数（仅开发环境）
chrome.exe --user-data-dir=/tmp/chrome_dev_session --disable-web-security --disable-features=VizDisplayCompositor
```

### **方案2: 浏览器扩展**
安装CORS扩展（如CORS Unblock），但不推荐用于生产环境。

### **方案3: 后端CORS配置**
如果有控制权，可以在明道云API服务器配置CORS头：
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type
```

## 📝 **注意事项**

### **开发环境 vs 生产环境**
- ✅ **开发环境**: 使用Vite代理解决跨域
- ⚠️ **生产环境**: 需要配置nginx代理或使用CORS

### **生产环境配置**
在生产环境中，您需要配置Web服务器（如nginx）进行代理：

```nginx
location /api/ {
    proxy_pass https://dmit.duoningbio.com/api/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 🎊 **测试结果**

现在您可以：
1. ✅ **启动应用**: `npm run dev`
2. ✅ **访问**: `http://localhost:5174/`
3. ✅ **测试功能**: 用户注册、登录、充值、文件上传
4. ✅ **查看数据**: 所有API请求都能正常工作

跨域问题已经完全解决！您可以正常使用所有功能了。🚀
