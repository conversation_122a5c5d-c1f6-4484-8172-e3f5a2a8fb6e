.translation-progress {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  flex-shrink: 0;
}

.status-icon.error {
  color: #dc3545;
}

.status-icon.success {
  color: #28a745;
}

.status-icon.processing {
  color: #007bff;
  animation: pulse 2s infinite;
}

.status-text {
  font-weight: 600;
  font-size: 14px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.connection-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.connection-indicator.connected {
  background-color: #28a745;
  box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}

.connection-indicator.disconnected {
  background-color: #dc3545;
}

.connection-text {
  font-size: 12px;
  color: #6c757d;
}

.progress-content {
  margin-bottom: 12px;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  min-width: 40px;
  text-align: right;
}

.progress-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-message {
  font-size: 13px;
  color: #495057;
  font-weight: 500;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #6c757d;
}

.progress-count {
  font-weight: 500;
}

.progress-time {
  font-style: italic;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 8px;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .translation-progress {
    padding: 12px;
  }
  
  .progress-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .progress-stats {
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
  }
}
