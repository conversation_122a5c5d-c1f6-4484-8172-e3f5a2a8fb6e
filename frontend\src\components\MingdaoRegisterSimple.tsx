import React, { useState } from 'react';
import { User, Mail, Lock, UserPlus, CheckCircle, AlertCircle } from 'lucide-react';
import { createMingdaoUser, checkUsernameExists, checkEmailExists } from '../services/mingdaoApi';

interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface MingdaoRegisterSimpleProps {
  onRegisterSuccess?: (userData: any) => void;
  onClose?: () => void;
}

const MingdaoRegisterSimple: React.FC<MingdaoRegisterSimpleProps> = ({ onRegisterSuccess, onClose }) => {
  const [formData, setFormData] = useState<RegisterFormData>({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);

  const handleInputChange = async (field: keyof RegisterFormData, value: string) => {
    console.log(`📝 输入变化: ${field} = ${value}`);
    setFormData(prev => ({ ...prev, [field]: value }));

    // 实时检查用户名和邮箱是否已存在
    if (field === 'username' && value.length >= 3) {
      setIsCheckingUsername(true);
      try {
        const exists = await checkUsernameExists(value);
        if (exists) {
          setResult({ success: false, message: '用户名已存在，请选择其他用户名' });
        } else {
          setResult(null);
        }
      } catch (error) {
        console.error('检查用户名失败:', error);
      } finally {
        setIsCheckingUsername(false);
      }
    }

    if (field === 'email' && value.includes('@')) {
      setIsCheckingEmail(true);
      try {
        const exists = await checkEmailExists(value);
        if (exists) {
          setResult({ success: false, message: '邮箱已被注册，请使用其他邮箱' });
        } else {
          setResult(null);
        }
      } catch (error) {
        console.error('检查邮箱失败:', error);
      } finally {
        setIsCheckingEmail(false);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🚀 简单注册表单提交', formData);

    // 基本验证
    if (!formData.username || !formData.email || !formData.password) {
      setResult({ success: false, message: '请填写所有必填字段' });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setResult({ success: false, message: '两次输入的密码不一致' });
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      console.log('⏳ 开始真实的明道云注册过程...');

      // 调用真实的明道云API创建用户
      const apiResult = await createMingdaoUser({
        username: formData.username,
        email: formData.email,
        password: formData.password,
        fullName: formData.username
      });

      if (apiResult.success && apiResult.data) {
        console.log('✅ 明道云注册成功:', apiResult.data);

        setResult({
          success: true,
          message: '注册成功！已分配1000字符免费额度，正在登录...'
        });

        setTimeout(() => {
          onRegisterSuccess?.(apiResult.data);
          onClose?.();
        }, 2000);
      } else {
        console.error('❌ 明道云注册失败:', apiResult.error_msg);
        setResult({
          success: false,
          message: apiResult.error_msg || '注册失败，请稍后重试'
        });
      }

    } catch (error) {
      console.error('❌ 注册过程发生错误:', error);

      // 如果是跨域错误，提供解决方案
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setResult({
          success: false,
          message: '网络连接失败，可能是跨域问题。请检查网络连接或联系管理员。'
        });
      } else {
        setResult({
          success: false,
          message: '注册失败，请稍后重试'
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestClick = () => {
    console.log('🧪 测试按钮被点击');
    alert('测试按钮工作正常！');
  };

  return (
    <div style={{
      maxWidth: '400px',
      margin: '0 auto',
      padding: '20px',
      background: 'white',
      borderRadius: '10px',
      boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <h2 style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '10px' }}>
          <UserPlus size={24} />
          创建明道云账号
        </h2>
        <p style={{ color: '#666', margin: 0 }}>注册即可获得1000字符免费翻译额度</p>
      </div>

      {/* 测试按钮 */}
      <button 
        onClick={handleTestClick}
        style={{
          width: '100%',
          padding: '10px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          marginBottom: '15px'
        }}
      >
        🧪 测试按钮点击
      </button>

      {result && (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          padding: '12px',
          borderRadius: '5px',
          marginBottom: '15px',
          background: result.success ? '#d4edda' : '#f8d7da',
          border: `1px solid ${result.success ? '#c3e6cb' : '#f5c6cb'}`,
          color: result.success ? '#155724' : '#721c24'
        }}>
          {result.success ? <CheckCircle size={20} /> : <AlertCircle size={20} />}
          <span>{result.message}</span>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '5px', marginBottom: '5px' }}>
            <User size={16} />
            用户名 *
          </label>
          <input
            type="text"
            value={formData.username}
            onChange={(e) => handleInputChange('username', e.target.value)}
            placeholder="请输入用户名"
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '10px',
              border: '2px solid #e1e5e9',
              borderRadius: '5px',
              fontSize: '14px'
            }}
          />
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '5px', marginBottom: '5px' }}>
            <Mail size={16} />
            邮箱 *
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="请输入邮箱地址"
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '10px',
              border: '2px solid #e1e5e9',
              borderRadius: '5px',
              fontSize: '14px'
            }}
          />
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '5px', marginBottom: '5px' }}>
            <Lock size={16} />
            密码 *
          </label>
          <input
            type="password"
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            placeholder="请输入密码"
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '10px',
              border: '2px solid #e1e5e9',
              borderRadius: '5px',
              fontSize: '14px'
            }}
          />
        </div>

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '5px', marginBottom: '5px' }}>
            <Lock size={16} />
            确认密码 *
          </label>
          <input
            type="password"
            value={formData.confirmPassword}
            onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
            placeholder="请再次输入密码"
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '10px',
              border: '2px solid #e1e5e9',
              borderRadius: '5px',
              fontSize: '14px'
            }}
          />
        </div>

        <div style={{ display: 'flex', gap: '10px' }}>
          <button
            type="submit"
            disabled={isLoading || result?.success}
            style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              padding: '12px',
              background: isLoading ? '#ccc' : (result?.success ? '#28a745' : 'linear-gradient(135deg, #667eea, #764ba2)'),
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              fontSize: '14px',
              fontWeight: '600'
            }}
          >
            {isLoading ? (
              <>
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid rgba(255,255,255,0.3)',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
                创建中...
              </>
            ) : result?.success ? (
              <>
                <CheckCircle size={16} />
                注册成功
              </>
            ) : (
              <>
                <UserPlus size={16} />
                创建账号
              </>
            )}
          </button>

          {onClose && (
            <button
              type="button"
              onClick={onClose}
              disabled={isLoading}
              style={{
                padding: '12px 20px',
                background: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: '600'
              }}
            >
              取消
            </button>
          )}
        </div>
      </form>

      <div style={{
        marginTop: '20px',
        padding: '15px',
        background: '#f8f9fa',
        borderRadius: '5px',
        border: '1px solid #e9ecef'
      }}>
        <h4 style={{ margin: '0 0 10px 0', color: '#333' }}>注册福利</h4>
        <ul style={{ margin: 0, paddingLeft: '20px', color: '#555' }}>
          <li>🎁 免费获得1000字符翻译额度</li>
          <li>📊 详细的使用统计和配额管理</li>
          <li>💳 灵活的付费方式选择</li>
          <li>🔒 企业级数据安全保障</li>
        </ul>
      </div>

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default MingdaoRegisterSimple;
