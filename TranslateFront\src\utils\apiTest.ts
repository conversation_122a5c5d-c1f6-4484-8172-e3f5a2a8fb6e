// API 测试工具函数

import { API_CONFIG } from '../config/api.config';

/**
 * 简单的 API 测试函数
 */
export const testApiConnection = async () => {
  const requestData = {
    appKey: API_CONFIG.appKey,
    sign: API_CONFIG.sign,
    worksheetId: API_CONFIG.worksheetId,
    pageSize: 5,
    pageIndex: 1,
    listType: 0,
    controls: [],
    filters: [], // 不使用任何过滤条件
    sortId: '',
    isAsc: true,
    notGetTotal: false,
    useControlId: false,
    getSystemControl: true
  };

  console.log('测试请求数据:', requestData);

  try {
    const response = await fetch(`${API_CONFIG.baseUrl}/worksheet/getFilterRows`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    console.log('响应状态:', response.status, response.statusText);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('错误响应内容:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    console.log('成功响应:', result);

    return {
      success: true,
      data: result,
      message: '连接成功'
    };
  } catch (error) {
    console.error('API 测试失败:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : '未知错误'
    };
  }
};

/**
 * 测试上传 API
 */
export const testUploadApi = async (testData: any) => {
  const requestData = {
    appKey: API_CONFIG.appKey,
    sign: API_CONFIG.sign,
    worksheetId: API_CONFIG.worksheetId,
    triggerWorkflow: true,
    controls: testData
  };

  console.log('测试上传请求数据:', requestData);

  try {
    const response = await fetch(`${API_CONFIG.baseUrl}/worksheet/addRow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    console.log('上传响应状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('上传错误响应:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    console.log('上传成功响应:', result);

    return {
      success: true,
      data: result,
      message: '上传测试成功'
    };
  } catch (error) {
    console.error('上传测试失败:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : '上传测试失败'
    };
  }
};
