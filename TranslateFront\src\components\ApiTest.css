.api-test {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 20px;
  margin-bottom: 20px;
}

.api-test-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.api-test-header p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #6c757d;
}

.api-test-actions {
  margin-bottom: 20px;
}

.api-test-result {
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid;
}

.api-test-result.success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.api-test-result.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.success-icon {
  color: #28a745;
}

.error-icon {
  color: #dc3545;
}

.result-message {
  font-weight: 500;
}

.result-error {
  margin-bottom: 12px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 14px;
}

.result-data {
  margin-top: 12px;
}

.result-data details {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 8px;
}

.result-data summary {
  cursor: pointer;
  font-weight: 500;
  padding: 4px;
}

.result-data pre {
  margin: 8px 0 0 0;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.api-test-info {
  border-top: 1px solid #dee2e6;
  padding-top: 16px;
}

.api-test-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.api-test-info ul {
  margin: 0;
  padding-left: 20px;
}

.api-test-info li {
  margin-bottom: 4px;
  font-size: 14px;
  color: #6c757d;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
