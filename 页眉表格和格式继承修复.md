# 🔧 页眉表格和格式继承修复

## 🚨 **问题分析**

根据您提供的截图，发现了两个主要问题：

### **问题1：页眉表格没有翻译**
- ✅ **现象**：页眉中的表格（包含"起草部门"、"设备动力科"等内容）没有被翻译
- ✅ **原因**：页眉表格的译文检测逻辑过于严格，误判为已翻译

### **问题2：正文段落首行缩进格式丢失**
- ✅ **现象**：译文段落没有保持原文的首行缩进格式
- ✅ **原因**：格式应用时没有继承原段落的格式设置

## 🔧 **修复内容**

### **1. 简化页眉表格译文检测逻辑**

#### **修复前**
```python
# 更严格的检测：Arial字体 + 6号字（页眉表格译文的特征）
if (run.font.name and 'Arial' in run.font.name and
    run.font.size and run.font.size.pt == 6):
    has_translation = True
```

#### **修复后**
```python
# 检查单元格是否已经有译文（简化检测逻辑）
has_translation = False
cell_text_all = ""
for para in cell.paragraphs:
    cell_text_all += para.text

# 如果单元格包含多个段落，且有Arial字体的内容，可能已经翻译过
if len(cell.paragraphs) > 1:
    for para in cell.paragraphs[1:]:  # 检查第二个段落开始
        if para.text.strip():  # 如果有内容，可能是译文
            has_translation = True
            break
```

### **2. 增强段落格式继承功能**

#### **修复前**
```python
def apply_paragraph_format(paragraph, format_settings):
    """将段落格式设置应用到段落"""
    # 只应用用户设置，不继承原格式
```

#### **修复后**
```python
def apply_paragraph_format(paragraph, format_settings, original_paragraph=None):
    """将段落格式设置应用到段落，同时继承原段落的格式"""
    # 如果有原段落，先继承其格式
    if original_paragraph:
        try:
            # 继承对齐方式
            if original_paragraph.alignment is not None:
                paragraph.alignment = original_paragraph.alignment
            
            # 继承首行缩进
            if original_paragraph.paragraph_format.first_line_indent is not None:
                paragraph.paragraph_format.first_line_indent = original_paragraph.paragraph_format.first_line_indent
            
            # 继承段前间距
            if original_paragraph.paragraph_format.space_before is not None:
                paragraph.paragraph_format.space_before = original_paragraph.paragraph_format.space_before
            
            # 继承段后间距
            if original_paragraph.paragraph_format.space_after is not None:
                paragraph.paragraph_format.space_after = original_paragraph.paragraph_format.space_after
            
            # 继承行间距
            if original_paragraph.paragraph_format.line_spacing is not None:
                paragraph.paragraph_format.line_spacing = original_paragraph.paragraph_format.line_spacing
        except:
            pass  # 如果继承失败，使用默认格式

    # 然后应用用户设置（如果不是inherit）
    text_align = format_settings.get('text_align', 'inherit')
    if text_align != 'inherit':
        # 用户设置覆盖继承的格式
```

### **3. 更新所有格式应用调用**

#### **正文段落**
```python
# 应用用户设置的段落格式，同时继承原段落格式
paragraph_settings = translation_settings.get('paragraph', {})
apply_paragraph_format(new_para, paragraph_settings, paragraph)
```

#### **表格单元格**
```python
# 应用用户设置的表格格式，继承原单元格第一个段落的格式
table_settings = translation_settings.get('table', {})
original_para = cell.paragraphs[0] if len(cell.paragraphs) > 0 else None
apply_paragraph_format(translated_para, table_settings, original_para)
```

#### **页眉段落**
```python
# 应用用户设置的页眉格式，同时继承原段落格式
header_settings = translation_settings.get('header', {})
apply_paragraph_format(new_para, header_settings, paragraph)
```

#### **页眉表格**
```python
# 应用用户设置的页眉表格格式（使用header设置），继承原单元格格式
header_settings = translation_settings.get('header', {})
original_para = cell.paragraphs[0] if len(cell.paragraphs) > 0 else None
apply_paragraph_format(translated_para, header_settings, original_para)
```

### **4. 增加调试日志**

```python
# 页眉表格处理日志
for table_idx, table in enumerate(header.tables):
    logger.info(f"处理页眉表格 {table_idx + 1}，行数: {len(table.rows)}，列数: {len(table.rows[0].cells) if len(table.rows) > 0 else 0}")

# 页眉表格单元格翻译日志
logger.info(f"翻译页眉表格单元格 [{row_idx},{col_idx}]: {original_text[:30]}...")
```

## 🎯 **修复效果**

### **页眉表格翻译**
- ✅ **现在会翻译**：页眉中的表格内容（如"起草部门"、"设备动力科"等）
- ✅ **避免重复**：已翻译的单元格不会重复翻译
- ✅ **保持格式**：继承原表格单元格的格式

### **格式继承**
- ✅ **首行缩进**：译文段落会继承原文的首行缩进
- ✅ **对齐方式**：继承原文的对齐设置
- ✅ **段落间距**：继承原文的段前段后间距
- ✅ **行间距**：继承原文的行间距设置

### **用户设置优先**
- ✅ **覆盖机制**：用户设置的格式会覆盖继承的格式
- ✅ **inherit选项**：当用户选择"inherit"时，完全使用继承的格式
- ✅ **分区域控制**：正文、表格、页眉可以分别设置不同的格式策略

## 🚀 **测试建议**

### **1. 重启服务器**
```bash
cd d:\mywork\Translate
.venv\Scripts\activate
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### **2. 测试页眉表格翻译**
- 上传包含页眉表格的Word文档
- 开始翻译
- 检查页眉表格是否被翻译

### **3. 测试格式继承**
- 上传包含首行缩进的Word文档
- 设置翻译格式为"inherit"
- 检查译文是否保持原文格式

### **4. 查看日志**
```bash
# 查看页眉表格处理日志
grep "处理页眉表格" logs/app.log

# 查看页眉表格单元格翻译日志
grep "翻译页眉表格单元格" logs/app.log
```

## 🎊 **预期结果**

修复后，翻译文档应该：

### **页眉表格**
- ✅ **"起草部门"** → **"Drafting Department"**
- ✅ **"设备动力科"** → **"Equipment Power Section"**
- ✅ **"颁发部门"** → **"Issuing Department"**
- ✅ **"质量保证部"** → **"Quality Assurance Department"**

### **正文格式**
- ✅ **首行缩进**：译文段落保持与原文相同的首行缩进
- ✅ **对齐方式**：保持原文的对齐设置
- ✅ **段落间距**：保持原文的段落间距

### **用户控制**
- ✅ **格式设置**：用户可以通过翻译设置界面控制格式
- ✅ **继承选项**：选择"inherit"时完全继承原文格式
- ✅ **分区域设置**：正文、表格、页眉可以分别设置

现在页眉表格应该能正常翻译，格式也会正确继承了！🎊
