<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明道云API直接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .config-info {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .config-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .config-info code {
            background: #bbdefb;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .pricing-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .pricing-table th,
        .pricing-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .pricing-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 明道云API直接测试</h1>
        
        <div class="config-info">
            <h3>📋 明道云配置信息</h3>
            <p>AppKey: <code>d88c1d2329c42504</code></p>
            <p>API地址: <code>https://dmit.duoningbio.com/api/v2/open</code></p>
            <p>用户表ID: <code>6886e20ba849420e13f69b23</code></p>
            <p>翻译表ID: <code>6886f053a849420e13f69b61</code></p>
        </div>
        
        <!-- 收费规则测试 -->
        <div class="section">
            <h2>💰 收费规则测试</h2>
            <p>测试不同字符数的收费计算：</p>
            <button onclick="testPricing()">测试收费计算</button>
            <div id="pricingResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 获取工作表信息 -->
        <div class="section">
            <h2>📊 获取工作表信息</h2>
            <button onclick="getWorksheetInfo('users')">获取用户表结构</button>
            <button onclick="getWorksheetInfo('translations')">获取翻译表结构</button>
            <button onclick="getWorksheetInfo('consumption_records')">获取消费记录表结构</button>
            <div id="worksheetResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 查询用户数据 -->
        <div class="section">
            <h2>👤 查询用户数据</h2>
            <button onclick="getUserData()">获取用户列表</button>
            <button onclick="searchUser('testuser')">搜索测试用户</button>
            <div id="userResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 查询翻译记录 -->
        <div class="section">
            <h2>📚 查询翻译记录</h2>
            <button onclick="getTranslationData()">获取翻译记录</button>
            <div id="translationResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 网络连接测试 -->
        <div class="section">
            <h2>🌐 网络连接测试</h2>
            <button onclick="testConnection()">测试网络连接</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 明道云配置
        const MINGDAO_CONFIG = {
            appKey: 'd88c1d2329c42504',
            sign: 'YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==',
            baseUrl: 'https://dmit.duoningbio.com/api/v2/open',
            worksheets: {
                users: '6886e20ba849420e13f69b23',
                translations: '6886f053a849420e13f69b61',
                consumption_records: '6886f9ffa849420e13f69bc5'
            }
        };

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        // 收费规则测试
        function testPricing() {
            const testCases = [
                { chars: 1000, desc: '小文档' },
                { chars: 5000, desc: '边界值' },
                { chars: 8000, desc: '中等文档' },
                { chars: 15000, desc: '大文档' },
                { chars: 300000, desc: '批量优惠' }
            ];

            const results = testCases.map(testCase => {
                const chars = testCase.chars;
                let cost, type, description;

                if (chars <= 5000) {
                    cost = 5;
                    type = '按次付费';
                    description = `${chars}字符，按次收费`;
                } else if (chars >= 300000) {
                    cost = 100;
                    type = '批量优惠';
                    description = `${chars}字符，批量优惠`;
                } else {
                    cost = Math.ceil(chars / 5000) * 5;
                    type = '按次付费';
                    description = `${chars}字符，分段收费`;
                }

                return {
                    chars,
                    cost,
                    type,
                    description,
                    testDesc: testCase.desc
                };
            });

            showResult('pricingResult', {
                message: '收费规则测试结果',
                rules: {
                    monthly: '20元/月，10万字额度',
                    small: '5000字以内5元一次',
                    large: '超过5000字，每5000字5元',
                    bulk: '30万字100元'
                },
                testResults: results
            });
        }

        // 获取工作表信息
        async function getWorksheetInfo(worksheetName) {
            const worksheetId = MINGDAO_CONFIG.worksheets[worksheetName] || worksheetName;
            
            const data = {
                appKey: MINGDAO_CONFIG.appKey,
                sign: MINGDAO_CONFIG.sign,
                worksheetId: worksheetId
            };

            try {
                const response = await fetch(`${MINGDAO_CONFIG.baseUrl}/worksheet/getWorksheetInfo`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    const worksheetData = result.data;
                    const summary = {
                        worksheetName: worksheetData.name,
                        worksheetId: worksheetData.worksheetId,
                        fieldsCount: worksheetData.controls.length,
                        fields: worksheetData.controls.map(control => ({
                            id: control.controlId,
                            name: control.controlName,
                            type: control.type,
                            required: control.required
                        }))
                    };
                    showResult('worksheetResult', summary);
                } else {
                    showResult('worksheetResult', result, true);
                }
            } catch (error) {
                showResult('worksheetResult', { error: error.message }, true);
            }
        }

        // 获取用户数据
        async function getUserData() {
            const data = {
                appKey: MINGDAO_CONFIG.appKey,
                sign: MINGDAO_CONFIG.sign,
                worksheetId: MINGDAO_CONFIG.worksheets.users,
                pageSize: 10,
                pageIndex: 1,
                listType: 0,
                controls: [],
                filters: []
            };

            try {
                const response = await fetch(`${MINGDAO_CONFIG.baseUrl}/worksheet/getFilterRows`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                showResult('userResult', result, !result.success);
            } catch (error) {
                showResult('userResult', { error: error.message }, true);
            }
        }

        // 搜索用户
        async function searchUser(username) {
            const data = {
                appKey: MINGDAO_CONFIG.appKey,
                sign: MINGDAO_CONFIG.sign,
                worksheetId: MINGDAO_CONFIG.worksheets.users,
                pageSize: 10,
                pageIndex: 1,
                listType: 0,
                controls: [],
                filters: [
                    {
                        controlId: '6886e20ba849420e13f69b24', // 用户名字段ID
                        dataType: 2,
                        spliceType: 1,
                        filterType: 1,
                        value: username
                    }
                ]
            };

            try {
                const response = await fetch(`${MINGDAO_CONFIG.baseUrl}/worksheet/getFilterRows`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                showResult('userResult', result, !result.success);
            } catch (error) {
                showResult('userResult', { error: error.message }, true);
            }
        }

        // 获取翻译记录
        async function getTranslationData() {
            const data = {
                appKey: MINGDAO_CONFIG.appKey,
                sign: MINGDAO_CONFIG.sign,
                worksheetId: MINGDAO_CONFIG.worksheets.translations,
                pageSize: 10,
                pageIndex: 1,
                listType: 0,
                controls: [],
                filters: []
            };

            try {
                const response = await fetch(`${MINGDAO_CONFIG.baseUrl}/worksheet/getFilterRows`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                showResult('translationResult', result, !result.success);
            } catch (error) {
                showResult('translationResult', { error: error.message }, true);
            }
        }

        // 测试网络连接
        async function testConnection() {
            const startTime = Date.now();
            
            try {
                const response = await fetch('https://dmit.duoningbio.com/api/v2/open/worksheet/getWorksheetInfo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        appKey: MINGDAO_CONFIG.appKey,
                        sign: MINGDAO_CONFIG.sign,
                        worksheetId: 'test'
                    })
                });

                const endTime = Date.now();
                const duration = endTime - startTime;

                const result = {
                    status: 'connected',
                    responseTime: `${duration}ms`,
                    statusCode: response.status,
                    timestamp: new Date().toISOString(),
                    message: '网络连接正常，可以访问明道云API'
                };

                showResult('connectionResult', result);
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;

                const result = {
                    status: 'failed',
                    responseTime: `${duration}ms`,
                    error: error.message,
                    timestamp: new Date().toISOString(),
                    message: '网络连接失败，无法访问明道云API'
                };

                showResult('connectionResult', result, true);
            }
        }

        // 页面加载完成后自动测试收费规则
        window.addEventListener('load', function() {
            console.log('🚀 明道云API测试页面已加载');
            testPricing();
        });
    </script>
</body>
</html>
