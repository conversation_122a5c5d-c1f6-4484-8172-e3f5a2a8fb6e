"""
文件处理工具函数
"""
import os
import uuid
import mimetypes
from typing import Tuple, List, Optional
from pathlib import Path
from docx import Document
from docx.table import Table
from docx.text.paragraph import Paragraph
from fastapi import UploadFile, HTTPException, status

from app.config import settings
from app.utils.logger import logger


def validate_file(file: UploadFile) -> None:
    """验证上传的文件"""
    # 检查文件大小
    if file.size and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
        )
    
    # 检查文件扩展名
    if file.filename:
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in settings.allowed_extensions_list:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Allowed types: {', '.join(settings.allowed_extensions_list)}"
            )


def generate_unique_filename(original_filename: str) -> str:
    """生成唯一的文件名"""
    file_ext = Path(original_filename).suffix
    unique_id = str(uuid.uuid4())
    return f"{unique_id}{file_ext}"


async def save_uploaded_file(file: UploadFile) -> Tuple[str, str]:
    """保存上传的文件"""
    # 验证文件
    validate_file(file)
    
    # 生成唯一文件名
    filename = generate_unique_filename(file.filename)
    file_path = os.path.join(settings.upload_dir, filename)
    
    # 确保上传目录存在
    os.makedirs(settings.upload_dir, exist_ok=True)
    
    # 保存文件
    try:
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.info(f"File saved: {filename}")
        return filename, file_path
    except Exception as e:
        logger.error(f"Error saving file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save file"
        )


def get_file_mime_type(filename: str) -> str:
    """获取文件的 MIME 类型"""
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type or "application/octet-stream"


def extract_docx_content(file_path: str) -> dict:
    """提取 DOCX 文件内容"""
    try:
        doc = Document(file_path)
        
        paragraphs = []
        tables = []
        total_characters = 0
        
        # 提取段落
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:  # 只保存非空段落
                paragraphs.append(text)
                total_characters += len(text)
        
        # 提取表格
        for table in doc.tables:
            table_data = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_data.append(cell_text)
                    total_characters += len(cell_text)
                table_data.append(row_data)
            tables.append(table_data)
        
        # 合并所有文本内容
        all_text = "\n".join(paragraphs)
        for table in tables:
            for row in table:
                all_text += "\n" + "\t".join(row)
        
        return {
            "paragraphs": paragraphs,
            "tables": tables,
            "total_characters": total_characters,
            "total_paragraphs": len(paragraphs),
            "extracted_text": all_text
        }
    
    except Exception as e:
        logger.error(f"Error extracting DOCX content: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to extract document content. Please ensure the file is a valid DOCX document."
        )


def delete_file(file_path: str) -> bool:
    """删除文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"File deleted: {file_path}")
            return True
        return False
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        return False


def get_file_size(file_path: str) -> int:
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except Exception:
        return 0


def file_exists(file_path: str) -> bool:
    """检查文件是否存在"""
    return os.path.exists(file_path) and os.path.isfile(file_path)
