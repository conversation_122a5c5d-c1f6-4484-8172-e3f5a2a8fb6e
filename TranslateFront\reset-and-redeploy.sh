#!/bin/bash

echo "🔄 完全重置并重新部署 SMILE TRANS"
echo "=================================="

# 1. 停止所有 PM2 进程
echo "1. ⏹️  停止所有 PM2 进程..."
pm2 delete all 2>/dev/null || echo "没有运行的 PM2 进程"

# 2. 备份当前目录（可选）
echo "2. 💾 备份当前目录..."
BACKUP_DIR="../TranslateFront-backup-$(date +%Y%m%d-%H%M%S)"
cp -r . "$BACKUP_DIR" 2>/dev/null && echo "已备份到: $BACKUP_DIR" || echo "备份跳过"

# 3. 获取当前目录路径
CURRENT_DIR=$(pwd)
PARENT_DIR=$(dirname "$CURRENT_DIR")
PROJECT_NAME=$(basename "$CURRENT_DIR")

echo "当前目录: $CURRENT_DIR"
echo "父目录: $PARENT_DIR"
echo "项目名: $PROJECT_NAME"

# 4. 删除当前代码（保留 .git 目录）
echo "3. 🗑️  清理当前代码..."
find . -maxdepth 1 ! -name '.' ! -name '..' ! -name '.git' -exec rm -rf {} + 2>/dev/null || echo "清理完成"

# 5. 重新拉取代码
echo "4. 📥 重新拉取最新代码..."
git reset --hard HEAD
git clean -fd
git pull origin main

# 6. 安装依赖
echo "5. 📦 安装依赖..."
npm install

# 7. 给脚本执行权限
echo "6. 🔑 设置脚本执行权限..."
chmod +x *.sh 2>/dev/null || echo "没有脚本文件需要设置权限"

# 8. 创建必要目录
echo "7. 📁 创建必要目录..."
mkdir -p logs

# 9. 构建项目
echo "8. 🔨 构建项目..."
npm run build

# 10. 启动服务
echo "9. 🚀 启动服务..."
if [ -f "ecosystem.config.cjs" ]; then
    pm2 start ecosystem.config.cjs
elif [ -f "start-production.sh" ]; then
    ./start-production.sh
else
    echo "使用基本方式启动..."
    pm2 start server.cjs --name translate-front
fi

# 11. 等待服务启动
echo "10. ⏳ 等待服务启动..."
sleep 5

# 12. 检查服务状态
echo "11. 📊 检查服务状态..."
pm2 status

# 13. 测试服务
echo "12. 🧪 测试服务..."
if netstat -tlnp | grep :3000 > /dev/null; then
    echo "✅ 端口 3000 正在监听"
    
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ HTTP 服务正常 (状态码: $HTTP_CODE)"
    else
        echo "⚠️  HTTP 响应异常 (状态码: $HTTP_CODE)"
    fi
else
    echo "❌ 端口 3000 未在监听"
    echo "查看错误日志:"
    pm2 logs translate-front --err --lines 10
fi

# 14. 配置防火墙
echo "13. 🛡️  配置防火墙..."
if command -v ufw &> /dev/null; then
    sudo ufw allow 3000 2>/dev/null && echo "✅ Ubuntu/Debian 防火墙已配置" || echo "⚠️  防火墙配置需要手动执行"
elif command -v firewall-cmd &> /dev/null; then
    sudo firewall-cmd --permanent --add-port=3000/tcp 2>/dev/null && sudo firewall-cmd --reload 2>/dev/null && echo "✅ CentOS/RHEL 防火墙已配置" || echo "⚠️  防火墙配置需要手动执行"
fi

# 15. 保存 PM2 配置
echo "14. 💾 保存 PM2 配置..."
pm2 save

# 16. 设置开机自启
echo "15. 🔄 设置开机自启..."
pm2 startup 2>/dev/null || echo "开机自启设置需要手动执行"

# 17. 显示结果
echo ""
echo "🎉 重新部署完成！"
echo "=================="

SERVER_IP=$(hostname -I | awk '{print $1}')
EXTERNAL_IP=$(curl -s ifconfig.me 2>/dev/null || echo "无法获取")

echo ""
echo "📊 服务状态:"
pm2 list

echo ""
echo "🌐 访问地址:"
echo "   内网: http://${SERVER_IP}:3000"
if [ "$EXTERNAL_IP" != "无法获取" ]; then
    echo "   外网: http://${EXTERNAL_IP}:3000"
fi

echo ""
echo "📝 常用命令:"
echo "   查看状态: pm2 status"
echo "   查看日志: pm2 logs translate-front"
echo "   查看错误: pm2 logs translate-front --err"
echo "   重启服务: pm2 restart translate-front"
echo "   停止服务: pm2 stop translate-front"

echo ""
echo "🔧 如果仍无法访问，请检查:"
echo "   1. 云服务器安全组是否开放 3000 端口"
echo "   2. 运行诊断: ./diagnose.sh (如果存在)"
echo "   3. 查看详细日志: pm2 logs translate-front"

echo ""
echo "✅ 现在你的服务应该可以在断开 SSH 连接后继续运行了！"
