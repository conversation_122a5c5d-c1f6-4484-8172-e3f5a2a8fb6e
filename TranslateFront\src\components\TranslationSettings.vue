import React, { useState, useEffect, useMemo } from 'react';
import { TranslationSettings, DEFAULT_TRANSLATION_SETTINGS, TranslationSettingsUtils } from '../types/translation';
import './TranslationSettings.css';

interface TranslationSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  onSettingsChange: (settings: TranslationSettings) => void;
}

const TranslationSettingsComponent: React.FC<TranslationSettingsProps> = ({
  isOpen,
  onClose,
  onSettingsChange
}) => {
  const [settings, setSettings] = useState<TranslationSettings>({ ...DEFAULT_TRANSLATION_SETTINGS });

  // 预览样式
  const previewStyle = useMemo(() => {
    const style: React.CSSProperties = {
      fontFamily: settings.fontFamily,
      fontSize: `${settings.fontSize}pt`,
      fontWeight: settings.bold ? 'bold' : 'normal',
      fontStyle: settings.italic ? 'italic' : 'normal',
      textDecoration: settings.underline ? 'underline' : 'none',
      color: settings.color,
      backgroundColor: settings.backgroundColor,
      marginTop: `${settings.marginTop}pt`,
      transition: 'all 0.3s ease'
    };

    if (settings.textAlign !== 'inherit') {
      style.textAlign = settings.textAlign as any;
    }

    if (settings.lineHeight !== 'inherit') {
      style.lineHeight = settings.lineHeight;
    }

    if (settings.textIndent > 0) {
      style.textIndent = `${settings.textIndent}em`;
    }

    return style;
  }, [settings]);

  // 更新设置
  const updateSetting = <K extends keyof TranslationSettings>(
    key: K,
    value: TranslationSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // 恢复默认设置
  const resetToDefault = () => {
    setSettings({ ...DEFAULT_TRANSLATION_SETTINGS });
  };

  // 保存设置
  const saveSettings = () => {
    TranslationSettingsUtils.saveSettings(settings);
    onSettingsChange(settings);
    alert('翻译格式设置已保存！');
  };

  // 加载保存的设置
  useEffect(() => {
    const loadedSettings = TranslationSettingsUtils.loadSettings();
    setSettings(loadedSettings);
  }, []);

  if (!isOpen) return null;

  return (
    <div className="translation-settings-overlay">
      <div className="translation-settings">
        <div className="settings-header">
          <h3>🎨 翻译格式设置</h3>
          <button className="close-btn" onClick={onClose}>✕</button>
          <p className="settings-description">
            自定义译文的显示格式，译文将先继承原文格式，再应用您的设置
          </p>
        </div>

        <div className="settings-content">
          {/* 字体设置 */}
          <div className="setting-group">
            <label className="setting-label">📝 字体设置</label>
            <div className="setting-row">
              <div className="setting-item">
                <label>字体名称</label>
                <select
                  value={settings.fontFamily}
                  onChange={(e) => updateSetting('fontFamily', e.target.value)}
                >
                  <option value="Arial">Arial</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Calibri">Calibri</option>
                  <option value="宋体">宋体</option>
                  <option value="微软雅黑">微软雅黑</option>
                  <option value="黑体">黑体</option>
                </select>
              </div>
              <div className="setting-item">
                <label>字体大小</label>
                <div className="input-with-unit">
                  <input
                    type="number"
                    value={settings.fontSize}
                    min="6"
                    max="72"
                    step="0.5"
                    onChange={(e) => updateSetting('fontSize', parseFloat(e.target.value))}
                  />
                  <span className="unit">pt</span>
                </div>
              </div>
            </div>
          </div>

          {/* 字体样式 */}
          <div className="setting-group">
            <label className="setting-label">✨ 字体样式</label>
            <div className="setting-row">
              <div className="setting-item checkbox-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings.bold}
                    onChange={(e) => updateSetting('bold', e.target.checked)}
                  />
                  <strong>粗体</strong>
                </label>
              </div>
              <div className="setting-item checkbox-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings.italic}
                    onChange={(e) => updateSetting('italic', e.target.checked)}
                  />
                  <em>斜体</em>
                </label>
              </div>
              <div className="setting-item checkbox-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings.underline}
                    onChange={(e) => updateSetting('underline', e.target.checked)}
                  />
                  <u>下划线</u>
                </label>
              </div>
            </div>
          </div>

          {/* 段落格式 */}
          <div className="setting-group">
            <label className="setting-label">📐 段落格式</label>
            <div className="setting-row">
              <div className="setting-item">
                <label>对齐方式</label>
                <select
                  value={settings.textAlign}
                  onChange={(e) => updateSetting('textAlign', e.target.value)}
                >
                  <option value="inherit">跟随原文</option>
                  <option value="left">左对齐</option>
                  <option value="center">居中</option>
                  <option value="right">右对齐</option>
                  <option value="justify">两端对齐</option>
                </select>
              </div>
              <div className="setting-item">
                <label>首行缩进</label>
                <div className="input-with-unit">
                  <input
                    type="number"
                    value={settings.textIndent}
                    min="0"
                    max="10"
                    step="0.5"
                    onChange={(e) => updateSetting('textIndent', parseFloat(e.target.value))}
                  />
                  <span className="unit">字符</span>
                </div>
              </div>
            </div>
          </div>

          {/* 间距设置 */}
          <div className="setting-group">
            <label className="setting-label">📏 间距设置</label>
            <div className="setting-row">
              <div className="setting-item">
                <label>行间距</label>
                <select
                  value={settings.lineHeight}
                  onChange={(e) => updateSetting('lineHeight', e.target.value)}
                >
                  <option value="inherit">跟随原文</option>
                  <option value="1">单倍行距</option>
                  <option value="1.15">1.15倍行距</option>
                  <option value="1.5">1.5倍行距</option>
                  <option value="2">双倍行距</option>
                </select>
              </div>
              <div className="setting-item">
                <label>段前间距</label>
                <div className="input-with-unit">
                  <input
                    type="number"
                    value={settings.marginTop}
                    min="0"
                    max="50"
                    step="1"
                    onChange={(e) => updateSetting('marginTop', parseFloat(e.target.value))}
                  />
                  <span className="unit">pt</span>
                </div>
              </div>
            </div>
          </div>

          {/* 颜色设置 */}
          <div className="setting-group">
            <label className="setting-label">🎨 颜色设置</label>
            <div className="setting-row">
              <div className="setting-item">
                <label>文字颜色</label>
                <input
                  type="color"
                  value={settings.color}
                  onChange={(e) => updateSetting('color', e.target.value)}
                  className="color-picker"
                />
              </div>
              <div className="setting-item">
                <label>背景颜色</label>
                <input
                  type="color"
                  value={settings.backgroundColor}
                  onChange={(e) => updateSetting('backgroundColor', e.target.value)}
                  className="color-picker"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 实时预览 */}
        <div className="preview-section">
          <h4>📖 效果预览</h4>
          <div className="preview-container">
            <div className="preview-original">
              <p className="original-text">
                原文示例：这是一段中文原文，用于展示原文的格式效果。
              </p>
            </div>
            <div className="preview-translated">
              <p className="translated-text" style={previewStyle}>
                译文示例：This is an English translation example to show the translation format effect.
              </p>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="settings-actions">
          <button onClick={resetToDefault} className="btn-secondary">
            🔄 恢复默认
          </button>
          <button onClick={saveSettings} className="btn-primary">
            💾 保存设置
          </button>
        </div>
      </div>
    </div>
  );
};

export default TranslationSettingsComponent;

      <!-- 字体样式 -->
      <div class="setting-group">
        <label class="setting-label">✨ 字体样式</label>
        <div class="setting-row">
          <div class="setting-item checkbox-item">
            <label>
              <input type="checkbox" v-model="settings.bold" @change="updatePreview" />
              <strong>粗体</strong>
            </label>
          </div>
          <div class="setting-item checkbox-item">
            <label>
              <input type="checkbox" v-model="settings.italic" @change="updatePreview" />
              <em>斜体</em>
            </label>
          </div>
          <div class="setting-item checkbox-item">
            <label>
              <input type="checkbox" v-model="settings.underline" @change="updatePreview" />
              <u>下划线</u>
            </label>
          </div>
        </div>
      </div>

      <!-- 段落格式 -->
      <div class="setting-group">
        <label class="setting-label">📐 段落格式</label>
        <div class="setting-row">
          <div class="setting-item">
            <label>对齐方式</label>
            <select v-model="settings.textAlign" @change="updatePreview">
              <option value="inherit">跟随原文</option>
              <option value="left">左对齐</option>
              <option value="center">居中</option>
              <option value="right">右对齐</option>
              <option value="justify">两端对齐</option>
            </select>
          </div>
          <div class="setting-item">
            <label>首行缩进</label>
            <input 
              type="number" 
              v-model.number="settings.textIndent" 
              min="0" 
              max="10" 
              step="0.5"
              @input="updatePreview"
            />
            <span class="unit">字符</span>
          </div>
        </div>
      </div>

      <!-- 间距设置 -->
      <div class="setting-group">
        <label class="setting-label">📏 间距设置</label>
        <div class="setting-row">
          <div class="setting-item">
            <label>行间距</label>
            <select v-model="settings.lineHeight" @change="updatePreview">
              <option value="inherit">跟随原文</option>
              <option value="1">单倍行距</option>
              <option value="1.15">1.15倍行距</option>
              <option value="1.5">1.5倍行距</option>
              <option value="2">双倍行距</option>
            </select>
          </div>
          <div class="setting-item">
            <label>段前间距</label>
            <input 
              type="number" 
              v-model.number="settings.marginTop" 
              min="0" 
              max="50" 
              step="1"
              @input="updatePreview"
            />
            <span class="unit">pt</span>
          </div>
        </div>
      </div>

      <!-- 颜色设置 -->
      <div class="setting-group">
        <label class="setting-label">🎨 颜色设置</label>
        <div class="setting-row">
          <div class="setting-item">
            <label>文字颜色</label>
            <input 
              type="color" 
              v-model="settings.color" 
              @change="updatePreview"
              class="color-picker"
            />
          </div>
          <div class="setting-item">
            <label>背景颜色</label>
            <input 
              type="color" 
              v-model="settings.backgroundColor" 
              @change="updatePreview"
              class="color-picker"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 实时预览 -->
    <div class="preview-section">
      <h4>📖 效果预览</h4>
      <div class="preview-container">
        <div class="preview-original">
          <p class="original-text">原文示例：这是一段中文原文，用于展示原文的格式效果。</p>
        </div>
        <div class="preview-translated">
          <p class="translated-text" :style="previewStyle">
            译文示例：This is an English translation example to show the translation format effect.
          </p>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="settings-actions">
      <button @click="resetToDefault" class="btn-secondary">
        🔄 恢复默认
      </button>
      <button @click="saveSettings" class="btn-primary">
        💾 保存设置
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { TranslationSettingsUtils } from '../types/translation'

// 翻译设置接口
interface TranslationSettings {
  fontFamily: string
  fontSize: number
  bold: boolean
  italic: boolean
  underline: boolean
  textAlign: string
  textIndent: number
  lineHeight: string
  marginTop: number
  color: string
  backgroundColor: string
}

// 默认设置
const defaultSettings: TranslationSettings = {
  fontFamily: 'Arial',
  fontSize: 10.5,
  bold: false,
  italic: false,
  underline: false,
  textAlign: 'inherit',
  textIndent: 0,
  lineHeight: 'inherit',
  marginTop: 0,
  color: '#000000',
  backgroundColor: '#ffffff'
}

// 当前设置
const settings = ref<TranslationSettings>({ ...defaultSettings })

// 预览样式
const previewStyle = computed(() => {
  const style: any = {
    fontFamily: settings.value.fontFamily,
    fontSize: `${settings.value.fontSize}pt`,
    fontWeight: settings.value.bold ? 'bold' : 'normal',
    fontStyle: settings.value.italic ? 'italic' : 'normal',
    textDecoration: settings.value.underline ? 'underline' : 'none',
    color: settings.value.color,
    backgroundColor: settings.value.backgroundColor,
    marginTop: `${settings.value.marginTop}pt`
  }

  if (settings.value.textAlign !== 'inherit') {
    style.textAlign = settings.value.textAlign
  }

  if (settings.value.lineHeight !== 'inherit') {
    style.lineHeight = settings.value.lineHeight
  }

  if (settings.value.textIndent > 0) {
    style.textIndent = `${settings.value.textIndent}em`
  }

  return style
})

// 更新预览
const updatePreview = () => {
  // 预览会自动通过computed更新
}

// 恢复默认设置
const resetToDefault = () => {
  settings.value = { ...defaultSettings }
  updatePreview()
}

// 保存设置
const saveSettings = async () => {
  try {
    // 使用工具类保存到后端和localStorage
    const success = await TranslationSettingsUtils.saveSettings(settings.value)

    // 触发事件通知父组件
    emit('settingsChanged', settings.value)

    // 显示成功提示
    if (success) {
      alert('翻译格式设置已保存到云端！')
    } else {
      alert('翻译格式设置已保存到本地，但云端保存失败。请检查网络连接。')
    }
  } catch (error) {
    console.error('保存设置失败:', error)
    alert('保存设置失败，请重试')
  }
}

// 加载保存的设置
const loadSettings = async () => {
  try {
    const loadedSettings = await TranslationSettingsUtils.loadSettings()
    settings.value = loadedSettings
  } catch (error) {
    console.error('加载设置失败:', error)
    // 使用同步版本作为备用
    const fallbackSettings = TranslationSettingsUtils.loadSettingsSync()
    settings.value = fallbackSettings
  }
}

// 定义事件
const emit = defineEmits<{
  settingsChanged: [settings: TranslationSettings]
}>()

// 组件挂载时加载设置
onMounted(async () => {
  await loadSettings()
})

// 导出设置供父组件使用
defineExpose({
  getSettings: () => settings.value,
  setSettings: (newSettings: TranslationSettings) => {
    settings.value = { ...newSettings }
  }
})
</script>

<style scoped>
.translation-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-header {
  text-align: center;
  margin-bottom: 30px;
}

.settings-header h3 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.settings-description {
  color: #7f8c8d;
  font-size: 14px;
}

.setting-group {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.setting-label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
}

.setting-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.setting-item {
  flex: 1;
  min-width: 200px;
}

.setting-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #34495e;
}

.setting-item input,
.setting-item select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.setting-item input:focus,
.setting-item select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.unit {
  margin-left: 8px;
  color: #7f8c8d;
  font-size: 12px;
}

.checkbox-item {
  flex: 0 0 auto;
  min-width: auto;
}

.checkbox-item label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-item input {
  width: auto;
  margin-right: 8px;
}

.color-picker {
  width: 60px !important;
  height: 40px;
  padding: 2px;
  border-radius: 4px;
  cursor: pointer;
}

.preview-section {
  margin: 30px 0;
  padding: 20px;
  background: #f1f2f6;
  border-radius: 8px;
}

.preview-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.preview-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.preview-original,
.preview-translated {
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e1e8ed;
}

.original-text {
  margin: 0;
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 14pt;
  line-height: 1.5;
  color: #2c3e50;
}

.translated-text {
  margin: 0;
  line-height: 1.5;
  transition: all 0.3s ease;
}

.settings-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .preview-container {
    grid-template-columns: 1fr;
  }
  
  .setting-row {
    flex-direction: column;
  }
  
  .setting-item {
    min-width: auto;
  }
}
</style>
