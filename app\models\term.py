"""
术语管理模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.database import Base


class TermCategory(Base):
    """术语分类模型"""
    __tablename__ = "term_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    terms = relationship("Term", back_populates="category")
    
    def __repr__(self):
        return f"<TermCategory(id={self.id}, name='{self.name}')>"


class Term(Base):
    """专业术语模型"""
    __tablename__ = "terms"
    
    id = Column(Integer, primary_key=True, index=True)
    category_id = Column(Integer, ForeignKey("term_categories.id"), nullable=True)
    
    # 术语内容
    source_term = Column(String(200), nullable=False, index=True)
    target_term = Column(String(200), nullable=False, index=True)
    source_language = Column(String(10), nullable=False)
    target_language = Column(String(10), nullable=False)
    
    # 术语信息
    definition = Column(Text, nullable=True)  # 术语定义
    context = Column(Text, nullable=True)  # 使用上下文
    notes = Column(Text, nullable=True)  # 备注
    
    # 状态和质量
    is_active = Column(Boolean, default=True)
    confidence_level = Column(Integer, default=5)  # 1-10 置信度等级
    usage_count = Column(Integer, default=0)  # 使用次数
    
    # 创建者信息
    created_by = Column(String(100), nullable=True)
    approved_by = Column(String(100), nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    category = relationship("TermCategory", back_populates="terms")
    
    # 创建复合索引以提高查询性能
    __table_args__ = (
        Index('idx_term_languages', 'source_language', 'target_language'),
        Index('idx_source_term_lang', 'source_term', 'source_language'),
        Index('idx_target_term_lang', 'target_term', 'target_language'),
    )
    
    def __repr__(self):
        return f"<Term(id={self.id}, source='{self.source_term}', target='{self.target_term}')>"
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1


class TermImportHistory(Base):
    """术语导入历史记录"""
    __tablename__ = "term_import_histories"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 导入信息
    filename = Column(String(255), nullable=False)
    total_terms = Column(Integer, default=0)
    imported_terms = Column(Integer, default=0)
    failed_terms = Column(Integer, default=0)
    
    # 导入状态
    status = Column(String(50), default="pending")  # pending, completed, failed
    error_message = Column(Text, nullable=True)
    
    # 导入者信息
    imported_by = Column(String(100), nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<TermImportHistory(id={self.id}, filename='{self.filename}', status='{self.status}')>"
