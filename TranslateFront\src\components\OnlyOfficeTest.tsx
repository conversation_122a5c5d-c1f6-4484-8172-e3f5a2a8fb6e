import React, { useState, useRef, useEffect } from 'react';
import { PREVIEW_CONFIG } from '../config/api.config';
import { signOnlyOfficeConfig } from '../utils/jwt';

// 声明全局 DocsAPI
declare global {
  interface Window {
    DocsAPI: any;
  }
}

const OnlyOfficeTest: React.FC = () => {
  const [testStatus, setTestStatus] = useState<string>('未开始');
  const [apiLoaded, setApiLoaded] = useState<boolean>(false);
  const [editorCreated, setEditorCreated] = useState<boolean>(false);
  const [logs, setLogs] = useState<string[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[OnlyOffice Test] ${message}`);
  };

  // 测试 OnlyOffice 服务器连接
  const testServerConnection = async () => {
    setTestStatus('测试服务器连接...');
    addLog('开始测试服务器连接');

    try {
      await fetch(`${PREVIEW_CONFIG.onlyoffice.serverUrl}healthcheck`, {
        method: 'GET',
        mode: 'no-cors' // 避免 CORS 问题
      });
      addLog('服务器连接成功');
      return true;
    } catch (error) {
      addLog(`服务器连接失败: ${error}`);
      return false;
    }
  };

  // 测试 OnlyOffice API 脚本加载
  const loadOnlyOfficeAPI = () => {
    return new Promise<void>((resolve, reject) => {
      // 检查是否已经加载
      if (window.DocsAPI) {
        addLog('API 已经加载');
        setApiLoaded(true);
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `${PREVIEW_CONFIG.onlyoffice.serverUrl}web-apps/apps/api/documents/api.js`;

      script.onload = () => {
        addLog('API 脚本加载成功');
        setApiLoaded(true);
        resolve();
      };

      script.onerror = () => {
        addLog('API 脚本加载失败');
        setApiLoaded(false);
        reject(new Error('Failed to load OnlyOffice API'));
      };

      document.head.appendChild(script);
    });
  };

  // 测试 API 脚本加载
  const testApiLoad = async () => {
    setTestStatus('测试 API 加载...');
    addLog('开始加载 OnlyOffice API');

    try {
      await loadOnlyOfficeAPI();
      return true;
    } catch (error) {
      return false;
    }
  };

  // 测试创建编辑器
  const testEditorCreation = async () => {
    setTestStatus('测试编辑器创建...');
    addLog('开始创建编辑器');

    if (!window.DocsAPI) {
      addLog('DocsAPI 不可用');
      return false;
    }

    if (!containerRef.current) {
      addLog('容器元素不可用');
      return false;
    }

    try {
      // 清空容器
      containerRef.current.innerHTML = '';

      // 使用一个简单的测试文档配置
      const baseConfig = {
        document: {
          fileType: 'docx',
          key: 'test_document_key_' + Date.now(),
          title: 'test.docx',
          url: 'https://file-examples.com/storage/fe86c96b3d0b0b1c0b8b8b1/2017/10/file_example_DOCX_10kB.docx'
        },
        documentType: 'word',
        editorConfig: {
          mode: 'view',
          lang: 'zh-CN',
          customization: {
            autosave: false,
            chat: false,
            comments: false,
            help: false,
            hideRightMenu: true,
            plugins: false,
            review: false,
            toolbar: false,
            zoom: 100
          }
        },
        height: '400px',
        width: '100%',
        events: {
          onAppReady: () => {
            addLog('✅ 编辑器应用准备就绪');
          },
          onDocumentReady: () => {
            addLog('✅ 文档加载完成');
            setEditorCreated(true);
          },
          onError: (event: any) => {
            addLog(`❌ 编辑器错误: ${JSON.stringify(event)}`);
          },
          onInfo: (event: any) => {
            addLog(`ℹ️ 信息: ${JSON.stringify(event)}`);
          },
          onWarning: (event: any) => {
            addLog(`⚠️ 警告: ${JSON.stringify(event)}`);
          },
          onRequestEditRights: () => {
            addLog('📝 请求编辑权限');
          },
          onRequestHistory: () => {
            addLog('📜 请求历史记录');
          }
        }
      };

      // 如果启用了 JWT，则对配置进行签名
      const config = await signOnlyOfficeConfig(baseConfig);

      addLog(`创建编辑器配置: ${JSON.stringify(config, null, 2)}`);
      
      editorRef.current = new window.DocsAPI.DocEditor(containerRef.current, config);
      addLog('编辑器实例创建成功');
      return true;
    } catch (error) {
      addLog(`创建编辑器失败: ${error}`);
      return false;
    }
  };

  // 运行完整测试
  const runFullTest = async () => {
    setLogs([]);
    setTestStatus('开始测试...');
    addLog('=== OnlyOffice 服务测试开始 ===');

    // 1. 测试服务器连接
    const serverOk = await testServerConnection();
    if (!serverOk) {
      setTestStatus('服务器连接失败');
      return;
    }

    // 2. 测试 API 加载
    const apiOk = await testApiLoad();
    if (!apiOk) {
      setTestStatus('API 加载失败');
      return;
    }

    // 等待一下确保 API 完全加载
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 3. 测试编辑器创建
    const editorOk = await testEditorCreation();
    if (!editorOk) {
      setTestStatus('编辑器创建失败');
      return;
    }

    setTestStatus('测试完成');
    addLog('=== OnlyOffice 服务测试完成 ===');
  };

  // 测试你的文档
  const testWithYourDocument = async () => {
    setLogs([]);
    setTestStatus('测试你的文档...');
    addLog('=== 测试你的文档开始 ===');

    try {
      await loadOnlyOfficeAPI();

      if (containerRef.current && window.DocsAPI) {
        containerRef.current.innerHTML = '';

        const baseConfig = {
          document: {
            fileType: 'docx',
            key: 'your_document_key_' + Date.now(),
            title: '你的文档.docx',
            url: 'https://dmit.duoningbio.com/file/mdoc/3e25a501-d7ee-4f3f-825b-a4e807ece34c/1b7d0bf2-bda4-4f32-b5cc-beb4f939b8ac/68789ddfa849420e13f65dc2/20250721/76co724faGbJ4R0x7Qf02Kfp8jeVefei4xfV3qcz9F7u6306dZ3z597p4xeSfG1m.docx?e=1753073588&token=mdstorage:oB7hW7cJRt5wyYLuEW2kbCWgj88='
          },
          documentType: 'word',
          editorConfig: {
            mode: 'view',
            lang: 'zh-CN',
            customization: {
              autosave: false,
              chat: false,
              comments: false,
              help: false,
              hideRightMenu: true,
              plugins: false,
              review: false,
              toolbar: false,
              zoom: 100
            }
          },
          height: '400px',
          width: '100%',
          events: {
            onAppReady: () => {
              addLog('✅ 编辑器应用准备就绪');
            },
            onDocumentReady: () => {
              addLog('✅ 文档加载完成');
              setEditorCreated(true);
            },
            onError: (event: any) => {
              addLog(`❌ 编辑器错误: ${JSON.stringify(event)}`);
            },
            onInfo: (event: any) => {
              addLog(`ℹ️ 信息: ${JSON.stringify(event)}`);
            },
            onWarning: (event: any) => {
              addLog(`⚠️ 警告: ${JSON.stringify(event)}`);
            }
          }
        };

        const config = await signOnlyOfficeConfig(baseConfig);
        addLog(`使用你的文档配置: ${JSON.stringify(config, null, 2)}`);

        editorRef.current = new window.DocsAPI.DocEditor(containerRef.current, config);
        addLog('编辑器实例创建成功');
      }
    } catch (error) {
      addLog(`测试失败: ${error}`);
    }
  };

  // 测试本地文档（从你的 OnlyOffice 服务器提供）
  const testWithLocalDocument = async () => {
    setLogs([]);
    setTestStatus('测试本地文档...');
    addLog('=== 测试本地文档开始 ===');

    try {
      await loadOnlyOfficeAPI();

      if (containerRef.current && window.DocsAPI) {
        containerRef.current.innerHTML = '';

        // 使用一个简单的本地文档URL（不需要复杂的token）
        const baseConfig = {
          document: {
            fileType: 'docx',
            key: 'local_document_key_' + Date.now(),
            title: '本地测试文档.docx',
            url: 'http://10.250.200.231:8088/example.docx' // 假设你在 OnlyOffice 服务器上放了一个测试文档
          },
          documentType: 'word',
          editorConfig: {
            mode: 'view',
            lang: 'zh-CN',
            customization: {
              autosave: false,
              chat: false,
              comments: false,
              help: false,
              hideRightMenu: true,
              plugins: false,
              review: false,
              toolbar: false,
              zoom: 100
            }
          },
          height: '400px',
          width: '100%',
          events: {
            onAppReady: () => {
              addLog('✅ 编辑器应用准备就绪');
            },
            onDocumentReady: () => {
              addLog('✅ 文档加载完成');
              setEditorCreated(true);
            },
            onError: (event: any) => {
              addLog(`❌ 编辑器错误: ${JSON.stringify(event)}`);
            },
            onInfo: (event: any) => {
              addLog(`ℹ️ 信息: ${JSON.stringify(event)}`);
            },
            onWarning: (event: any) => {
              addLog(`⚠️ 警告: ${JSON.stringify(event)}`);
            }
          }
        };

        const config = await signOnlyOfficeConfig(baseConfig);
        addLog(`使用本地文档配置: ${JSON.stringify(config, null, 2)}`);

        editorRef.current = new window.DocsAPI.DocEditor(containerRef.current, config);
        addLog('编辑器实例创建成功');
      }
    } catch (error) {
      addLog(`测试失败: ${error}`);
    }
  };

  // 检查文档访问
  const checkDocumentAccess = async () => {
    setLogs([]);
    setTestStatus('检查文档访问...');
    addLog('=== 检查文档访问开始 ===');

    const testUrls = [
      'http://10.250.200.231:8088/example.docx',
      'https://file-examples.com/storage/fe86c96b3d0b0b1c0b8b8b1/2017/10/file_example_DOCX_10kB.docx',
      'https://dmit.duoningbio.com/file/mdoc/3e25a501-d7ee-4f3f-825b-a4e807ece34c/1b7d0bf2-bda4-4f32-b5cc-beb4f939b8ac/68789ddfa849420e13f65dc2/20250721/76co724faGbJ4R0x7Qf02Kfp8jeVefei4xfV3qcz9F7u6306dZ3z597p4xeSfG1m.docx?e=1753094595&token=mdstorage:ZwqUZz7KotTfgfp3C871dr04-cs='
    ];

    for (const url of testUrls) {
      try {
        addLog(`🔍 检查: ${url}`);
        const response = await fetch(url, {
          method: 'HEAD',
          mode: 'no-cors' // 避免 CORS 问题
        });
        addLog(`✅ 响应: ${response.status} ${response.statusText}`);
      } catch (error) {
        addLog(`❌ 错误: ${error}`);
      }
    }

    addLog('=== 文档访问检查完成 ===');
  };

  // 清理函数
  useEffect(() => {
    return () => {
      if (editorRef.current && editorRef.current.destroyEditor) {
        editorRef.current.destroyEditor();
      }
    };
  }, []);

  return (
    <div style={{ padding: '20px', maxWidth: '1000px', margin: '0 auto' }}>
      <h2>OnlyOffice 服务测试</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <p><strong>服务器地址:</strong> {PREVIEW_CONFIG.onlyoffice.serverUrl}</p>
        <p><strong>测试状态:</strong> {testStatus}</p>
        <p><strong>API 加载状态:</strong> {apiLoaded ? '✅ 已加载' : '❌ 未加载'}</p>
        <p><strong>编辑器状态:</strong> {editorCreated ? '✅ 已创建' : '❌ 未创建'}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={runFullTest}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          开始测试（公开文档）
        </button>

        <button
          onClick={() => testWithYourDocument()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          测试你的文档
        </button>

        <button
          onClick={() => testWithLocalDocument()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#ffc107',
            color: 'black',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          测试本地文档
        </button>

        <button
          onClick={() => checkDocumentAccess()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          检查文档访问
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>测试日志:</h3>
        <div style={{
          height: '200px',
          overflow: 'auto',
          border: '1px solid #ccc',
          padding: '10px',
          backgroundColor: '#f5f5f5',
          fontFamily: 'monospace',
          fontSize: '12px'
        }}>
          {logs.map((log, index) => (
            <div key={index}>{log}</div>
          ))}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>编辑器测试区域:</h3>
        <div
          ref={containerRef}
          style={{
            height: '400px',
            border: '1px solid #ccc',
            backgroundColor: '#fff',
            position: 'relative'
          }}
        >
          {!editorCreated && (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: '#666'
            }}>
              <div>等待编辑器加载...</div>
              <div style={{ fontSize: '12px', marginTop: '8px' }}>
                如果长时间没有响应，请检查浏览器控制台错误
              </div>
            </div>
          )}
        </div>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <h4>故障排除提示:</h4>
        <ul>
          <li>确保 OnlyOffice Document Server 正在运行</li>
          <li>检查服务器地址是否正确: <code>{PREVIEW_CONFIG.onlyoffice.serverUrl}</code></li>
          <li>确保服务器可以从浏览器访问（没有防火墙阻止）</li>
          <li>检查浏览器控制台是否有 CORS 错误</li>
          <li>确保 OnlyOffice 服务器配置允许你的域名访问</li>
        </ul>
      </div>
    </div>
  );
};

export default OnlyOfficeTest;
