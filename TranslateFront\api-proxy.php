<?php
/**
 * 简单的 API 代理脚本
 * 用于解决前端 CORS 问题，无需 Node.js
 */

// 设置 CORS 头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Authorization');

// 处理 OPTIONS 预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

// 明道云 API 配置
$API_BASE_URL = 'https://dmit.duoningbio.com/api/v2/open';

// 获取请求路径
$path = $_GET['path'] ?? '';
if (empty($path)) {
    http_response_code(400);
    echo json_encode(['error' => '缺少 path 参数']);
    exit;
}

// 构建完整的 API URL
$api_url = $API_BASE_URL . '/' . ltrim($path, '/');

// 获取请求方法
$method = $_SERVER['REQUEST_METHOD'];

// 获取请求体
$input = file_get_contents('php://input');

// 设置 cURL 选项
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_CUSTOMREQUEST => $method,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json',
        'User-Agent: TranslateFront-Proxy/1.0'
    ]
]);

// 如果是 POST 请求，添加请求体
if ($method === 'POST' && !empty($input)) {
    curl_setopt($ch, CURLOPT_POSTFIELDS, $input);
}

// 执行请求
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// 检查错误
if ($error) {
    http_response_code(500);
    echo json_encode(['error' => 'cURL 错误: ' . $error]);
    exit;
}

// 设置响应状态码
http_response_code($http_code);

// 设置响应头
header('Content-Type: application/json');

// 输出响应
echo $response;
?>
