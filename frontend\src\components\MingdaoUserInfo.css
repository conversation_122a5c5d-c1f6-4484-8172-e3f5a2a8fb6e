.mingdao-user-info {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 占位符 */
.user-info-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.user-info-placeholder svg {
  margin-bottom: 15px;
  color: #ccc;
}

/* 用户基本信息 */
.user-basic-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.user-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.user-details h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.2rem;
}

.user-email {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 0.9rem;
}

.user-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.user-type-badge,
.user-status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.user-type-badge {
  background: #e3f2fd;
  color: #1976d2;
}

.user-status-badge {
  background: #e8f5e8;
  color: #2e7d32;
}

.user-status-badge.disabled {
  background: #ffebee;
  color: #c62828;
}

.user-status-badge.pending {
  background: #fff3e0;
  color: #f57c00;
}

.refresh-btn {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.2s;
}

.refresh-btn:hover {
  background: #e9ecef;
}

/* 账户余额 */
.account-balance {
  background: linear-gradient(135deg, #4caf50, #45a049);
  color: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
}

.balance-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-size: 0.9rem;
  opacity: 0.9;
}

.balance-amount {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.recharge-btn {
  background: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.3);
  color: white;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.2s;
}

.recharge-btn:hover {
  background: rgba(255,255,255,0.3);
}

/* 配额部分 */
.quota-section,
.monthly-quota-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.quota-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  font-weight: 600;
  color: #495057;
}

.expire-badge {
  margin-left: auto;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
  background: #e3f2fd;
  color: #1976d2;
}

.expire-badge.warning {
  background: #fff3e0;
  color: #f57c00;
}

.quota-progress {
  margin-bottom: 10px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.quota-text {
  font-size: 0.9rem;
  color: #495057;
  margin-bottom: 4px;
}

.quota-percent {
  font-size: 0.8rem;
  color: #6c757d;
}

/* 使用统计 */
.usage-stats {
  margin-bottom: 20px;
}

.usage-stats h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-label {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #333;
}

/* 账户信息 */
.account-info {
  margin-bottom: 20px;
}

.account-info h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1rem;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 0.9rem;
  color: #333;
  font-weight: 600;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

/* 警告信息 */
.quota-warning,
.expire-warning {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.quota-warning {
  background: #fff3e0;
  border: 1px solid #ffcc02;
  color: #e65100;
}

.expire-warning {
  background: #e3f2fd;
  border: 1px solid #2196f3;
  color: #0d47a1;
}

.quota-warning svg,
.expire-warning svg {
  flex-shrink: 0;
  margin-top: 2px;
}

.quota-warning h4,
.expire-warning h4 {
  margin: 0 0 5px 0;
  font-size: 0.9rem;
}

.quota-warning p,
.expire-warning p {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mingdao-user-info {
    padding: 15px;
  }
  
  .user-basic-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .user-avatar {
    width: 50px;
    height: 50px;
  }
  
  .balance-amount {
    font-size: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-value {
    max-width: 100%;
    text-align: left;
  }
  
  .quota-warning,
  .expire-warning {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .user-badges {
    flex-direction: column;
    align-items: flex-start;
  }
}
