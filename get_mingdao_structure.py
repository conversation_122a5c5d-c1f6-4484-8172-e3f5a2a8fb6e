"""
获取明道云表单结构
"""
import requests
import json

# 明道云配置
MINGDAO_CONFIG = {
    "appKey": "d88c1d2329c42504",
    "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
    "baseUrl": "https://dmit.duoningbio.com/api/v2/open"
}

def get_worksheet_info(worksheet_id):
    """获取工作表结构信息"""
    url = f"{MINGDAO_CONFIG['baseUrl']}/worksheet/getWorksheetInfo"
    
    data = {
        "appKey": MINGDAO_CONFIG["appKey"],
        "sign": MINGDAO_CONFIG["sign"],
        "worksheetId": worksheet_id
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        result = response.json()
        
        print(f"\n=== 工作表 {worksheet_id} 结构信息 ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            worksheet_data = result.get("data", {})
            print(f"\n工作表名称: {worksheet_data.get('name')}")
            print(f"工作表ID: {worksheet_data.get('worksheetId')}")
            
            controls = worksheet_data.get("controls", [])
            print(f"\n字段列表 ({len(controls)} 个字段):")
            for control in controls:
                print(f"  - {control.get('controlName')} (ID: {control.get('controlId')}, 类型: {control.get('type')})")
        
        return result
        
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 获取明道云表单结构")
    
    # 已知的工作表ID
    worksheets = {
        "users": "用户表",
        "translations": "翻译记录表",
        "recharge_records": "充值记录表",
        "consumption_records": "消费记录表",
        "fywd": "翻译文档表(旧)",
        "sfjl": "收费记录表(旧)"
    }
    
    for worksheet_id, name in worksheets.items():
        print(f"\n{'='*50}")
        print(f"正在获取 {name} ({worksheet_id}) 的结构...")
        get_worksheet_info(worksheet_id)

if __name__ == "__main__":
    main()
