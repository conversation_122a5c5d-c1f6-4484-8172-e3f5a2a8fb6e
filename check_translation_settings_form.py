#!/usr/bin/env python3
"""
查看明道云翻译设置表单详情
"""

import requests
import json

def check_translation_settings_form():
    """查看翻译设置表单的详情"""

    url = "https://dmit.duoningbio.com/api/v2/open/worksheet/getWorksheetInfo"

    data = {
        "appKey": "d88c1d2329c42504",
        "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
        "worksheetId": "yhfygssz"
    }

    try:
        print(f"🔍 请求URL: {url}")
        print(f"📤 请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        print(f"📊 响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 响应成功!")
            print(f"📋 响应数据:")
            print("=" * 80)
            print(json.dumps(result, indent=2, ensure_ascii=False))
            print("=" * 80)

            # 解析字段信息
            if 'data' in result and 'controls' in result['data']:
                controls = result['data']['controls']
                print(f"\n📝 表单字段分析:")
                print(f"字段总数: {len(controls)}")
                print("-" * 60)

                for i, control in enumerate(controls):
                    control_name = control.get('controlName', '未知字段')
                    control_id = control.get('controlId', '未知ID')
                    control_type = control.get('type', '未知类型')

                    print(f"{i+1:2d}. 字段名: {control_name}")
                    print(f"    字段ID: {control_id}")
                    print(f"    字段类型: {control_type}")

                    # 如果是选择类型，显示选项
                    if 'options' in control and control['options']:
                        print(f"    选项:")
                        for j, option in enumerate(control['options']):
                            option_text = option.get('value', '未知选项')
                            print(f"      {j+1}. {option_text}")

                    print()

        else:
            print(f"❌ 请求失败!")
            print(f"状态码: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_translation_settings_form()
