.recharge-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.recharge-modal {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 模态框头部 */
.recharge-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  border-radius: 15px 15px 0 0;
}

.recharge-modal-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.close-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

/* 用户信息区域 */
.user-info-section {
  padding: 20px 25px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.balance-info {
  display: flex;
  gap: 30px;
  justify-content: center;
}

.balance-item {
  text-align: center;
}

.balance-item .label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.balance-item .value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

/* 结果消息 */
.result-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 25px;
  font-size: 0.9rem;
  font-weight: 500;
}

.result-message.success {
  background: #d4edda;
  color: #155724;
}

.result-message.error {
  background: #f8d7da;
  color: #721c24;
}

/* 充值表单 */
.recharge-form {
  padding: 25px;
}

.form-section {
  margin-bottom: 30px;
}

.form-section h3 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* 充值类型选择 */
.type-options {
  display: flex;
  gap: 15px;
}

.type-option {
  flex: 1;
  cursor: pointer;
}

.type-option input[type="radio"] {
  display: none;
}

.type-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  transition: all 0.3s;
  background: white;
}

.type-option input[type="radio"]:checked + .type-card {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.05);
}

.type-info {
  flex: 1;
}

.type-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.type-description {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
}

/* 金额选择 */
.amount-options {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.amount-btn {
  position: relative;
  padding: 12px 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.amount-btn:hover {
  border-color: #ff6b35;
}

.amount-btn.active {
  border-color: #ff6b35;
  background: #ff6b35;
  color: white;
}

.amount-btn.popular::after {
  content: "推荐";
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4d4f;
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 8px;
}

.custom-amount {
  margin-top: 15px;
}

.custom-amount label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.custom-amount input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
}

/* 支付方式 */
.payment-methods {
  display: flex;
  gap: 15px;
}

.payment-method {
  flex: 1;
  cursor: pointer;
}

.payment-method input[type="radio"] {
  display: none;
}

.method-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  transition: all 0.3s;
  background: white;
}

.payment-method input[type="radio"]:checked + .method-card {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.05);
}

/* 表单操作 */
.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 25px;
}

.recharge-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 20px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.recharge-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.recharge-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.records-btn {
  padding: 14px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 充值记录 */
.records-section {
  padding: 20px 25px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.record-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-amount {
  font-weight: 700;
  color: #333;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.record-type {
  font-size: 0.9rem;
  color: #333;
}

.record-time {
  font-size: 0.8rem;
  color: #666;
}

.record-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.record-status.success {
  background: #d4edda;
  color: #155724;
}

.record-status.pending {
  background: #fff3cd;
  color: #856404;
}

.record-status.failed {
  background: #f8d7da;
  color: #721c24;
}

.no-records {
  text-align: center;
  color: #666;
  padding: 20px;
}

/* 充值说明 */
.recharge-info {
  padding: 20px 25px;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
}

.recharge-info h4 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1rem;
}

.recharge-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recharge-info li {
  font-size: 0.9rem;
  color: #555;
  padding: 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .type-options,
  .payment-methods {
    flex-direction: column;
  }
  
  .amount-options {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .balance-info {
    gap: 20px;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
