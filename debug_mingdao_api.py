#!/usr/bin/env python3
"""
调试明道云API连接
"""

import requests
import json
import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_mingdao_connection():
    """测试明道云API连接"""
    
    print("🔍 测试明道云API连接")
    print("=" * 60)
    
    # 测试数据
    test_user_rowid = "e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa"
    test_settings = {
        "paragraph": {
            "font_family": "微软雅黑",
            "font_size": 12,
            "bold": False,
            "italic": False,
            "underline": False,
            "text_align": "inherit",
            "color": "#000000"
        },
        "table": {
            "font_family": "宋体",
            "font_size": 8,
            "bold": True,
            "italic": False,
            "underline": False,
            "text_align": "center",
            "color": "#333333"
        },
        "header": {
            "font_family": "黑体",
            "font_size": 14,
            "bold": True,
            "italic": False,
            "underline": True,
            "text_align": "center",
            "color": "#000080"
        },
        "enable_paragraph": True,
        "enable_table": True,
        "enable_header": True
    }
    
    # 1. 测试直接调用明道云服务
    print("\n1️⃣ 测试明道云服务类")
    try:
        from app.services.mingdao_service import mingdao_service
        
        # 测试保存设置
        print("📤 保存翻译设置...")
        success = await mingdao_service.save_user_translation_settings(test_user_rowid, test_settings)
        print(f"保存结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 测试读取设置
            print("📥 读取翻译设置...")
            retrieved_settings = await mingdao_service.get_user_translation_settings(test_user_rowid)
            if retrieved_settings:
                print("✅ 读取成功:")
                print(json.dumps(retrieved_settings, indent=2, ensure_ascii=False))
            else:
                print("❌ 读取失败")
        
    except Exception as e:
        print(f"❌ 明道云服务测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 2. 测试API接口
    print("\n2️⃣ 测试API接口")
    try:
        base_url = "http://localhost:8000"
        
        # 测试保存API
        print("📤 测试保存API...")
        response = requests.post(
            f"{base_url}/api/translation-settings/{test_user_rowid}",
            json=test_settings,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ API保存成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ API保存失败: {response.text}")
        
        # 测试读取API
        print("\n📥 测试读取API...")
        response = requests.get(f"{base_url}/api/translation-settings/{test_user_rowid}")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ API读取成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ API读取失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器，请确保服务器已启动")
    except Exception as e:
        print(f"❌ API测试失败: {e}")
    
    # 3. 直接测试明道云原始API
    print("\n3️⃣ 测试明道云原始API")
    try:
        # 测试创建记录
        url = "https://dmit.duoningbio.com/api/v2/open/worksheet/addRow"
        
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        settings_json = json.dumps(test_settings, ensure_ascii=False)
        
        data = {
            "appKey": "d88c1d2329c42504",
            "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
            "worksheetId": "yhfygssz",
            "controls": [
                {
                    "controlId": "6888a7c2a849420e13f69e4a",  # 用户
                    "value": test_user_rowid
                },
                {
                    "controlId": "6888a761a849420e13f69e40",  # 翻译设置JSON
                    "value": settings_json
                },
                {
                    "controlId": "6888a7c2a849420e13f69e4c",  # 创建时间
                    "value": current_time
                },
                {
                    "controlId": "6888a7c2a849420e13f69e4d",  # 更新时间
                    "value": current_time
                }
            ]
        }
        
        print("📤 直接调用明道云API...")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        print("响应结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            print("✅ 明道云API调用成功！")
        else:
            print(f"❌ 明道云API调用失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 明道云原始API测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎊 调试完成！")

if __name__ == "__main__":
    asyncio.run(test_mingdao_connection())
