@echo off
echo 测试明道云API
echo.

echo 1. 测试创建翻译设置记录
curl -X POST "https://dmit.duoningbio.com/api/v2/open/worksheet/addRow" ^
  -H "Content-Type: application/json" ^
  -d "{\"appKey\": \"d88c1d2329c42504\", \"sign\": \"YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==\", \"worksheetId\": \"yhfygssz\", \"controls\": [{\"controlId\": \"6888a7c2a849420e13f69e4a\", \"value\": \"e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa\"}, {\"controlId\": \"6888a761a849420e13f69e40\", \"value\": \"{\\\"paragraph\\\": {\\\"font_family\\\": \\\"微软雅黑\\\", \\\"font_size\\\": 12}, \\\"table\\\": {\\\"font_family\\\": \\\"宋体\\\", \\\"font_size\\\": 8}, \\\"header\\\": {\\\"font_family\\\": \\\"黑体\\\", \\\"font_size\\\": 14}, \\\"enable_paragraph\\\": true, \\\"enable_table\\\": true, \\\"enable_header\\\": true}\"}, {\"controlId\": \"6888a7c2a849420e13f69e4c\", \"value\": \"2025-07-29 19:30:00\"}, {\"controlId\": \"6888a7c2a849420e13f69e4d\", \"value\": \"2025-07-29 19:30:00\"}]}"

echo.
echo.
echo 2. 测试查询翻译设置记录
curl -X POST "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows" ^
  -H "Content-Type: application/json" ^
  -d "{\"appKey\": \"d88c1d2329c42504\", \"sign\": \"YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==\", \"worksheetId\": \"yhfygssz\", \"pageSize\": 10, \"pageIndex\": 1, \"filters\": [{\"controlId\": \"6888a7c2a849420e13f69e4a\", \"dataType\": 29, \"spliceType\": 1, \"filterType\": 2, \"value\": \"e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa\"}]}"

echo.
echo 测试完成！
