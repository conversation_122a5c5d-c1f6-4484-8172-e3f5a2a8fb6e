@echo off
echo ========================================
echo 启动完整翻译服务
echo ========================================

echo.
echo 1. 激活虚拟环境并启动后端服务...
start "后端服务" cmd /k "cd /d d:\mywork\Translate && .venv\Scripts\activate && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

echo.
echo 等待后端服务启动...
timeout /t 8 /nobreak

echo.
echo 2. 启动前端服务...
start "前端服务" cmd /k "cd /d d:\mywork\Translate\TranslateFront && npm run dev"

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo 后端服务: http://localhost:8000
echo 前端服务: http://localhost:5174
echo API文档: http://localhost:8000/docs
echo ========================================
echo.
echo 现在可以测试完整的翻译功能了！
echo 1. 访问前端: http://localhost:5174
echo 2. 登录用户账号
echo 3. 选择翻译语言
echo 4. 上传文档进行翻译
echo.
echo 按任意键关闭此窗口...
pause >nul
