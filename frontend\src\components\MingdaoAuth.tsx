import React, { useState } from 'react';
import { X, Cloud } from 'lucide-react';
import MingdaoLogin from './MingdaoLogin';
import MingdaoRegister from './MingdaoRegister';
import MingdaoRegisterSimple from './MingdaoRegisterSimple';
import './MingdaoAuth.css';

interface MingdaoAuthProps {
  isOpen: boolean;
  onClose: () => void;
  onAuthSuccess: (userData: any) => void;
  initialMode?: 'login' | 'register';
}

const MingdaoAuth: React.FC<MingdaoAuthProps> = ({
  isOpen,
  onClose,
  onAuthSuccess,
  initialMode = 'login'
}) => {
  const [currentMode, setCurrentMode] = useState<'login' | 'register'>(initialMode);

  if (!isOpen) return null;

  const handleAuthSuccess = (userData: any) => {
    onAuthSuccess(userData);
    onClose();
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="mingdao-auth-overlay" onClick={handleOverlayClick}>
      <div className="mingdao-auth-modal">
        {/* 模态框头部 */}
        <div className="auth-modal-header">
          <div className="auth-modal-title">
            <Cloud size={24} />
            <span>明道云翻译系统</span>
          </div>
          <button className="close-btn" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        {/* 模式切换标签 */}
        <div className="auth-mode-tabs">
          <button
            className={`mode-tab ${currentMode === 'login' ? 'active' : ''}`}
            onClick={() => setCurrentMode('login')}
          >
            登录
          </button>
          <button
            className={`mode-tab ${currentMode === 'register' ? 'active' : ''}`}
            onClick={() => setCurrentMode('register')}
          >
            注册
          </button>
        </div>

        {/* 内容区域 */}
        <div className="auth-content">
          {currentMode === 'login' ? (
            <MingdaoLogin
              onLoginSuccess={handleAuthSuccess}
              onClose={onClose}
              onSwitchToRegister={() => setCurrentMode('register')}
            />
          ) : (
            <MingdaoRegisterSimple
              onRegisterSuccess={handleAuthSuccess}
              onClose={onClose}
            />
          )}
        </div>

        {/* 底部信息 */}
        <div className="auth-modal-footer">
          <div className="footer-info">
            <p>
              <strong>明道云翻译系统特色：</strong>
            </p>
            <div className="feature-highlights">
              <span>☁️ 云端存储</span>
              <span>🔒 企业级安全</span>
              <span>💰 灵活收费</span>
              <span>📱 跨设备同步</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MingdaoAuth;
