"""
直接测试翻译功能
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.azure_translator import AzureTranslatorService

async def test_direct_translation():
    """直接测试 Azure 翻译"""
    print("🔍 直接测试 Azure 翻译功能")
    print("=" * 50)
    
    # 创建翻译器实例
    translator = AzureTranslatorService()
    
    # 测试文本
    test_texts = [
        "制定目的",
        "建立厂房技术资料档案，加强技术资料档案管理。",
        "适用范围",
        "本公司厂房的技术资料档案。"
    ]
    
    print(f"测试文本数量: {len(test_texts)}")
    for i, text in enumerate(test_texts, 1):
        print(f"  {i}. {text}")
    
    print("\n开始翻译...")
    
    try:
        # 调用翻译
        results = await translator.translate_text(
            texts=test_texts,
            target_language='en',
            source_language='zh'
        )
        
        print(f"\n✅ 翻译完成！结果数量: {len(results)}")
        print("=" * 50)
        
        # 显示结果
        for i, (original, result) in enumerate(zip(test_texts, results), 1):
            if 'translations' in result and len(result['translations']) > 0:
                translated = result['translations'][0]['text']
                print(f"{i}. 原文: {original}")
                print(f"   译文: {translated}")
            else:
                print(f"{i}. 原文: {original}")
                print(f"   译文: [翻译失败]")
            print()
            
    except Exception as e:
        print(f"❌ 翻译失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_direct_translation())
