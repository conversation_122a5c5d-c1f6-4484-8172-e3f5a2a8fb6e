.file-upload {
  width: 100%;
}

.upload-zone {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.upload-zone:hover {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.upload-zone.drag-active {
  border-color: #007bff;
  background-color: #e3f2fd;
  transform: scale(1.02);
}

.upload-icon {
  color: #6c757d;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #495057;
  margin-bottom: 8px;
}

.upload-link {
  color: #007bff;
  text-decoration: underline;
}

.upload-hint {
  font-size: 12px;
  color: #6c757d;
  margin: 0;
}

.file-list {
  margin-top: 20px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.file-list h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.file-item.error {
  border-color: #dc3545;
  background-color: #f8d7da;
}

.file-item.success {
  border-color: #28a745;
  background-color: #d4edda;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.file-size {
  font-size: 12px;
  color: #6c757d;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #dc3545;
}

.btn-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: #dc3545;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-remove:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-remove:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-btn {
  width: 100%;
  margin-top: 16px;
  padding: 12px;
  font-size: 16px;
}
