.file-upload {
  width: 100%;
}

.upload-zone {
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 12px;
  padding: 32px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(102, 126, 234, 0.05);
}

.upload-zone:hover {
  border-color: rgba(102, 126, 234, 0.6);
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.upload-zone.drag-active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.15);
  transform: scale(1.02);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.upload-icon {
  color: #667eea;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #2d3748;
  margin-bottom: 8px;
  font-weight: 500;
}

.upload-link {
  color: #667eea;
  text-decoration: underline;
  font-weight: 600;
}

.upload-hint {
  font-size: 12px;
  color: #6c757d;
  margin: 0;
}

.file-list {
  margin-top: 20px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.file-list h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.file-item.error {
  border-color: #dc3545;
  background-color: #f8d7da;
}

.file-item.success {
  border-color: #28a745;
  background-color: #d4edda;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.file-size {
  font-size: 12px;
  color: #6c757d;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #dc3545;
}

.btn-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: #dc3545;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-remove:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-remove:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-btn {
  width: 100%;
  margin-top: 16px;
  padding: 12px;
  font-size: 16px;
}

/* 翻译设置样式 */
.translation-settings {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.translation-settings h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.language-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.language-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.language-label {
  font-weight: 500;
  color: #495057;
}

.language-pair {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

.language-description {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}
