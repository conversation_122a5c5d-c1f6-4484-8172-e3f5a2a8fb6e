import React from 'react';
import { Info, FileText, Languages, AlertTriangle } from 'lucide-react';

const DataStructureInfo: React.FC = () => {
  return (
    <div className="data-structure-info">
      <div className="info-header">
        <h3>
          <Info size={16} />
          数据结构说明
        </h3>
      </div>

      <div className="structure-section">
        <h4>字段映射变化</h4>
        <div className="mapping-table">
          <div className="mapping-row header">
            <span>字段名</span>
            <span>说明</span>
            <span>文件类型</span>
          </div>
          <div className="mapping-row">
            <span className="field-name">YWJ</span>
            <span>原文件</span>
            <span className="file-type original">
              <FileText size={14} />
              原始上传文件
            </span>
          </div>
          <div className="mapping-row">
            <span className="field-name">FYWJ</span>
            <span>翻译文件</span>
            <span className="file-type translated">
              <Languages size={14} />
              翻译后文件
            </span>
          </div>
        </div>
      </div>

      <div className="structure-section">
        <h4>预览功能状态</h4>
        <div className="preview-status">
          <div className="status-item">
            <AlertTriangle size={16} className="warning-icon" />
            <div className="status-content">
              <strong>预览功能暂时保留</strong>
              <p>预览地址格式已保留，等待确认新的预览方案</p>
            </div>
          </div>
        </div>
      </div>

      <div className="structure-section">
        <h4>功能说明</h4>
        <ul className="feature-list">
          <li>
            <strong>文件上传：</strong>上传的文件会保存到 YWJ 字段（原文件）
          </li>
          <li>
            <strong>翻译服务：</strong>后台翻译服务会将翻译后的文件保存到 FYWJ 字段
          </li>
          <li>
            <strong>预览按钮：</strong>只有当 FYWJ 字段有翻译文件时才显示预览按钮
          </li>
          <li>
            <strong>下载功能：</strong>可以分别下载原文件和翻译文件
          </li>
        </ul>
      </div>

      <div className="structure-section">
        <h4>示例数据结构</h4>
        <details className="example-data">
          <summary>查看示例数据</summary>
          <pre className="json-example">
{`{
  "_id": "687a29a5a849420e13f6640c",
  "rowid": "7945faa6-5c35-4843-9461-3d2d75b61f50",
  "ctime": "2025-07-18 19:01:57",
  "YWJ": "[{
    \\"file_id\\": \\"3ee49a83-5664-4026-9e2c-d76c393a64c7\\",
    \\"original_file_name\\": \\"厂房设计管理规程2.docx\\",
    \\"file_size\\": 55287
  }]",
  "FYWJ": "[{
    \\"file_id\\": \\"99fab634-46e6-4400-b893-e1766ded1606\\",
    \\"original_file_name\\": \\"厂房设计管理规程_eng-US.doc\\",
    \\"file_size\\": 95744
  }]"
}`}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default DataStructureInfo;
