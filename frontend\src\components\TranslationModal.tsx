import { useState } from 'react';
import { X, Languages, FileText } from 'lucide-react';
import { TranslationRequest, UploadedFile } from '../types/document';
import { API_CONFIG } from '../config/api.config';

interface TranslationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTranslation: (request: TranslationRequest) => Promise<void>;
  uploadedFiles: UploadedFile[];
}

export default function TranslationModal({ 
  isOpen, 
  onClose, 
  onCreateTranslation, 
  uploadedFiles 
}: TranslationModalProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    file_id: 0,
    source_language: API_CONFIG.translation.defaultSourceLang,
    target_language: API_CONFIG.translation.defaultTargetLang,
    include_original: true
  });

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.file_id === 0) {
      alert('请选择要翻译的文件');
      return;
    }

    if (formData.source_language === formData.target_language) {
      alert('源语言和目标语言不能相同');
      return;
    }

    setLoading(true);
    try {
      await onCreateTranslation(formData);
      onClose();
      // 重置表单
      setFormData({
        file_id: 0,
        source_language: API_CONFIG.translation.defaultSourceLang,
        target_language: API_CONFIG.translation.defaultTargetLang,
        include_original: true
      });
    } catch (error) {
      console.error('创建翻译任务失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
               name === 'file_id' ? parseInt(value) : value
    }));
  };

  const availableFiles = uploadedFiles.filter(file => 
    file.status === 'COMPLETED' && file.total_characters && file.total_characters > 0
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Languages size={20} />
            创建翻译任务
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              选择文件
            </label>
            <select
              name="file_id"
              value={formData.file_id}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={0}>请选择要翻译的文件</option>
              {availableFiles.map(file => (
                <option key={file.id} value={file.id}>
                  {file.original_filename} ({file.total_characters} 字符)
                </option>
              ))}
            </select>
            {availableFiles.length === 0 && (
              <p className="text-sm text-gray-500 mt-1">
                没有可用的文件，请先上传文档
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                源语言
              </label>
              <select
                name="source_language"
                value={formData.source_language}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {Object.entries(API_CONFIG.translation.supportedLanguages).map(([code, name]) => (
                  <option key={code} value={code}>{name}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                目标语言
              </label>
              <select
                name="target_language"
                value={formData.target_language}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {Object.entries(API_CONFIG.translation.supportedLanguages).map(([code, name]) => (
                  <option key={code} value={code}>{name}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              name="include_original"
              checked={formData.include_original}
              onChange={handleInputChange}
              className="mr-2"
            />
            <label className="text-sm text-gray-700">
              包含原文（生成双语对照文档）
            </label>
          </div>

          <button
            type="submit"
            disabled={loading || availableFiles.length === 0}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <>
                <FileText size={16} />
                创建翻译任务
              </>
            )}
          </button>
        </form>
      </div>
    </div>
  );
}
