import { useState, useEffect } from 'react';
import { Download, RefreshCw, Search, User, LogIn, UserPlus, CreditCard, LogOut, Settings } from 'lucide-react';
import FileUpload from './components/FileUpload';
import DocumentList from './components/DocumentList';
import LoginPage from './components/LoginPage';
import TranslationSettings from './components/TranslationSettings';
// import DocumentPreview from './components/DocumentPreview';
// import OnlyOfficePreview from './components/OnlyOfficePreview';
// import OnlyOfficeTest from './components/OnlyOfficeTest';
import StatusMessage from './components/StatusMessage';
import BatchDownloadModal from './components/BatchDownloadModal';
import Pagination from './components/Pagination';
import LoginModal from './components/LoginModal';
import RegisterModal from './components/RegisterModal';
import RechargeModal from './components/RechargeModal';
// import ApiTest from './components/ApiTest';
// import PreviewTest from './components/PreviewTest';
// import DataStructureInfo from './components/DataStructureInfo';
// import SystemStatus from './components/SystemStatus';
import { DocumentFile, DocumentRow, UploadFile, UserAccount } from './types/document';
import { TranslationSettings as TranslationSettingsType } from './types/translation';
import { downloadFile } from './utils/download';
import { getDocuments, uploadMultipleFiles, getDocumentsTotalNum, startTranslation } from './services/api';
import { authService } from './services/auth';
import { useMessages } from './hooks/useMessages';
import { openPreviewWindow, getDocumentFiles } from './utils/preview';
import { getFilePreviewUrl } from './utils/officePreview';
import './App.css';
import './components/FileUpload.css';
import './components/DocumentList.css';
import './components/DocumentPreview.css';
import './components/BatchDownloadModal.css';
import './components/LoginPage.css';
// import './components/OnlyOfficePreview.css';
import './components/StatusMessage.css';
// import './components/ApiTest.css';
// import './components/PreviewTest.css';
// import './components/DataStructureInfo.css';
// import './components/SystemStatus.css';

// 模拟数据已移除，现在使用真实API数据

function App() {
  const [documents, setDocuments] = useState<DocumentRow[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // 用户认证相关状态
  const [currentUser, setCurrentUser] = useState<UserAccount | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [showRechargeModal, setShowRechargeModal] = useState(false);

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  // const [previewFile, setPreviewFile] = useState<DocumentFile | null>(null);
  // const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({});
  console.log('Upload progress:', uploadProgress); // 避免未使用警告
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewFileName, setPreviewFileName] = useState<string>('');

  // OnlyOffice 预览状态（暂时禁用）
  // const [onlyOfficePreview, setOnlyOfficePreview] = useState<{
  //   isOpen: boolean;
  //   rowid: string;
  //   fileName: string;
  // }>({
  //   isOpen: false,
  //   rowid: '',
  //   fileName: ''
  // });

  // 测试页面状态（暂时禁用）
  // const [showTestPage, setShowTestPage] = useState<boolean>(false);

  // 批量下载模态框状态
  const [batchDownloadModal, setBatchDownloadModal] = useState<{
    isOpen: boolean;
    selectedDocuments: DocumentRow[];
  }>({
    isOpen: false,
    selectedDocuments: []
  });

  // 当前选中的文档
  const [selectedDocuments, setSelectedDocuments] = useState<DocumentRow[]>([]);

  // 翻译设置相关状态
  const [showTranslationSettings, setShowTranslationSettings] = useState(false);
  const [currentTranslationSettings, setCurrentTranslationSettings] = useState<TranslationSettingsType | null>(null);

  // 消息系统
  const { messages, removeMessage, showSuccess, showError, showInfo } = useMessages();

  // 加载文档总数
  const loadTotalCount = async () => {
    console.log('🔢 开始获取文档总数...');
    try {
      const total = await getDocumentsTotalNum();
      console.log('🔢 获取到总数:', total);
      setTotalItems(total);
      setTotalPages(Math.ceil(total / pageSize));
      console.log(`🔢 设置状态 - 总记录数: ${total}, 总页数: ${Math.ceil(total / pageSize)}`);
    } catch (error) {
      console.error('🔢 获取总数失败:', error);
      setTotalItems(0);
      setTotalPages(0);
    }
  };

  // 加载文档数据
  const loadDocuments = async (page: number = currentPage, size: number = pageSize) => {
    setLoading(true);
    try {
      const response = await getDocuments({
        pageSize: size,
        pageIndex: page,
        viewId: '',
        listType: 0
      });

      if (response.success && response.data && response.data.rows) {
        setDocuments(response.data.rows);
        showInfo(`成功加载第 ${page} 页，共 ${response.data.rows.length} 条记录`);
      } else {
        console.error('API 返回错误:', response.message);
        showError('获取文档列表失败');
        setDocuments([]);
      }
    } catch (error) {
      console.error('加载文档失败:', error);
      showError('网络连接失败');
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadDocuments(page, pageSize);
  };

  // 处理每页大小变化
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
    setTotalPages(Math.ceil(totalItems / newPageSize));
    loadDocuments(1, newPageSize);
  };

  // 用户认证相关函数
  const checkAuthStatus = () => {
    const user = authService.getCurrentUser();
    const authenticated = authService.isAuthenticated();
    const token = authService.getToken();

    setCurrentUser(user);
    setIsAuthenticated(authenticated);

    console.log('用户认证状态检查:', {
      user,
      authenticated,
      token: token ? '已设置' : '未设置',
      localStorage_current_user: localStorage.getItem('current_user'),
      localStorage_auth_token: localStorage.getItem('auth_token')
    });
  };

  const handleLoginSuccess = (user: UserAccount) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
    setShowLoginModal(false);
    showSuccess(`欢迎回来，${user.fullName || user.username}！`);
  };

  const handleRegisterSuccess = (user: UserAccount) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
    setShowRegisterModal(false);
    showSuccess(`注册成功，欢迎 ${user.fullName || user.username}！`);
  };

  const handleLogout = () => {
    authService.logout();
    setCurrentUser(null);
    setIsAuthenticated(false);
    showInfo('已退出登录');
  };

  const handleRechargeSuccess = () => {
    // 刷新用户信息
    checkAuthStatus();
    setShowRechargeModal(false);
    showSuccess('充值成功！');
  };

  // 翻译设置相关处理函数
  const handleShowTranslationSettings = () => {
    setShowTranslationSettings(true);
  };

  const handleCloseTranslationSettings = () => {
    setShowTranslationSettings(false);
  };

  const handleTranslationSettingsChange = (settings: TranslationSettingsType) => {
    setCurrentTranslationSettings(settings);
    showSuccess('翻译格式设置已更新！');
  };

  // 转换翻译设置格式（前端格式 -> 后端格式）
  const convertTranslationSettings = (settings: TranslationSettingsType | null) => {
    if (!settings) return null;

    return {
      paragraph: {
        font_family: settings.paragraph.fontFamily,
        font_size: settings.paragraph.fontSize,
        bold: settings.paragraph.bold,
        italic: settings.paragraph.italic,
        underline: settings.paragraph.underline,
        text_align: settings.paragraph.textAlign,
        text_indent: settings.paragraph.textIndent,
        line_height: settings.paragraph.lineHeight,
        margin_top: settings.paragraph.marginTop,
        color: settings.paragraph.color,
        background_color: settings.paragraph.backgroundColor
      },
      table: {
        font_family: settings.table.fontFamily,
        font_size: settings.table.fontSize,
        bold: settings.table.bold,
        italic: settings.table.italic,
        underline: settings.table.underline,
        text_align: settings.table.textAlign,
        text_indent: settings.table.textIndent,
        line_height: settings.table.lineHeight,
        margin_top: settings.table.marginTop,
        color: settings.table.color,
        background_color: settings.table.backgroundColor
      },
      header: {
        font_family: settings.header.fontFamily,
        font_size: settings.header.fontSize,
        bold: settings.header.bold,
        italic: settings.header.italic,
        underline: settings.header.underline,
        text_align: settings.header.textAlign,
        text_indent: settings.header.textIndent,
        line_height: settings.header.lineHeight,
        margin_top: settings.header.marginTop,
        color: settings.header.color,
        background_color: settings.header.backgroundColor
      },
      enable_paragraph: settings.enableParagraph,
      enable_table: settings.enableTable,
      enable_header: settings.enableHeader
    };
  };

  // 开始翻译处理函数
  const handleStartTranslation = async (rowId: string, sourceLanguage: string, targetLanguage: string) => {
    try {
      showInfo('正在开始翻译...');

      // 转换并传递当前的翻译设置
      const backendSettings = convertTranslationSettings(currentTranslationSettings);
      const result = await startTranslation(rowId, sourceLanguage, targetLanguage, backendSettings);

      if (result.success) {
        showSuccess(result.message || '翻译已开始！');
        // 刷新文档列表以更新状态
        await loadDocuments(currentPage, pageSize);
      } else {
        showError(result.message || '开始翻译失败');
      }
    } catch (error) {
      console.error('开始翻译失败:', error);
      showError('开始翻译失败，请重试');
    }
  };

  useEffect(() => {
    console.log('🚀 useEffect 开始执行...');

    // 检查用户认证状态
    checkAuthStatus();
    // 首次加载时先获取总数，再加载数据
    const initData = async () => {
      console.log('🚀 开始初始化数据...');
      try {
        console.log('🚀 步骤1: 获取总数');
        await loadTotalCount();
        console.log('🚀 步骤2: 加载文档');
        await loadDocuments(1, 100); // 明确指定参数
        console.log('🚀 初始化完成');
      } catch (error) {
        console.error('🚀 初始化失败:', error);
      }
    };
    initData();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理文件上传
  const handleFilesSelected = (files: UploadFile[]) => {
    console.log('选择的文件:', files);
  };

  const handleUpload = async (files: UploadFile[], sourceLanguage: string, targetLanguage: string) => {
    console.log('开始上传文件:', files, { sourceLanguage, targetLanguage });

    try {
      // 过滤出状态为 pending 的文件
      const filesToUpload = files.filter(f => f.status === 'pending').map(f => f.file);

      if (filesToUpload.length === 0) {
        showError('没有可上传的文件');
        return;
      }

      // 使用新的仅上传文件 API
      const results = await uploadMultipleFiles(
        filesToUpload,
        sourceLanguage,
        targetLanguage,
        (progress, fileName) => {
          console.log(`上传进度: ${progress}% - ${fileName}`);
          setUploadProgress(prev => ({
            ...prev,
            [fileName]: progress
          }));
        }
      );

      // 检查上传结果
      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      if (successCount > 0) {
        showSuccess(`成功上传 ${successCount} 个文件${failCount > 0 ? `，失败 ${failCount} 个` : ''}，可在列表中手动开始翻译`);
        // 上传完成后重新加载文档列表
        await loadDocuments();
      } else {
        showError('所有文件上传失败，请检查网络连接或文件格式');
      }

      // 清空上传进度
      setUploadProgress({});
    } catch (error) {
      console.error('上传失败:', error);
      showError('上传失败，请稍后重试');
      setUploadProgress({});
    }
  };

  // 处理文档预览（已移除，使用内嵌预览）
  // const handlePreview = (file: DocumentFile) => {
  //   setPreviewFile(file);
  //   setIsPreviewOpen(true);
  // };

  // 处理行预览（翻译后文档）- 暂时禁用 OnlyOffice
  const handleRowPreview = (rowid: string) => {
    showInfo('正在打开翻译后的文档预览...');
    openPreviewWindow(rowid);
  };

  // 关闭 OnlyOffice 预览（暂时禁用）
  // const closeOnlyOfficePreview = () => {
  //   setOnlyOfficePreview({
  //     isOpen: false,
  //     rowid: '',
  //     fileName: ''
  //   });
  // };

  // 处理文档下载
  const handleDownload = async (file: DocumentFile) => {
    try {
      await downloadFile(file);
      showSuccess(`开始下载文件: ${file.original_file_name}`);
    } catch (error) {
      console.error('下载失败:', error);
      showError('下载失败，请稍后重试');
    }
  };

  // 处理内嵌预览
  const handleEmbeddedPreview = async (file: DocumentFile) => {
    const url = getFilePreviewUrl(file);

    if (url) {
      setPreviewUrl(url);
      setPreviewFileName(file.original_file_name);
    } else {
      showError('该文件格式不支持在线预览');
    }
  };

  // 关闭预览
  const closePreview = () => {
    setPreviewUrl(null);
    setPreviewFileName('');
  };

  // 批量下载 - 打开选择模态框
  const handleBatchDownload = async () => {
    if (selectedDocuments.length === 0) {
      showError('请先选择要下载的文档');
      return;
    }

    setBatchDownloadModal({
      isOpen: true,
      selectedDocuments: selectedDocuments
    });
  };

  // 确认批量下载
  const confirmBatchDownload = (downloadType: 'original' | 'translated' | 'both') => {
    const { selectedDocuments } = batchDownloadModal;

    showInfo(`开始批量下载 ${selectedDocuments.length} 个文档...`);

    selectedDocuments.forEach((doc, index) => {
      setTimeout(() => {
        downloadDocumentByType(doc, downloadType);

        if (index === selectedDocuments.length - 1) {
          showSuccess('批量下载已开始');
        }
      }, index * 500); // 延迟下载，避免浏览器阻止
    });
  };

  // 根据类型下载文档
  const downloadDocumentByType = (doc: DocumentRow, downloadType: 'original' | 'translated' | 'both') => {
    const documentFiles = getDocumentFiles(doc);

    if (downloadType === 'original' || downloadType === 'both') {
      // 下载原文档
      documentFiles.originalFiles.forEach((file, fileIndex) => {
        setTimeout(() => {
          const link = document.createElement('a');
          link.href = file.original_file_full_path;
          link.download = file.original_file_name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }, fileIndex * 200);
      });
    }

    if (downloadType === 'translated' || downloadType === 'both') {
      // 下载翻译文档
      documentFiles.processedFiles.forEach((file, fileIndex) => {
        setTimeout(() => {
          const link = document.createElement('a');
          link.href = file.original_file_full_path;
          link.download = file.original_file_name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }, fileIndex * 200 + (downloadType === 'both' ? 1000 : 0));
      });
    }
  };

  // 关闭批量下载模态框
  const closeBatchDownloadModal = () => {
    setBatchDownloadModal({
      isOpen: false,
      selectedDocuments: []
    });
  };

  // 处理文档选择变化
  const handleSelectionChange = (selected: DocumentRow[]) => {
    setSelectedDocuments(selected);
  };



  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();

    // 搜索用户名
    if (doc.ownerid.fullname.toLowerCase().includes(searchLower)) {
      return true;
    }

    // 搜索文档中的文件名（包括原始文件和翻译后文件）
    return Object.keys(doc).some(key => {
      if (typeof doc[key] === 'string' && doc[key].startsWith('[{')) {
        try {
          const files = JSON.parse(doc[key]);
          if (Array.isArray(files)) {
            return files.some((file: DocumentFile) =>
              file.original_file_name.toLowerCase().includes(searchLower)
            );
          }
        } catch (error) {
          return false;
        }
      }
      return false;
    });
  });

  // 如果用户未登录，显示登录页面
  if (!isAuthenticated) {
    return (
      <div className="app">
        {/* 消息提示 */}
        <div className="status-messages-container">
          {messages.map((message) => (
            <StatusMessage
              key={message.id}
              type={message.type}
              message={message.message}
              onClose={() => removeMessage(message.id)}
            />
          ))}
        </div>

        <LoginPage
          onLoginSuccess={handleLoginSuccess}
          onRegisterSuccess={handleRegisterSuccess}
        />
      </div>
    );
  }

  // 用户已登录，显示主应用界面
  return (
    <div className="app">
      {/* 消息提示 */}
      <div className="status-messages-container">
        {messages.map((message) => (
          <StatusMessage
            key={message.id}
            type={message.type}
            message={message.message}
            onClose={() => removeMessage(message.id)}
          />
        ))}
      </div>

      <div className="app-header">
        <h1>SMILE TRANS (word文档带格式中英文上下对照翻译系统)</h1>
      </div>

      <div className="app-content">
        {/* 左侧操作区域 */}
        <div className="sidebar">
          {/* 用户认证区域 */}
          <div className="user-section">
            <h3>用户中心</h3>
            {isAuthenticated && currentUser ? (
              <div className="user-info">
                <div className="user-avatar">
                  <User size={24} />
                </div>
                <div className="user-details">
                  <div className="user-name">{currentUser.fullName || currentUser.username}</div>
                  <div className="user-stats">
                    <div className="stat-item">
                      <span className="stat-label">余额:</span>
                      <span className="stat-value">¥{currentUser.balance.toFixed(2)}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">配额:</span>
                      <span className="stat-value">
                        {(currentUser.totalQuota - currentUser.usedQuota).toLocaleString()}字符
                      </span>
                    </div>
                  </div>
                </div>
                <div className="user-actions">
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={() => setShowRechargeModal(true)}
                  >
                    <CreditCard size={14} />
                    充值
                  </button>
                  <button
                    className="btn btn-secondary btn-sm"
                    onClick={handleLogout}
                  >
                    <LogOut size={14} />
                    退出
                  </button>
                </div>
              </div>
            ) : (
              <div className="auth-buttons">
                <button
                  className="btn btn-primary"
                  onClick={() => setShowLoginModal(true)}
                >
                  <LogIn size={16} />
                  登录
                </button>
                <button
                  className="btn btn-success"
                  onClick={() => setShowRegisterModal(true)}
                >
                  <UserPlus size={16} />
                  注册
                </button>
              </div>
            )}
          </div>

          {/* 文件上传区域 */}
          <div className="upload-section">
            <h3>文件上传</h3>
            <FileUpload
              onFilesSelected={handleFilesSelected}
              onUpload={handleUpload}
              maxFileSize={50}
              multiple={true}
            />
          </div>

          {/* 操作按钮区域 */}
          <div className="actions-section">
            <h3>操作</h3>
            <div className="action-buttons">
              <button
                className="btn btn-primary"
                onClick={() => {
                  loadTotalCount();
                  loadDocuments(currentPage, pageSize);
                }}
                disabled={loading}
              >
                <RefreshCw size={16} />
                刷新列表
              </button>

              <button
                className="btn btn-info"
                onClick={handleShowTranslationSettings}
                title="自定义翻译格式设置"
              >
                <Settings size={16} />
                翻译设置
              </button>

              <button
                className="btn btn-success"
                onClick={handleBatchDownload}
                disabled={selectedDocuments.length === 0}
              >
                <Download size={16} />
                批量下载 {selectedDocuments.length > 0 && `(${selectedDocuments.length})`}
              </button>

              {/* <button
                className="btn btn-outline"
                onClick={() => setShowTestPage(!showTestPage)}
              >
                🔧 OnlyOffice 测试
              </button> */}
            </div>
          </div>

          {/* 搜索过滤区域 */}
          <div className="filter-section">
            <h3>搜索过滤</h3>
            <div className="search-box">
              <Search size={16} className="search-icon" />
              <input
                type="text"
                placeholder="搜索文档名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>



          {/* 调试组件已移除，准备部署 */}
        </div>

        {/* 右侧文档列表区域 */}
        <div className="main-content">
          {/* {showTestPage ? (
            // OnlyOffice 测试页面
            <OnlyOfficeTest />
          ) : */}
          {previewUrl ? (
            // 预览界面
            <div className="preview-container">
              <div className="preview-header">
                <button
                  className="btn btn-secondary back-btn"
                  onClick={closePreview}
                >
                  ← 返回列表
                </button>
                <h3 className="preview-title">{previewFileName}</h3>
              </div>
              <div className="preview-frame-container">
                <iframe
                  src={previewUrl}
                  className="preview-frame"
                  title={`预览 ${previewFileName}`}
                  frameBorder="0"
                  allowFullScreen
                />
              </div>
            </div>
          ) : (
            // 文档列表
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflow: 'hidden'
            }}>
              {/* 文档列表区域 - 可滚动 */}
              <div style={{
                flex: '1',
                overflow: 'auto',
                minHeight: '0'
              }}>
                <DocumentList
                  documents={filteredDocuments}
                  onPreview={handleEmbeddedPreview}
                  onDownload={handleDownload}
                  onRowPreview={handleRowPreview}
                  onSelectionChange={handleSelectionChange}
                  onStartTranslation={handleStartTranslation}
                  loading={loading}
                />
              </div>

              {/* 分页组件区域 - 固定在底部 */}
              <div style={{ flexShrink: 0 }}>
                {(totalItems > 0 || documents.length > 0) ? (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages || Math.ceil(documents.length / pageSize) || 1}
                    totalItems={totalItems || documents.length}
                    pageSize={pageSize}
                    pageSizeOptions={[50, 100, 200]}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    loading={loading}
                  />
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    {loading ? '正在加载分页信息...' : `暂无分页信息 (总记录数: ${totalItems})`}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 文档预览模态框（已移除，使用内嵌预览） */}
      {/* <DocumentPreview
        file={previewFile}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        onDownload={handleDownload}
      /> */}

      {/* OnlyOffice 预览模态框（暂时禁用） */}
      {/* {onlyOfficePreview.isOpen && (
        <OnlyOfficePreview
          rowid={onlyOfficePreview.rowid}
          fileName={onlyOfficePreview.fileName}
          onClose={closeOnlyOfficePreview}
        />
      )} */}

      {/* 批量下载选择模态框 */}
      <BatchDownloadModal
        isOpen={batchDownloadModal.isOpen}
        onClose={closeBatchDownloadModal}
        onConfirm={confirmBatchDownload}
        selectedCount={batchDownloadModal.selectedDocuments.length}
      />

      {/* 登录模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLoginSuccess={handleLoginSuccess}
        onSwitchToRegister={() => {
          setShowLoginModal(false);
          setShowRegisterModal(true);
        }}
      />

      {/* 注册模态框 */}
      <RegisterModal
        isOpen={showRegisterModal}
        onClose={() => setShowRegisterModal(false)}
        onRegisterSuccess={handleRegisterSuccess}
        onSwitchToLogin={() => {
          setShowRegisterModal(false);
          setShowLoginModal(true);
        }}
      />

      {/* 充值模态框 */}
      <RechargeModal
        isOpen={showRechargeModal}
        onClose={() => setShowRechargeModal(false)}
        onRechargeSuccess={handleRechargeSuccess}
      />

      {/* 翻译设置模态框 */}
      <TranslationSettings
        isOpen={showTranslationSettings}
        onClose={handleCloseTranslationSettings}
        onSettingsChange={handleTranslationSettingsChange}
      />

    </div>
  );
}

export default App;
