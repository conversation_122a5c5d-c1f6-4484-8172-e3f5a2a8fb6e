# SMILE TRANS - Word文档带格式中英文上下对照翻译系统

一个基于 React + TypeScript 的现代化文档翻译管理系统，支持 Word 文档的格式化翻译和预览功能。

## 🚀 功能特性

### ✅ 已实现功能
- **文档上传**: 支持拖拽上传，多文件上传，文件类型验证
- **文档列表**: 展示文档信息，支持搜索过滤
- **文档下载**: 单个下载和批量下载
- **文档预览**:
  - 原始文件预览（Word、PDF、图片等格式）
  - 翻译后文档预览（通过服务器预览地址）
  - 智能预览按钮显示（只有翻译后文件才显示预览按钮）
- **API 集成**: 集成真实的后端 API 接口
- **消息提示**: 友好的操作反馈和错误提示
- **响应式设计**: 适配不同屏幕尺寸
- **CORS 解决方案**: 开发环境使用 Vite 代理解决跨域问题

### 🔧 技术栈
- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **文档预览**: mammoth.js (Word 文档)
- **图标库**: lucide-react
- **样式**: 纯 CSS（无额外框架依赖）

## 📦 安装和运行

### 环境要求
- Node.js >= 16
- npm >= 8

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看应用

### 构建生产版本
```bash
npm run build
```

### 生产环境部署

#### 方案1: 使用 PM2 (推荐)

PM2 是专业的 Node.js 进程管理器，提供自动重启、日志管理、监控等功能。

```bash
# 1. 安装 PM2
npm install -g pm2

# 2. 使用脚本一键启动（推荐）
chmod +x start-production.sh
./start-production.sh

# 3. 或手动启动
npm run production

# 4. 管理服务
pm2 status              # 查看状态
pm2 logs translate-front # 查看日志
pm2 restart translate-front # 重启服务
pm2 stop translate-front    # 停止服务
```

#### 方案2: 使用 nohup (简单方案)

```bash
# 启动服务
chmod +x start-nohup.sh
./start-nohup.sh

# 停止服务
chmod +x stop-nohup.sh
./stop-nohup.sh

# 查看日志
tail -f app.log
```

#### 方案3: 使用 systemd (最稳定)

```bash
# 设置 systemd 服务
chmod +x setup-systemd.sh
./setup-systemd.sh

# 管理服务
sudo systemctl start translate-front
sudo systemctl status translate-front
sudo journalctl -u translate-front -f
```

生产服务器将运行在 `http://localhost:3000`，自动处理 API 代理和 CORS 问题。

**🔥 解决服务器断开连接问题**: 使用上述任一方案都可以让你的服务在断开SSH连接后继续运行！

## � 可用脚本

### 开发脚本
- `npm run dev` - 启动开发服务器 (http://localhost:5173)
- `npm run build` - 构建生产版本
- `npm run preview` - 预览构建结果
- `npm run lint` - 代码检查

### 生产脚本
- `npm run serve` - 构建并启动服务
- `npm run start` - 启动服务器
- `npm run production` - 构建并用 PM2 启动

### PM2 管理脚本
- `npm run pm2:start` - 启动 PM2 服务
- `npm run pm2:stop` - 停止 PM2 服务
- `npm run pm2:restart` - 重启 PM2 服务
- `npm run pm2:logs` - 查看 PM2 日志
- `npm run pm2:status` - 查看 PM2 状态

### 部署脚本文件
- `./start-production.sh` - PM2 一键启动脚本
- `./stop-production.sh` - PM2 停止脚本
- `./start-nohup.sh` - nohup 启动脚本
- `./stop-nohup.sh` - nohup 停止脚本
- `./setup-systemd.sh` - systemd 服务设置脚本
- `./install-pm2.sh` - PM2 安装脚本

## �🔧 配置说明

### API 配置
编辑 `src/config/api.config.ts` 文件来配置 API 参数：

```typescript
export const API_CONFIG = {
  // API 基础配置
  baseUrl: 'https://dmit.duoningbio.com/api/v2/open',
  appKey: 'your-app-key',
  sign: 'your-sign',
  worksheetId: 'your-worksheet-id',
  
  // 文件上传配置
  upload: {
    originalFileControlId: '68789ddfa849420e13f65dc5',
    maxFileSize: 50, // MB
    allowedTypes: ['.doc', '.docx', '.pdf', '.txt', '.xlsx'],
  }
};
```

### 控制项 ID 配置
在 `src/config/api.config.ts` 中配置对应的控制项 ID：

```typescript
export const CONTROL_IDS = {
  ORIGINAL_FILE: 'YWJ',    // 原文件
  PROCESSED_FILE: 'FYWJ',  // 翻译文件
  REMARK: '68789e5fa849420e13f65dcf',         // 备注
  OWNER: 'ownerid'                           // 拥有者
};
```

### 数据结构变化
- **YWJ**：原文件字段，存储用户上传的原始文件
- **FYWJ**：翻译文件字段，存储翻译服务处理后的文件

## 📋 API 接口说明

### 1. 获取文档列表
- **接口**: `POST /api/v2/open/worksheet/getFilterRows`
- **功能**: 获取工作表中的文档记录
- **参数**: 分页、过滤条件等

### 2. 新建文档记录
- **接口**: `POST /api/v2/open/worksheet/addRow`
- **功能**: 上传文件并创建新的文档记录
- **支持**: 文件流 base64 编码上传

## 🎨 界面布局

系统采用左右分栏布局：

### 左侧操作区域
- **文件上传**: 拖拽上传区域，支持多文件选择
- **操作按钮**: 刷新列表、批量下载等功能
- **搜索过滤**: 文档名称搜索
- **API 测试**: 测试与服务器的连接

### 右侧内容区域
- **文档列表**: 展示所有文档，包含文件信息和操作按钮
- **预览功能**: 点击预览按钮可在弹窗中查看文档内容

## 🔍 文档预览支持

### 原始文件预览
- **Word 文档** (.doc, .docx): 使用 mammoth.js 转换为 HTML 预览
- **PDF 文件**: 直接在 iframe 中预览
- **图片文件**: 直接显示
- **文本文件**: 显示文本内容

### 翻译后文档预览
- **预览地址格式**: `https://dmit.duoningbio.com/rowfile/{rowid}/`（暂时保留）
- **预览条件**: 只有当 `FYWJ` 字段有翻译文件时才显示预览按钮
- **预览状态**: 暂时不可用，等待确认新的预览方案
- **文件来源**: 显示翻译服务处理后的文件，不是原始上传文件

## 🚨 错误处理

系统包含完善的错误处理机制：
- 网络连接失败时显示友好提示
- API 调用失败时自动回退到模拟数据
- 文件上传失败时显示具体错误信息
- 支持重试机制

## 📱 响应式设计

- **桌面端**: 左右分栏布局
- **平板端**: 上下布局，操作区域水平滚动
- **移动端**: 垂直堆叠布局，优化触摸操作

## 🔧 开发说明

### 项目结构
```
src/
├── components/          # React 组件
│   ├── FileUpload.tsx   # 文件上传组件
│   ├── DocumentList.tsx # 文档列表组件
│   ├── DocumentPreview.tsx # 文档预览组件
│   └── StatusMessage.tsx   # 消息提示组件
├── services/           # API 服务
│   └── api.ts         # API 调用封装
├── config/            # 配置文件
│   └── api.config.ts  # API 配置
├── types/             # TypeScript 类型定义
│   └── document.ts    # 文档相关类型
├── utils/             # 工具函数
│   └── download.ts    # 下载相关工具
└── hooks/             # 自定义 Hooks
    └── useMessages.ts # 消息管理 Hook
```

### 添加新功能
1. 在 `src/components/` 中创建新组件
2. 在 `src/services/api.ts` 中添加 API 调用
3. 在 `src/types/` 中定义相关类型
4. 更新 `src/App.tsx` 集成新功能

## 📄 许可证

MIT License

## 🚀 服务器部署指南

### 方式一：Linux 服务器部署（推荐）

#### 1. 准备服务器环境
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 2. 上传项目文件
```bash
# 方式一：使用 git clone
git clone <your-repo-url>
cd TranslateFront

# 方式二：使用 scp 上传
scp -r ./TranslateFront user@your-server:/var/www/
```

#### 3. 安装依赖并构建
```bash
# 进入项目目录
cd /var/www/TranslateFront

# 安装依赖
npm install

# 构建生产版本
npm run build
```

#### 4. 使用 PM2 管理进程（推荐）
```bash
# 安装 PM2
sudo npm install -g pm2

# 启动应用
pm2 start server.cjs --name "translate-front"

# 设置开机自启
pm2 startup
pm2 save

# 查看状态
pm2 status
pm2 logs translate-front
```

#### 5. 配置 Nginx 反向代理
```bash
# 安装 Nginx
sudo apt install nginx

# 创建配置文件
sudo nano /etc/nginx/sites-available/translate-front
```

Nginx 配置内容：
```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/translate-front /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

### 方式二：Docker 部署

#### 1. 创建 Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "run", "start"]
```

#### 2. 构建和运行
```bash
# 构建镜像
docker build -t translate-front .

# 运行容器
docker run -d -p 3000:3000 --name translate-front translate-front

# 查看日志
docker logs translate-front
```

### 方式三：云服务器一键部署

#### Vercel 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

#### Railway 部署
1. 连接 GitHub 仓库
2. 选择项目
3. 自动部署

### 🔧 环境变量配置

创建 `.env` 文件：
```bash
# 服务器端口
PORT=3000

# API 配置（如果需要）
API_BASE_URL=https://dmit.duoningbio.com/api/v2/open
```

### 🔍 部署验证

部署完成后，访问你的域名或 IP 地址，检查：
- ✅ 页面正常加载
- ✅ 文档列表能正常获取数据
- ✅ 文件上传功能正常
- ✅ 文档预览功能正常

### 🚨 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
sudo lsof -i :3000

# 杀死进程
sudo kill -9 <PID>
```

#### 2. 权限问题
```bash
# 给予执行权限
chmod +x server.cjs

# 修改文件所有者
sudo chown -R $USER:$USER /var/www/TranslateFront
```

#### 3. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 3000
sudo ufw allow 80
sudo ufw allow 443

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
