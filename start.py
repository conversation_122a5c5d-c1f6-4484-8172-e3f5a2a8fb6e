"""
完整的启动脚本 - 同时启动后端和前端服务
"""
import uvicorn
import subprocess
import threading
import time
import webbrowser
import os
import sys
import signal

def start_frontend():
    """启动前端服务"""
    try:
        print("🌐 启动前端服务...")
        frontend_dir = os.path.join(os.getcwd(), "frontend")

        if not os.path.exists(frontend_dir):
            print("❌ frontend 目录不存在")
            return

        # 启动前端HTTP服务器
        process = subprocess.Popen([
            sys.executable, "-m", "http.server", "3000"
        ], cwd=frontend_dir)

        print(f"✅ 前端服务已启动 (PID: {process.pid})")
        return process

    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return None

def open_browser():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务启动
    try:
        print("🌐 打开浏览器...")
        webbrowser.open("http://localhost:3000")
    except Exception as e:
        print(f"⚠️  无法自动打开浏览器: {e}")

if __name__ == "__main__":
    print("🚀 启动 FastAPI 翻译服务...")
    print("📖 API 文档: http://localhost:8000/docs")
    print("🌐 Web 界面: http://localhost:3000")
    print("🔍 健康检查: http://localhost:8000/health")
    print("=" * 50)

    frontend_process = None

    try:
        # 启动前端服务
        frontend_process = start_frontend()

        # 在后台线程打开浏览器
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()

        # 等待前端服务启动
        time.sleep(2)

        print("⚙️  启动后端服务...")

        # 在主线程启动后端服务
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )

    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")

        # 停止前端服务
        if frontend_process:
            try:
                frontend_process.terminate()
                frontend_process.wait(timeout=5)
                print("✅ 前端服务已停止")
            except:
                try:
                    frontend_process.kill()
                    print("✅ 前端服务已强制停止")
                except:
                    print("⚠️  无法停止前端服务")

        print("🛑 服务已停止")

    except Exception as e:
        print(f"❌ 启动失败: {e}")

        # 清理前端进程
        if frontend_process:
            try:
                frontend_process.terminate()
            except:
                pass
