module.exports = {
  apps: [{
    name: 'translate-front',
    script: 'server.cjs',
    cwd: './',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    // 自动重启配置
    min_uptime: '10s',
    max_restarts: 10,
    // 优雅关闭
    kill_timeout: 5000,
    // 启动延迟
    listen_timeout: 3000,
    // 健康检查
    health_check_grace_period: 3000
  }]
};
