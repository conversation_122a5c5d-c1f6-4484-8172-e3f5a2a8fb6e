"""
测试服务器 - 最简单的FastAPI应用
"""
from fastapi import FastAPI
import uvicorn

app = FastAPI(title="测试服务器")

@app.get("/")
async def root():
    return {"message": "Hello World", "status": "ok"}

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "test-server"}

@app.get("/test-mingdao")
async def test_mingdao():
    return {
        "success": True,
        "message": "明道云API测试",
        "pricing": {
            "monthly": {"price": 20, "quota": 100000},
            "per_translation": {"price": 5, "max_chars": 5000}
        }
    }

if __name__ == "__main__":
    print("🚀 启动测试服务器...")
    print("📖 访问: http://localhost:8000")
    print("📖 文档: http://localhost:8000/docs")
    
    uvicorn.run(
        "test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
