"""
修复卡住的翻译任务并重新启动翻译
"""
import asyncio
import sqlite3
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.azure_translator import AzureTranslatorService

async def fix_stuck_translation():
    """修复卡住的翻译任务"""
    print("🔧 修复卡住的翻译任务")
    print("=" * 60)
    
    # 连接数据库
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    try:
        # 查找卡住的翻译任务（超过5分钟没有更新的IN_PROGRESS任务）
        five_minutes_ago = datetime.now() - timedelta(minutes=5)
        
        cursor.execute("""
            SELECT id, status, progress, original_text, source_language, target_language, created_at, updated_at
            FROM translation_histories 
            WHERE status = 'IN_PROGRESS' 
            AND datetime(updated_at) < datetime(?)
        """, (five_minutes_ago.strftime('%Y-%m-%d %H:%M:%S'),))
        
        stuck_tasks = cursor.fetchall()
        
        if not stuck_tasks:
            print("✅ 没有发现卡住的翻译任务")
            
            # 检查是否有PENDING任务
            cursor.execute("SELECT COUNT(*) FROM translation_histories WHERE status = 'PENDING'")
            pending_count = cursor.fetchone()[0]
            
            if pending_count > 0:
                print(f"🔍 发现 {pending_count} 个待处理的翻译任务")
                return True
            else:
                return False
        
        print(f"🔍 发现 {len(stuck_tasks)} 个卡住的翻译任务:")
        
        for task_id, status, progress, original_text, source_lang, target_lang, created_at, updated_at in stuck_tasks:
            print(f"\n📋 任务 {task_id}:")
            print(f"   状态: {status}, 进度: {progress:.1%}")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            print(f"   原文预览: {original_text[:50]}...")
            
            # 重置为 PENDING 状态，让系统重新处理
            cursor.execute("""
                UPDATE translation_histories 
                SET status = 'PENDING', 
                    progress = 0.0, 
                    error_message = '任务超时已重置',
                    updated_at = datetime('now')
                WHERE id = ?
            """, (task_id,))
            
            print(f"   ✅ 任务 {task_id} 已重置为 PENDING 状态")
        
        # 提交更改
        conn.commit()
        print(f"\n🎉 成功重置 {len(stuck_tasks)} 个翻译任务")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

async def process_pending_translations():
    """处理待处理的翻译任务"""
    print("\n🚀 开始处理待处理的翻译任务")
    print("=" * 60)
    
    # 连接数据库
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    try:
        # 查找待处理的翻译任务
        cursor.execute("""
            SELECT th.id, th.original_text, th.source_language, th.target_language,
                   uf.original_filename, th.total_characters
            FROM translation_histories th
            JOIN uploaded_files uf ON th.file_id = uf.id
            WHERE th.status = 'PENDING'
            ORDER BY th.created_at
            LIMIT 1
        """)
        
        pending_task = cursor.fetchone()
        
        if not pending_task:
            print("✅ 没有待处理的翻译任务")
            return
        
        task_id, original_text, source_lang, target_lang, filename, total_chars = pending_task
        
        print(f"📝 处理翻译任务 {task_id}:")
        print(f"   文件: {filename}")
        print(f"   语言: {source_lang} → {target_lang}")
        print(f"   字符数: {total_chars}")
        
        # 创建翻译器
        translator = AzureTranslatorService()
        
        try:
            # 更新状态为进行中
            cursor.execute("""
                UPDATE translation_histories 
                SET status = 'IN_PROGRESS', 
                    progress = 0.1,
                    updated_at = datetime('now')
                WHERE id = ?
            """, (task_id,))
            conn.commit()
            
            print("   🔄 开始翻译...")
            
            # 分割文本为段落
            paragraphs = [p.strip() for p in original_text.split('\n') if p.strip()]
            print(f"   📄 段落数量: {len(paragraphs)}")
            
            # 分批翻译（每次最多10段）
            batch_size = 10
            translated_paragraphs = []
            
            for i in range(0, len(paragraphs), batch_size):
                batch = paragraphs[i:i+batch_size]
                print(f"   🔄 翻译第 {i//batch_size + 1} 批 ({len(batch)} 段)...")
                
                try:
                    # 翻译当前批次
                    results = await translator.translate_text(
                        texts=batch,
                        target_language=target_lang,
                        source_language=source_lang
                    )
                    
                    # 提取翻译结果
                    for result in results:
                        if 'translations' in result and len(result['translations']) > 0:
                            translated_paragraphs.append(result['translations'][0]['text'])
                        else:
                            translated_paragraphs.append('[Translation failed]')
                    
                    # 更新进度
                    progress = 0.1 + (min(i + batch_size, len(paragraphs)) / len(paragraphs)) * 0.8
                    cursor.execute("""
                        UPDATE translation_histories 
                        SET progress = ?,
                            updated_at = datetime('now')
                        WHERE id = ?
                    """, (progress, task_id))
                    conn.commit()
                    
                    print(f"   ✅ 第 {i//batch_size + 1} 批翻译完成，进度: {progress:.1%}")
                    
                    # 短暂延迟，避免API限制
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    print(f"   ⚠️  第 {i//batch_size + 1} 批翻译失败: {e}")
                    # 添加错误占位符
                    for _ in batch:
                        translated_paragraphs.append(f'[Error: {str(e)}]')
            
            # 组合翻译结果
            translated_text = '\n'.join(translated_paragraphs)
            
            # 更新翻译结果
            cursor.execute("""
                UPDATE translation_histories 
                SET status = 'COMPLETED', 
                    progress = 1.0,
                    translated_text = ?,
                    translated_characters = ?,
                    completed_at = datetime('now'),
                    updated_at = datetime('now')
                WHERE id = ?
            """, (translated_text, len(translated_text), task_id))
            
            conn.commit()
            
            print(f"   🎉 任务 {task_id} 翻译完成！")
            print(f"   📊 翻译字符数: {len(translated_text)}")
            
        except Exception as e:
            print(f"   ❌ 任务 {task_id} 翻译失败: {e}")
            
            # 更新为失败状态
            cursor.execute("""
                UPDATE translation_histories 
                SET status = 'FAILED', 
                    error_message = ?,
                    updated_at = datetime('now')
                WHERE id = ?
            """, (str(e), task_id))
            conn.commit()
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

async def show_translation_status():
    """显示翻译任务状态"""
    print("\n📊 翻译任务状态:")
    print("=" * 60)
    
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    try:
        # 统计各状态的任务数量
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM translation_histories
            GROUP BY status
            ORDER BY 
                CASE status 
                    WHEN 'PENDING' THEN 1
                    WHEN 'IN_PROGRESS' THEN 2
                    WHEN 'COMPLETED' THEN 3
                    WHEN 'FAILED' THEN 4
                    ELSE 5
                END
        """)
        
        status_counts = cursor.fetchall()
        
        if not status_counts:
            print("   没有翻译任务")
            return
        
        for status, count in status_counts:
            status_emoji = {
                'PENDING': '⏳',
                'IN_PROGRESS': '🔄',
                'COMPLETED': '✅',
                'FAILED': '❌'
            }.get(status, '❓')
            
            print(f"   {status_emoji} {status}: {count} 个任务")
        
        # 显示最近的任务
        print("\n📋 最近的翻译任务:")
        cursor.execute("""
            SELECT id, status, progress, created_at, updated_at
            FROM translation_histories
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        recent_tasks = cursor.fetchall()
        for task_id, status, progress, created_at, updated_at in recent_tasks:
            print(f"   ID {task_id}: {status} ({progress:.1%}) - {created_at}")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")
    finally:
        conn.close()

async def main():
    """主函数"""
    print("🔧 翻译任务修复工具")
    print("=" * 60)
    
    # 1. 修复卡住的任务
    has_tasks = await fix_stuck_translation()
    
    # 2. 处理待处理的任务
    if has_tasks:
        await process_pending_translations()
    
    # 3. 显示最终状态
    await show_translation_status()
    
    print("\n🎉 修复完成！")

if __name__ == "__main__":
    asyncio.run(main())
