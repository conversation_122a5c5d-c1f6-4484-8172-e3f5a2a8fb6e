# 📖 SMILE TRANS 使用指南

## 🚀 服务器部署后的使用方法

### 1. 启动服务

根据你选择的部署方案：

#### PM2 方案
```bash
# 启动服务
./start-production.sh

# 查看状态
pm2 status

# 查看日志
pm2 logs translate-front
```

#### nohup 方案
```bash
# 启动服务
./start-nohup.sh

# 查看日志
tail -f app.log
```

#### systemd 方案
```bash
# 启动服务
sudo systemctl start translate-front

# 查看状态
sudo systemctl status translate-front
```

### 2. 访问系统

打开浏览器访问：`http://your-server-ip:3000`

### 3. 停止服务

#### PM2 方案
```bash
./stop-production.sh
# 或
pm2 stop translate-front
```

#### nohup 方案
```bash
./stop-nohup.sh
```

#### systemd 方案
```bash
sudo systemctl stop translate-front
```

## 🔧 常用管理命令

### PM2 管理命令

```bash
# 查看所有服务状态
pm2 status

# 查看特定服务日志
pm2 logs translate-front

# 实时监控
pm2 monit

# 重启服务
pm2 restart translate-front

# 重新加载配置
pm2 reload translate-front

# 删除服务
pm2 delete translate-front

# 保存当前配置
pm2 save

# 查看服务详细信息
pm2 show translate-front
```

### 日志查看

```bash
# PM2 日志
pm2 logs translate-front
pm2 logs translate-front --err  # 只看错误日志

# nohup 日志
tail -f app.log
tail -n 100 app.log  # 查看最后100行

# systemd 日志
sudo journalctl -u translate-front -f
sudo journalctl -u translate-front --since "1 hour ago"
```

### 服务状态检查

```bash
# 检查端口是否在监听
sudo netstat -tlnp | grep :3000
# 或
sudo ss -tlnp | grep :3000

# 检查进程
ps aux | grep node
ps aux | grep translate-front

# 检查服务器资源使用
top
htop  # 如果安装了
```

## 🔄 更新部署

当你有新的代码需要部署时：

### 使用 Git 更新

```bash
# 1. 停止服务
./stop-production.sh  # 或对应的停止命令

# 2. 更新代码
git pull origin main

# 3. 安装新依赖（如果有）
npm install

# 4. 重新构建
npm run build

# 5. 启动服务
./start-production.sh  # 或对应的启动命令
```

### 手动文件更新

```bash
# 1. 停止服务
./stop-production.sh

# 2. 备份当前版本（可选）
cp -r . ../TranslateFront-backup-$(date +%Y%m%d)

# 3. 上传新文件
# 使用 scp 或其他方式上传新文件

# 4. 安装依赖并构建
npm install
npm run build

# 5. 启动服务
./start-production.sh
```

## 🚨 故障处理

### 服务无法启动

1. **检查端口占用**
```bash
sudo lsof -i :3000
# 如果有进程占用，杀死它
sudo kill -9 <PID>
```

2. **检查权限**
```bash
chmod +x *.sh
sudo chown -R $USER:$USER ./
```

3. **检查 Node.js 版本**
```bash
node --version  # 需要 >= 16.0.0
```

### 服务运行异常

1. **查看错误日志**
```bash
pm2 logs translate-front --err
# 或
tail -f app.log
```

2. **重启服务**
```bash
pm2 restart translate-front
# 或重新运行启动脚本
```

3. **清理并重新部署**
```bash
./stop-production.sh
rm -rf node_modules
npm install
npm run build
./start-production.sh
```

### 网络访问问题

1. **检查防火墙**
```bash
# Ubuntu/Debian
sudo ufw status
sudo ufw allow 3000

# CentOS/RHEL
sudo firewall-cmd --list-ports
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

2. **检查服务绑定**
```bash
sudo netstat -tlnp | grep :3000
```

## 📊 性能监控

### PM2 监控

```bash
# 实时监控面板
pm2 monit

# 查看资源使用
pm2 show translate-front
```

### 系统监控

```bash
# CPU 和内存使用
top
htop

# 磁盘使用
df -h

# 网络连接
sudo netstat -an | grep :3000
```

## 🔐 安全建议

1. **使用反向代理**
   - 配置 Nginx 作为反向代理
   - 隐藏真实端口号
   - 添加 SSL 证书

2. **防火墙配置**
   - 只开放必要的端口
   - 限制访问来源

3. **定期更新**
   - 定期更新 Node.js 和依赖包
   - 关注安全漏洞

## 📞 获取帮助

如果遇到问题：

1. 查看本文档的故障处理部分
2. 检查日志文件获取错误信息
3. 查看 [README.md](./README.md) 获取更多技术细节
4. 查看 [DEPLOYMENT.md](./DEPLOYMENT.md) 获取部署详情

---

**SMILE TRANS** - 让你的文档翻译服务稳定运行！ 🚀
