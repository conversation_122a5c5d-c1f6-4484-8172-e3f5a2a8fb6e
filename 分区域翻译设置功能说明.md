# 🎨 分区域翻译设置功能说明

## 🎯 **重大改进**

### **从单一设置到分区域设置**
- ❌ **之前** - 所有内容使用相同的翻译格式
- ✅ **现在** - 正文、表格、页眉分别设置格式

### **核心优势**
- 📝 **正文段落** - 适合阅读的字体和格式
- 📊 **表格内容** - 紧凑的字体，节省空间
- 📄 **页眉内容** - 与页眉风格协调的格式

## 🔧 **新的数据结构**

### **1. 格式设置接口**
```typescript
// 单个区域的格式设置
interface TranslationFormatSettings {
  fontFamily: string      // 字体名称
  fontSize: number        // 字体大小
  bold: boolean          // 粗体
  italic: boolean        // 斜体
  underline: boolean     // 下划线
  textAlign: string      // 对齐方式
  textIndent: number     // 首行缩进
  lineHeight: string     // 行间距
  marginTop: number      // 段前间距
  color: string          // 文字颜色
  backgroundColor: string // 背景颜色
}

// 完整的翻译设置（包含三个区域）
interface TranslationSettings {
  paragraph: TranslationFormatSettings  // 正文段落设置
  table: TranslationFormatSettings      // 表格设置
  header: TranslationFormatSettings     // 页眉设置
  
  enableParagraph: boolean              // 是否启用正文翻译
  enableTable: boolean                  // 是否启用表格翻译
  enableHeader: boolean                 // 是否启用页眉翻译
}
```

### **2. 默认设置**
```typescript
const DEFAULT_TRANSLATION_SETTINGS = {
  // 正文段落设置（Arial 10.5pt）
  paragraph: {
    fontFamily: 'Arial',
    fontSize: 10.5,
    // ... 其他设置
  },
  
  // 表格设置（Arial 6pt）
  table: {
    fontFamily: 'Arial',
    fontSize: 6,
    // ... 其他设置
  },
  
  // 页眉设置（Arial 10.5pt）
  header: {
    fontFamily: 'Arial',
    fontSize: 10.5,
    // ... 其他设置
  },
  
  // 全局开关
  enableParagraph: true,
  enableTable: true,
  enableHeader: true
}
```

## 🎨 **用户界面设计**

### **1. 界面布局**
```
┌─────────────────────────────────────────────────────┐
│                🎨 翻译格式设置                [✕]    │
├─────────────────────────────────────────────────────┤
│ 全局开关                                            │
│ [☑] 📝 翻译正文段落  [☑] 📊 翻译表格内容  [☑] 📄 翻译页眉内容 │
├─────────────────────────────────────────────────────┤
│ [📝 正文段落] [📊 表格内容] [📄 页眉内容]           │
├─────────────────────────────────────────────────────┤
│ 📝 正文段落格式设置                                 │
│ 设置正文段落译文的字体、样式和格式                   │
│                                                     │
│ 📝 字体设置                                         │
│   字体名称: [Arial ▼]    字体大小: [10.5] pt       │
│                                                     │
│ ✨ 字体样式                                         │
│   [☐] 粗体  [☐] 斜体  [☐] 下划线                   │
│                                                     │
│ 📐 段落格式                                         │
│   对齐方式: [跟随原文 ▼]  首行缩进: [0] 字符        │
│                                                     │
│ 📏 间距设置                                         │
│   行间距: [跟随原文 ▼]    段前间距: [0] pt          │
│                                                     │
│ 🎨 颜色设置                                         │
│   文字颜色: [⬛]          背景颜色: [⬜]             │
├─────────────────────────────────────────────────────┤
│ 📖 效果预览 - 正文段落                              │
│ ┌─────────────────┐ ┌─────────────────┐             │
│ │ 原文示例：      │ │ 译文示例：      │             │
│ │ 这是一段中文... │ │ This is an...   │             │
│ └─────────────────┘ └─────────────────┘             │
│ 格式信息: 字体:Arial 大小:10.5pt 样式:无 对齐:跟随原文 │
├─────────────────────────────────────────────────────┤
│           [🔄 恢复默认]  [💾 保存设置]              │
└─────────────────────────────────────────────────────┘
```

### **2. 标签页切换**
- 📝 **正文段落标签** - 设置正文段落的翻译格式
- 📊 **表格内容标签** - 设置表格单元格的翻译格式
- 📄 **页眉内容标签** - 设置页眉内容的翻译格式

### **3. 全局开关**
- ✅ **启用/禁用翻译** - 可以选择性地翻译某些区域
- 🔒 **标签页联动** - 禁用的区域标签页变为不可点击

## 🔧 **功能特点**

### **1. 分区域设置**
- 📝 **正文段落** - 默认Arial 10.5pt，适合阅读
- 📊 **表格内容** - 默认Arial 6pt，节省空间
- 📄 **页眉内容** - 默认Arial 10.5pt，与页眉协调

### **2. 实时预览**
- 🎯 **当前区域预览** - 只显示当前选中标签页的效果
- 📖 **区域特定示例** - 不同区域显示不同的示例文本
- 📊 **格式信息显示** - 实时显示当前格式的详细信息

### **3. 智能交互**
- 🔄 **标签页切换** - 平滑切换不同区域的设置
- 🔒 **禁用状态** - 关闭的区域标签页变灰不可点击
- 💾 **统一保存** - 一次保存所有区域的设置

### **4. 格式继承**
```
原文格式 + 区域特定设置 = 最终译文格式
```

## 🚀 **使用流程**

### **1. 设置全局开关**
```
用户选择要翻译的区域 → 勾选对应的开关 → 对应标签页变为可用
```

### **2. 分别设置格式**
```
点击"正文段落"标签 → 设置正文格式 → 点击"表格内容"标签 → 设置表格格式 → 点击"页眉内容"标签 → 设置页眉格式
```

### **3. 实时预览效果**
```
每次修改设置 → 立即看到当前区域的预览效果 → 格式信息实时更新
```

### **4. 保存应用设置**
```
点击"保存设置" → 所有区域设置保存 → 后续翻译使用分区域格式
```

## 🎯 **预期效果**

### **翻译后的文档结构**
```
原始标题（保持原有格式）
Translated Title（页眉格式：Arial 10.5pt）

原始段落1（保持原有格式）
Translated Paragraph 1（正文格式：Arial 10.5pt）

原始段落2（保持原有格式）
Translated Paragraph 2（正文格式：Arial 10.5pt）

[原始表格]
原始单元格1 | 原始单元格2
Translated Cell 1（表格格式：Arial 6pt） | Translated Cell 2（表格格式：Arial 6pt）

[页眉内容]
原始页眉内容
Translated Header Content（页眉格式：Arial 10.5pt）
```

### **格式优势对比**
| 区域 | 之前（统一格式） | 现在（分区域格式） | 优势 |
|------|------------------|-------------------|------|
| 正文段落 | Arial 10.5pt | Arial 10.5pt（可自定义） | ✅ 保持可读性 |
| 表格内容 | Arial 10.5pt | Arial 6pt（可自定义） | ✅ 节省空间，避免换行 |
| 页眉内容 | Arial 10.5pt | Arial 10.5pt（可自定义） | ✅ 与页眉风格协调 |

## 🔧 **技术实现**

### **1. 状态管理**
```tsx
const [settings, setSettings] = useState<TranslationSettings>({ ...DEFAULT_TRANSLATION_SETTINGS });
const [activeTab, setActiveTab] = useState<'paragraph' | 'table' | 'header'>('paragraph');
```

### **2. 设置更新**
```tsx
// 更新当前标签页的格式设置
const updateFormatSetting = <K extends keyof TranslationFormatSettings>(
  key: K,
  value: TranslationFormatSettings[K]
) => {
  setSettings(prev => ({
    ...prev,
    [activeTab]: {
      ...prev[activeTab],
      [key]: value
    }
  }));
};
```

### **3. 预览样式**
```tsx
// 基于当前选中的标签页计算预览样式
const previewStyle = useMemo(() => {
  const currentSettings = settings[activeTab];
  return TranslationSettingsUtils.formatToCSSStyle(currentSettings);
}, [settings, activeTab]);
```

## 🎊 **功能完成状态**

### **前端部分** ✅ 完成
- ✅ 分区域数据结构设计
- ✅ 标签页界面实现
- ✅ 全局开关功能
- ✅ 实时预览更新
- ✅ 格式信息显示
- ✅ 响应式设计

### **后端部分** 🔄 待实现
- 🔄 API接口扩展（支持分区域设置）
- 🔄 Word文档处理更新
- 🔄 分区域格式应用逻辑

现在用户可以非常精细地控制不同区域的翻译格式，真正实现个性化的翻译效果！🎊
