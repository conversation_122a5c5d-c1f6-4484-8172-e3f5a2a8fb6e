@echo off
chcp 65001 >nul

echo 🚀 开始部署翻译文档管理系统...

REM 检查 Node.js 是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)

REM 检查 npm 是否安装
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装，请先安装 npm
    pause
    exit /b 1
)

echo ✅ Node.js 和 npm 已安装

REM 安装依赖
echo 📦 安装依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 构建项目
echo 🔨 构建项目...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 项目构建失败
    pause
    exit /b 1
)

echo.
echo 🎉 构建完成！
echo.
echo 🚀 启动选项:
echo   1. 开发环境: npm run dev
echo   2. 生产环境: npm run start
echo   3. 一键启动: npm run serve
echo.
echo 📍 生产环境访问地址: http://localhost:3000
echo 📍 开发环境访问地址: http://localhost:5173
echo.
pause
