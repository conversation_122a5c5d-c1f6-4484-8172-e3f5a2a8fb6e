// 用户认证相关API服务
import { MINGDAO_AUTH_CONFIG, MINGDAO_DATA_TYPES, MINGDAO_FILTER_TYPES } from '../config/api.config';
import { UserAccount, LoginRequest, RegisterRequest } from '../types/document';

// API响应基础接口
interface AuthApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error_code?: string;
}

// 用户认证服务类
export class AuthService {
  private static instance: AuthService;
  private currentUser: UserAccount | null = null;
  private token: string | null = null;

  private constructor() {
    // 从localStorage恢复登录状态
    this.loadFromStorage();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // 从localStorage加载用户信息
  private loadFromStorage(): void {
    try {
      const token = localStorage.getItem('auth_token');
      const userStr = localStorage.getItem('current_user');
      
      if (token && userStr) {
        this.token = token;
        this.currentUser = JSON.parse(userStr);
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      this.clearStorage();
    }
  }

  // 保存到localStorage
  private saveToStorage(): void {
    if (this.token && this.currentUser) {
      localStorage.setItem('auth_token', this.token);
      localStorage.setItem('current_user', JSON.stringify(this.currentUser));
    }
  }

  // 清除localStorage
  private clearStorage(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('current_user');
    this.token = null;
    this.currentUser = null;
  }

  // 获取当前用户
  public getCurrentUser(): UserAccount | null {
    return this.currentUser;
  }

  // 获取认证token
  public getToken(): string | null {
    return this.token;
  }

  // 检查是否已登录
  public isAuthenticated(): boolean {
    return !!(this.token && this.currentUser);
  }

  // 用户注册
  public async register(registerData: RegisterRequest): Promise<AuthApiResponse<UserAccount>> {
    try {
      console.log('🚀 开始用户注册:', registerData.username);

      // 构建明道云API请求数据
      const controls = [
        { controlId: MINGDAO_AUTH_CONFIG.userFields.username, value: registerData.username },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.email, value: registerData.email },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.password_hash, value: btoa(registerData.password) }, // 简单编码
        { controlId: MINGDAO_AUTH_CONFIG.userFields.full_name, value: registerData.fullName || registerData.username },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.phone, value: registerData.phone || '' },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.status, value: 'active' },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.user_type, value: 'free' },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.balance, value: 0 },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.total_quota, value: 1000 }, // 免费1000字符
        { controlId: MINGDAO_AUTH_CONFIG.userFields.used_quota, value: 0 },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.monthly_quota, value: 0 },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.monthly_used, value: 0 },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.created_at, value: new Date().toISOString() }
      ];

      const requestData = {
        appKey: MINGDAO_AUTH_CONFIG.appKey,
        sign: MINGDAO_AUTH_CONFIG.sign,
        worksheetId: MINGDAO_AUTH_CONFIG.worksheets.users,
        triggerWorkflow: true,
        controls: controls
      };

      console.log('📤 发送注册请求:', requestData);

      const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/addRow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📥 注册响应:', result);

      if (result.success) {
        // 注册成功，创建用户对象
        const newUser: UserAccount = {
          id: result.data,
          username: registerData.username,
          email: registerData.email,
          fullName: registerData.fullName || registerData.username,
          phone: registerData.phone || '',
          status: 'active',
          userType: 'free',
          balance: 0,
          totalQuota: 1000,
          usedQuota: 0,
          monthlyQuota: 0,
          monthlyUsed: 0,
          createdAt: new Date().toISOString()
        };

        return {
          success: true,
          data: newUser,
          message: '注册成功！已分配1000字符免费额度'
        };
      } else {
        return {
          success: false,
          message: result.message || '注册失败',
          error_code: result.error_code
        };
      }

    } catch (error) {
      console.error('❌ 注册失败:', error);
      return {
        success: false,
        message: `注册失败: ${error}`,
        error_code: 'NETWORK_ERROR'
      };
    }
  }

  // 用户登录
  public async login(loginData: LoginRequest): Promise<AuthApiResponse<UserAccount>> {
    try {
      console.log('🔐 开始用户登录:', loginData.username);

      // 构建查询条件 - 尝试多个可能的用户名字段
      const filters = [
        {
          controlId: MINGDAO_AUTH_CONFIG.userFields.username,
          dataType: MINGDAO_DATA_TYPES.TEXT,
          spliceType: 1, // AND
          filterType: MINGDAO_FILTER_TYPES.EQUAL,
          value: loginData.username
        }
      ];

      const requestData = {
        appKey: MINGDAO_AUTH_CONFIG.appKey,
        sign: MINGDAO_AUTH_CONFIG.sign,
        worksheetId: MINGDAO_AUTH_CONFIG.worksheets.users,
        pageSize: 1,
        pageIndex: 1,
        listType: 0,
        controls: [], // 返回所有字段
        filters: filters
      };

      console.log('📤 发送登录查询请求:', requestData);

      const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/getFilterRows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📥 登录查询响应:', result);

      if (result.success && result.data && result.data.rows && result.data.rows.length > 0) {
        const userData = result.data.rows[0];

        // 验证密码 - 兼容多种密码存储格式
        const storedPasswordHash = userData[MINGDAO_AUTH_CONFIG.userFields.password_hash] || userData.password_hash;
        const inputPasswordHash = btoa(loginData.password);

        console.log('登录验证:', {
          username: loginData.username,
          storedHash: storedPasswordHash,
          inputHash: inputPasswordHash,
          userData: userData
        });

        if (storedPasswordHash === inputPasswordHash || storedPasswordHash === loginData.password) {
          // 密码正确，创建用户对象 - 兼容新旧字段名
          const user: UserAccount = {
            id: userData.rowid,
            username: userData[MINGDAO_AUTH_CONFIG.userFields.username] || userData.username,
            email: userData[MINGDAO_AUTH_CONFIG.userFields.email] || userData.email,
            fullName: userData[MINGDAO_AUTH_CONFIG.userFields.full_name] || userData.full_name || userData.fullName,
            phone: userData[MINGDAO_AUTH_CONFIG.userFields.phone] || userData.phone,
            status: userData[MINGDAO_AUTH_CONFIG.userFields.status] || userData.status || 'active',
            userType: userData[MINGDAO_AUTH_CONFIG.userFields.user_type] || userData.user_type || userData.userType || 'free',
            balance: parseFloat(userData[MINGDAO_AUTH_CONFIG.userFields.balance] || userData.balance || '0'),
            totalQuota: parseInt(userData[MINGDAO_AUTH_CONFIG.userFields.total_quota] || userData.total_quota || userData.totalQuota || '1000'),
            usedQuota: parseInt(userData[MINGDAO_AUTH_CONFIG.userFields.used_quota] || userData.used_quota || userData.usedQuota || '0'),
            monthlyQuota: parseInt(userData[MINGDAO_AUTH_CONFIG.userFields.monthly_quota] || userData.monthly_quota || userData.monthlyQuota || '0'),
            monthlyUsed: parseInt(userData[MINGDAO_AUTH_CONFIG.userFields.monthly_used] || userData.monthly_used || userData.monthlyUsed || '0'),
            monthlyExpireDate: userData[MINGDAO_AUTH_CONFIG.userFields.monthly_expire_date] || userData.monthly_expire_date || userData.monthlyExpireDate,
            lastLogin: new Date().toISOString(),
            createdAt: userData[MINGDAO_AUTH_CONFIG.userFields.created_at] || userData.created_at || userData.createdAt,
            updatedAt: userData[MINGDAO_AUTH_CONFIG.userFields.updated_at] || userData.updated_at || userData.updatedAt
          };

          // 生成简单的token（实际项目中应该使用JWT）
          const token = btoa(`${user.id}_${Date.now()}`);

          // 保存登录状态
          this.currentUser = user;
          this.token = token;
          this.saveToStorage();

          // 更新最后登录时间
          this.updateLastLoginTime(user.id);

          return {
            success: true,
            data: user,
            message: '登录成功'
          };
        } else {
          return {
            success: false,
            message: '密码错误',
            error_code: 'INVALID_PASSWORD'
          };
        }
      } else {
        return {
          success: false,
          message: '用户名不存在',
          error_code: 'USER_NOT_FOUND'
        };
      }

    } catch (error) {
      console.error('❌ 登录失败:', error);
      return {
        success: false,
        message: `登录失败: ${error}`,
        error_code: 'NETWORK_ERROR'
      };
    }
  }

  // 用户登出
  public logout(): void {
    console.log('👋 用户登出');
    this.clearStorage();
  }

  // 更新最后登录时间
  private async updateLastLoginTime(userId: string): Promise<void> {
    try {
      const controls = [
        { controlId: MINGDAO_AUTH_CONFIG.userFields.last_login, value: new Date().toISOString() },
        { controlId: MINGDAO_AUTH_CONFIG.userFields.updated_at, value: new Date().toISOString() }
      ];

      const requestData = {
        appKey: MINGDAO_AUTH_CONFIG.appKey,
        sign: MINGDAO_AUTH_CONFIG.sign,
        worksheetId: MINGDAO_AUTH_CONFIG.worksheets.users,
        rowId: userId,
        controls: controls
      };

      await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/editRow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      console.log('✅ 已更新最后登录时间');
    } catch (error) {
      console.error('⚠️ 更新最后登录时间失败:', error);
    }
  }

  // 刷新用户信息
  public async refreshUserInfo(): Promise<AuthApiResponse<UserAccount>> {
    if (!this.currentUser) {
      return {
        success: false,
        message: '用户未登录',
        error_code: 'NOT_AUTHENTICATED'
      };
    }

    try {
      const filters = [
        {
          controlId: 'rowid',
          dataType: MINGDAO_DATA_TYPES.TEXT,
          spliceType: 1,
          filterType: MINGDAO_FILTER_TYPES.EQUAL,
          value: this.currentUser.id
        }
      ];

      const requestData = {
        appKey: MINGDAO_AUTH_CONFIG.appKey,
        sign: MINGDAO_AUTH_CONFIG.sign,
        worksheetId: MINGDAO_AUTH_CONFIG.worksheets.users,
        pageSize: 1,
        pageIndex: 1,
        listType: 0,
        controls: [],
        filters: filters
      };

      const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/getFilterRows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      const result = await response.json();

      if (result.success && result.data && result.data.rows && result.data.rows.length > 0) {
        const userData = result.data.rows[0];
        
        const updatedUser: UserAccount = {
          ...this.currentUser,
          balance: parseFloat(userData[MINGDAO_AUTH_CONFIG.userFields.balance] || '0'),
          totalQuota: parseInt(userData[MINGDAO_AUTH_CONFIG.userFields.total_quota] || '0'),
          usedQuota: parseInt(userData[MINGDAO_AUTH_CONFIG.userFields.used_quota] || '0'),
          monthlyQuota: parseInt(userData[MINGDAO_AUTH_CONFIG.userFields.monthly_quota] || '0'),
          monthlyUsed: parseInt(userData[MINGDAO_AUTH_CONFIG.userFields.monthly_used] || '0'),
          monthlyExpireDate: userData[MINGDAO_AUTH_CONFIG.userFields.monthly_expire_date],
          updatedAt: userData[MINGDAO_AUTH_CONFIG.userFields.updated_at]
        };

        this.currentUser = updatedUser;
        this.saveToStorage();

        return {
          success: true,
          data: updatedUser,
          message: '用户信息已更新'
        };
      } else {
        return {
          success: false,
          message: '获取用户信息失败',
          error_code: 'USER_NOT_FOUND'
        };
      }

    } catch (error) {
      console.error('❌ 刷新用户信息失败:', error);
      return {
        success: false,
        message: `刷新失败: ${error}`,
        error_code: 'NETWORK_ERROR'
      };
    }
  }
}

// 导出单例实例
export const authService = AuthService.getInstance();
