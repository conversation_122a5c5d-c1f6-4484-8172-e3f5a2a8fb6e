"""
明道云API代理服务器 - 解决跨域问题
"""
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import httpx
import uvicorn
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="明道云API代理服务器", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 明道云配置
MINGDAO_CONFIG = {
    "base_url": "https://dmit.duoningbio.com/api/v2/open",
    "app_key": "d88c1d2329c42504",
    "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA=="
}

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "明道云API代理服务器",
        "version": "1.0.0",
        "endpoints": {
            "worksheet_info": "/api/mingdao/worksheet/info",
            "get_rows": "/api/mingdao/worksheet/rows",
            "add_row": "/api/mingdao/worksheet/add",
            "edit_row": "/api/mingdao/worksheet/edit",
            "pricing": "/api/mingdao/pricing"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "mingdao-proxy"}

@app.post("/api/mingdao/worksheet/info")
async def get_worksheet_info(request: Request):
    """获取工作表信息"""
    try:
        body = await request.json()
        worksheet_id = body.get("worksheetId")
        
        if not worksheet_id:
            raise HTTPException(status_code=400, detail="worksheetId is required")
        
        data = {
            "appKey": MINGDAO_CONFIG["app_key"],
            "sign": MINGDAO_CONFIG["sign"],
            "worksheetId": worksheet_id
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{MINGDAO_CONFIG['base_url']}/worksheet/getWorksheetInfo",
                json=data
            )
            
            result = response.json()
            logger.info(f"获取工作表信息: {worksheet_id}, 成功: {result.get('success')}")
            
            return result
            
    except Exception as e:
        logger.error(f"获取工作表信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/mingdao/worksheet/rows")
async def get_worksheet_rows(request: Request):
    """获取工作表数据"""
    try:
        body = await request.json()
        worksheet_id = body.get("worksheetId")
        page_size = body.get("pageSize", 20)
        page_index = body.get("pageIndex", 1)
        filters = body.get("filters", [])
        
        if not worksheet_id:
            raise HTTPException(status_code=400, detail="worksheetId is required")
        
        data = {
            "appKey": MINGDAO_CONFIG["app_key"],
            "sign": MINGDAO_CONFIG["sign"],
            "worksheetId": worksheet_id,
            "pageSize": page_size,
            "pageIndex": page_index,
            "listType": 0,
            "controls": [],
            "filters": filters
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{MINGDAO_CONFIG['base_url']}/worksheet/getFilterRows",
                json=data
            )
            
            result = response.json()
            logger.info(f"获取工作表数据: {worksheet_id}, 页码: {page_index}, 成功: {result.get('success')}")
            
            return result
            
    except Exception as e:
        logger.error(f"获取工作表数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/mingdao/worksheet/add")
async def add_worksheet_row(request: Request):
    """添加工作表行"""
    try:
        body = await request.json()
        worksheet_id = body.get("worksheetId")
        controls = body.get("controls", [])
        
        if not worksheet_id:
            raise HTTPException(status_code=400, detail="worksheetId is required")
        
        data = {
            "appKey": MINGDAO_CONFIG["app_key"],
            "sign": MINGDAO_CONFIG["sign"],
            "worksheetId": worksheet_id,
            "triggerWorkflow": True,
            "controls": controls
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{MINGDAO_CONFIG['base_url']}/worksheet/addRow",
                json=data
            )
            
            result = response.json()
            logger.info(f"添加工作表行: {worksheet_id}, 成功: {result.get('success')}")
            
            return result
            
    except Exception as e:
        logger.error(f"添加工作表行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/mingdao/worksheet/edit")
async def edit_worksheet_row(request: Request):
    """编辑工作表行"""
    try:
        body = await request.json()
        worksheet_id = body.get("worksheetId")
        row_id = body.get("rowId")
        controls = body.get("controls", [])
        
        if not worksheet_id or not row_id:
            raise HTTPException(status_code=400, detail="worksheetId and rowId are required")
        
        data = {
            "appKey": MINGDAO_CONFIG["app_key"],
            "sign": MINGDAO_CONFIG["sign"],
            "worksheetId": worksheet_id,
            "rowId": row_id,
            "triggerWorkflow": False,
            "controls": controls
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{MINGDAO_CONFIG['base_url']}/worksheet/editRow",
                json=data
            )
            
            result = response.json()
            logger.info(f"编辑工作表行: {worksheet_id}/{row_id}, 成功: {result.get('success')}")
            
            return result
            
    except Exception as e:
        logger.error(f"编辑工作表行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/mingdao/pricing")
async def get_pricing_rules():
    """获取收费规则"""
    return {
        "success": True,
        "pricing_rules": {
            "monthly": {
                "name": "月付套餐",
                "price": 20,
                "quota": 100000,
                "duration_days": 30,
                "description": "20元/月，10万字额度"
            },
            "per_translation_small": {
                "name": "按次收费-小文档",
                "min_chars": 1,
                "max_chars": 5000,
                "price": 5,
                "description": "5000字以内5元一次"
            },
            "per_translation_large": {
                "name": "按次收费-大文档",
                "min_chars": 5001,
                "price_per_5k": 5,
                "description": "超过5000字，每5000字5元"
            },
            "bulk_discount": {
                "name": "批量优惠",
                "min_chars": 300000,
                "price": 100,
                "description": "30万字100元"
            }
        }
    }

@app.post("/api/mingdao/calculate-cost")
async def calculate_cost(request: Request):
    """计算翻译费用"""
    try:
        body = await request.json()
        char_count = body.get("charCount", 0)
        
        if char_count <= 0:
            raise HTTPException(status_code=400, detail="字符数必须大于0")
        
        if char_count <= 5000:
            cost = 5
            payment_type = "按次付费"
            description = f"{char_count}字符，按次收费"
        elif char_count >= 300000:
            cost = 100
            payment_type = "批量优惠"
            description = f"{char_count}字符，批量优惠"
        else:
            # 超过5000字，每5000字5元
            cost = ((char_count - 1) // 5000 + 1) * 5
            payment_type = "按次付费"
            description = f"{char_count}字符，分段收费"
        
        return {
            "success": True,
            "char_count": char_count,
            "cost": cost,
            "payment_type": payment_type,
            "description": description
        }
        
    except Exception as e:
        logger.error(f"计算费用失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("🚀 启动明道云API代理服务器...")
    print("📖 API文档: http://localhost:9000/docs")
    print("🔗 代理地址: http://localhost:9000/api/mingdao/")
    print("=" * 50)
    
    uvicorn.run(
        "proxy_server:app",
        host="0.0.0.0",
        port=9000,
        reload=True,
        log_level="info"
    )
