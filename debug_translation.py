#!/usr/bin/env python3
"""
调试翻译功能
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_translation():
    try:
        from app.services.translation_service import TranslationService
        
        # 测试文档路径
        original_doc = r"d:\mywork\Translate\SMP-FM-003-01 厂房档案标准管理规程.docx"
        
        if not os.path.exists(original_doc):
            print(f"❌ 原始文档不存在: {original_doc}")
            return
        
        print(f"📄 测试文档: {original_doc}")
        
        # 读取文档内容
        with open(original_doc, 'rb') as f:
            content = f.read()
        
        print(f"📊 文档大小: {len(content)} bytes")
        
        # 测试翻译设置
        test_settings = {
            'paragraph': {
                'font_family': 'Arial', 'font_size': 10.5, 'bold': False, 'italic': False,
                'underline': False, 'text_align': 'inherit', 'color': '#000000'
            },
            'table': {
                'font_family': 'Arial', 'font_size': 6, 'bold': False, 'italic': False,
                'underline': False, 'text_align': 'inherit', 'color': '#000000'
            },
            'header': {
                'font_family': 'Arial', 'font_size': 10.5, 'bold': False, 'italic': False,
                'underline': False, 'text_align': 'inherit', 'color': '#000000'
            },
            'enable_paragraph': True,
            'enable_table': True,
            'enable_header': True
        }
        
        print("🔧 使用测试翻译设置:")
        print(f"  段落翻译: {test_settings['enable_paragraph']}")
        print(f"  表格翻译: {test_settings['enable_table']}")
        print(f"  页眉翻译: {test_settings['enable_header']}")
        
        # 调用翻译函数
        print("\n🚀 开始翻译...")
        translated_content = await TranslationService._extract_docx_text(content, test_settings)
        
        print(f"✅ 翻译完成，输出大小: {len(translated_content)} bytes")
        
        # 保存测试结果
        test_output = r"d:\mywork\Translate\test_translation_output.docx"
        with open(test_output, 'wb') as f:
            f.write(translated_content)
        
        print(f"💾 测试结果保存到: {test_output}")
        
        # 分析输出文档
        print("\n🔍 分析翻译结果...")
        from docx import Document
        import io
        
        doc_stream = io.BytesIO(translated_content)
        doc = Document(doc_stream)
        
        print(f"📊 翻译后文档统计:")
        print(f"  段落数: {len(doc.paragraphs)}")
        print(f"  表格数: {len(doc.tables)}")
        
        # 检查页眉
        header_count = 0
        for section in doc.sections:
            if section.header:
                header_count += 1
                print(f"  页眉段落数: {len(section.header.paragraphs)}")
                print(f"  页眉表格数: {len(section.header.tables)}")
        
        print(f"  页眉数: {header_count}")
        
        # 检查第一个表格
        if len(doc.tables) > 0:
            table = doc.tables[0]
            print(f"\n📋 第一个表格:")
            print(f"  行数: {len(table.rows)}")
            print(f"  列数: {len(table.rows[0].cells) if len(table.rows) > 0 else 0}")
            
            # 检查前几个单元格的内容
            for row_idx, row in enumerate(table.rows[:2]):
                for col_idx, cell in enumerate(row.cells[:2]):
                    cell_text = ""
                    for para in cell.paragraphs:
                        cell_text += para.text + " "
                    cell_text = cell_text.strip()
                    if cell_text:
                        print(f"    [{row_idx},{col_idx}]: {cell_text[:50]}...")
        
        print("\n🎊 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_translation())
