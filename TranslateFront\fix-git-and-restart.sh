#!/bin/bash

echo "🔧 修复 Git 冲突并重新启动服务"
echo "================================"

# 1. 停止所有 PM2 进程
echo "1. ⏹️  停止所有 PM2 进程..."
pm2 delete all 2>/dev/null || echo "没有运行的 PM2 进程"

# 2. 备份本地修改
echo "2. 💾 备份本地修改..."
cp start-production.sh start-production.sh.backup 2>/dev/null || echo "没有需要备份的文件"

# 3. 暂存本地修改
echo "3. 📦 暂存本地修改..."
git stash

# 4. 拉取最新代码
echo "4. 📥 拉取最新代码..."
git pull origin main

# 5. 恢复本地修改（如果需要）
echo "5. 🔄 检查是否需要恢复本地修改..."
if git stash list | grep -q "stash@{0}"; then
    echo "发现暂存的修改，尝试恢复..."
    git stash pop || echo "自动合并失败，请手动处理冲突"
fi

# 6. 确保脚本有执行权限
echo "6. 🔑 设置脚本执行权限..."
chmod +x *.sh

# 7. 检查必要文件
echo "7. 📋 检查必要文件..."
if [ ! -f "ecosystem.config.cjs" ]; then
    echo "❌ ecosystem.config.cjs 文件不存在"
    echo "正在创建..."
    cat > ecosystem.config.cjs << 'EOF'
module.exports = {
  apps: [{
    name: 'translate-front',
    script: 'server.cjs',
    cwd: './',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    min_uptime: '10s',
    max_restarts: 10,
    kill_timeout: 5000,
    listen_timeout: 3000,
    health_check_grace_period: 3000
  }]
};
EOF
    echo "✅ ecosystem.config.cjs 文件已创建"
fi

# 8. 创建日志目录
echo "8. 📁 创建日志目录..."
mkdir -p logs

# 9. 构建项目
echo "9. 🔨 构建项目..."
npm run build

# 10. 启动 PM2 服务
echo "10. 🚀 启动 PM2 服务..."
pm2 start ecosystem.config.cjs

# 11. 等待服务启动
echo "11. ⏳ 等待服务启动..."
sleep 5

# 12. 检查服务状态
echo "12. 📊 检查服务状态..."
pm2 status

# 13. 检查端口监听
echo "13. 🔌 检查端口监听..."
if netstat -tlnp | grep :3000 > /dev/null; then
    echo "✅ 端口 3000 正在监听"
else
    echo "❌ 端口 3000 未在监听，查看错误日志..."
    pm2 logs translate-front --err --lines 10
fi

# 14. 测试服务
echo "14. 🧪 测试服务..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ HTTP 服务正常 (状态码: $HTTP_CODE)"
else
    echo "⚠️  HTTP 响应异常 (状态码: $HTTP_CODE)"
fi

# 15. 保存 PM2 配置
echo "15. 💾 保存 PM2 配置..."
pm2 save

# 16. 显示结果
echo ""
echo "🎉 修复完成！"
echo "=============="

SERVER_IP=$(hostname -I | awk '{print $1}')
echo ""
echo "🌐 访问地址: http://${SERVER_IP}:3000"
echo ""
echo "📝 常用命令:"
echo "   查看状态: pm2 status"
echo "   查看日志: pm2 logs translate-front"
echo "   查看错误: pm2 logs translate-front --err"
echo "   重启服务: pm2 restart translate-front"
echo ""

# 17. 最终检查
echo "🔍 最终检查:"
echo "PM2 进程列表:"
pm2 list
echo ""
echo "端口监听状态:"
netstat -tlnp | grep :3000 || echo "端口 3000 未在监听"
