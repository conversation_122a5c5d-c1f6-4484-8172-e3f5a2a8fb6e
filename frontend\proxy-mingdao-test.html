<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明道云API代理测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .config-info {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .config-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .config-info code {
            background: #bbdefb;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #4caf50;
        }
        
        .status-offline {
            background: #f44336;
        }
        
        input[type="number"] {
            width: 150px;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 明道云API代理测试</h1>
        
        <div class="config-info">
            <h3>📋 代理服务器配置</h3>
            <p>代理地址: <code>http://localhost:9000</code></p>
            <p>API文档: <code>http://localhost:9000/docs</code></p>
            <p>状态: <span class="status-indicator" id="proxyStatus"></span><span id="proxyStatusText">检测中...</span></p>
        </div>
        
        <!-- 代理服务器状态 -->
        <div class="section">
            <h2>🔗 代理服务器状态</h2>
            <button onclick="checkProxyStatus()">检查代理状态</button>
            <button onclick="window.open('http://localhost:9000/docs', '_blank')">打开API文档</button>
            <div id="proxyResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 收费规则测试 -->
        <div class="section">
            <h2>💰 收费规则测试</h2>
            <p>输入字符数计算费用：</p>
            <input type="number" id="charCount" placeholder="输入字符数" value="8000" min="1">
            <button onclick="calculateCost()">计算费用</button>
            <button onclick="getPricingRules()">获取收费规则</button>
            <div id="pricingResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 获取工作表信息 -->
        <div class="section">
            <h2>📊 获取工作表信息</h2>
            <button onclick="getWorksheetInfo('6886e20ba849420e13f69b23')">获取用户表结构</button>
            <button onclick="getWorksheetInfo('6886f053a849420e13f69b61')">获取翻译表结构</button>
            <button onclick="getWorksheetInfo('6886f9ffa849420e13f69bc5')">获取消费记录表结构</button>
            <div id="worksheetResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 查询用户数据 -->
        <div class="section">
            <h2>👤 查询用户数据</h2>
            <button onclick="getUserData()">获取用户列表</button>
            <button onclick="searchUser('testuser')">搜索测试用户</button>
            <div id="userResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 查询翻译记录 -->
        <div class="section">
            <h2>📚 查询翻译记录</h2>
            <button onclick="getTranslationData()">获取翻译记录</button>
            <div id="translationResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const PROXY_BASE = 'http://localhost:9000';

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        function updateProxyStatus(isOnline, message) {
            const statusIndicator = document.getElementById('proxyStatus');
            const statusText = document.getElementById('proxyStatusText');
            
            statusIndicator.className = `status-indicator ${isOnline ? 'status-online' : 'status-offline'}`;
            statusText.textContent = message;
        }

        // 检查代理服务器状态
        async function checkProxyStatus() {
            try {
                const response = await fetch(`${PROXY_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    updateProxyStatus(true, '代理服务器在线');
                    showResult('proxyResult', data);
                } else {
                    updateProxyStatus(false, '代理服务器响应异常');
                    showResult('proxyResult', data, true);
                }
            } catch (error) {
                updateProxyStatus(false, '代理服务器离线');
                showResult('proxyResult', { error: error.message }, true);
            }
        }

        // 计算费用
        async function calculateCost() {
            const charCount = parseInt(document.getElementById('charCount').value);
            
            if (!charCount || charCount <= 0) {
                showResult('pricingResult', { error: '请输入有效的字符数' }, true);
                return;
            }

            try {
                const response = await fetch(`${PROXY_BASE}/api/mingdao/calculate-cost`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ charCount })
                });

                const data = await response.json();
                showResult('pricingResult', data, !response.ok);
            } catch (error) {
                showResult('pricingResult', { error: error.message }, true);
            }
        }

        // 获取收费规则
        async function getPricingRules() {
            try {
                const response = await fetch(`${PROXY_BASE}/api/mingdao/pricing`);
                const data = await response.json();
                showResult('pricingResult', data, !response.ok);
            } catch (error) {
                showResult('pricingResult', { error: error.message }, true);
            }
        }

        // 获取工作表信息
        async function getWorksheetInfo(worksheetId) {
            try {
                const response = await fetch(`${PROXY_BASE}/api/mingdao/worksheet/info`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ worksheetId })
                });

                const data = await response.json();
                
                if (data.success) {
                    const summary = {
                        worksheetName: data.data.name,
                        worksheetId: data.data.worksheetId,
                        fieldsCount: data.data.controls.length,
                        fields: data.data.controls.map(control => ({
                            id: control.controlId,
                            name: control.controlName,
                            type: control.type,
                            required: control.required
                        }))
                    };
                    showResult('worksheetResult', summary);
                } else {
                    showResult('worksheetResult', data, true);
                }
            } catch (error) {
                showResult('worksheetResult', { error: error.message }, true);
            }
        }

        // 获取用户数据
        async function getUserData() {
            try {
                const response = await fetch(`${PROXY_BASE}/api/mingdao/worksheet/rows`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        worksheetId: '6886e20ba849420e13f69b23',
                        pageSize: 10,
                        pageIndex: 1,
                        filters: []
                    })
                });

                const data = await response.json();
                showResult('userResult', data, !data.success);
            } catch (error) {
                showResult('userResult', { error: error.message }, true);
            }
        }

        // 搜索用户
        async function searchUser(username) {
            try {
                const response = await fetch(`${PROXY_BASE}/api/mingdao/worksheet/rows`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        worksheetId: '6886e20ba849420e13f69b23',
                        pageSize: 10,
                        pageIndex: 1,
                        filters: [
                            {
                                controlId: '6886e20ba849420e13f69b24', // 用户名字段ID
                                dataType: 2,
                                spliceType: 1,
                                filterType: 1,
                                value: username
                            }
                        ]
                    })
                });

                const data = await response.json();
                showResult('userResult', data, !data.success);
            } catch (error) {
                showResult('userResult', { error: error.message }, true);
            }
        }

        // 获取翻译记录
        async function getTranslationData() {
            try {
                const response = await fetch(`${PROXY_BASE}/api/mingdao/worksheet/rows`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        worksheetId: '6886f053a849420e13f69b61',
                        pageSize: 10,
                        pageIndex: 1,
                        filters: []
                    })
                });

                const data = await response.json();
                showResult('translationResult', data, !data.success);
            } catch (error) {
                showResult('translationResult', { error: error.message }, true);
            }
        }

        // 页面加载完成后自动检查代理状态
        window.addEventListener('load', function() {
            console.log('🚀 明道云API代理测试页面已加载');
            checkProxyStatus();
        });
    </script>
</body>
</html>
