.mingdao-pricing {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.pricing-header {
  text-align: center;
  margin-bottom: 40px;
}

.pricing-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #333;
  margin-bottom: 10px;
  font-size: 2rem;
}

.pricing-header p {
  color: #666;
  font-size: 1.1rem;
}

/* 费用计算器 */
.pricing-calculator {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 40px;
  border: 2px solid #e9ecef;
}

.pricing-calculator h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #495057;
  margin-bottom: 20px;
}

.calculator-input {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.calculator-input label {
  font-weight: 600;
  color: #495057;
  min-width: 120px;
}

.calculator-input input {
  flex: 1;
  min-width: 200px;
  padding: 10px 15px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.calculator-input input:focus {
  outline: none;
  border-color: #667eea;
}

.calculate-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: transform 0.2s;
}

.calculate-btn:hover {
  transform: translateY(-2px);
}

.calculation-result {
  margin-top: 20px;
}

.result-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  border: 2px solid #e3f2fd;
}

.result-card h4 {
  color: #1976d2;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.result-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.result-item .label {
  font-weight: 600;
  color: #666;
}

.result-item .value {
  font-weight: 700;
  color: #333;
}

.result-item .value.cost {
  color: #e91e63;
  font-size: 1.2rem;
}

/* 收费规则网格 */
.pricing-rules {
  margin-bottom: 40px;
}

.pricing-rules h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.5rem;
}

.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.rule-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  border: 2px solid #e9ecef;
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

.rule-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.rule-card.free {
  border-color: #4caf50;
  background: linear-gradient(135deg, #e8f5e8, #ffffff);
}

.rule-card.small {
  border-color: #ff9800;
  background: linear-gradient(135deg, #fff3e0, #ffffff);
}

.rule-card.large {
  border-color: #2196f3;
  background: linear-gradient(135deg, #e3f2fd, #ffffff);
}

.rule-card.monthly {
  border-color: #9c27b0;
  background: linear-gradient(135deg, #f3e5f5, #ffffff);
}

.rule-card.bulk {
  border-color: #f44336;
  background: linear-gradient(135deg, #ffebee, #ffffff);
}

.rule-icon {
  margin-bottom: 15px;
}

.rule-card.free .rule-icon { color: #4caf50; }
.rule-card.small .rule-icon { color: #ff9800; }
.rule-card.large .rule-icon { color: #2196f3; }
.rule-card.monthly .rule-icon { color: #9c27b0; }
.rule-card.bulk .rule-icon { color: #f44336; }

.rule-card h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.rule-price {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.rule-desc {
  color: #666;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.rule-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.rule-features li {
  padding: 5px 0;
  color: #555;
  font-size: 0.9rem;
}

.rule-features li:before {
  content: "✓";
  color: #4caf50;
  font-weight: bold;
  margin-right: 8px;
}

/* 示例表格 */
.pricing-examples {
  margin-bottom: 40px;
}

.pricing-examples h3 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.examples-table {
  overflow-x: auto;
}

.examples-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.examples-table th,
.examples-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.examples-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.examples-table tr:hover {
  background: #f8f9fa;
}

.examples-table tr:last-child td {
  border-bottom: none;
}

/* 付费说明 */
.pricing-notes {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  border: 2px solid #e9ecef;
}

.pricing-notes h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.notes-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.note-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.note-item h4 {
  color: #333;
  margin-bottom: 8px;
  font-size: 1rem;
}

.note-item p {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mingdao-pricing {
    padding: 15px;
  }
  
  .calculator-input {
    flex-direction: column;
    align-items: stretch;
  }
  
  .calculator-input label {
    min-width: auto;
  }
  
  .calculator-input input {
    min-width: auto;
  }
  
  .rules-grid {
    grid-template-columns: 1fr;
  }
  
  .result-details {
    grid-template-columns: 1fr;
  }
  
  .notes-content {
    grid-template-columns: 1fr;
  }
  
  .pricing-header h2 {
    font-size: 1.5rem;
  }
  
  .rule-price {
    font-size: 1.5rem;
  }
}
