// API 配置文件 - PHP 代理版本
// 适用于无法安装 Node.js 的服务器

export const API_CONFIG = {
  // API 基础配置 - 使用 PHP 代理
  baseUrl: '/api-proxy.php?path=',
  appKey: 'd88c1d2329c42504',
  sign: 'YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==',
  worksheetId: 'fywd',

  // 文件上传相关配置
  upload: {
    // 原始文件控制项ID
    originalFileControlId: 'YWJ',
    // 处理后文件控制项ID
    processedFileControlId: 'FYWJ',
    // 备注控制项ID
    remarkControlId: '68789e5fa849420e13f65dcf',
    // 默认拥有者ID（可选）
    defaultOwnerId: '',
    // 最大文件大小（MB）
    maxFileSize: 50,
    // 支持的文件类型
    allowedTypes: ['.doc', '.docx', '.pdf', '.txt', '.xlsx', '.xls', '.pptx', '.ppt'],
    // 是否触发工作流
    triggerWorkflow: true
  },

  // 分页配置
  pagination: {
    defaultPageSize: 50,
    maxPageSize: 100
  },

  // 默认过滤器（简化版，不使用过滤条件）
  defaultFilters: [],

  // 请求配置
  request: {
    timeout: 30000, // 30秒超时
    retryCount: 3,   // 重试次数
    retryDelay: 1000 // 重试延迟（毫秒）
  }
};

// 控制项ID映射（方便管理）
export const CONTROL_IDS = {
  ORIGINAL_FILE: 'YWJ',        // 原文件
  PROCESSED_FILE: 'FYWJ',      // 翻译文件（用于预览）
  REMARK: '68789e5fa849420e13f65dcf',           // 备注
  OWNER: 'ownerid'                             // 拥有者
} as const;

// 预览相关配置
export const PREVIEW_CONFIG = {
  // 原始预览地址基础URL（备用）
  baseUrl: 'https://dmit.duoningbio.com/rowfile',
  // 翻译后文件的控制项ID（用于预览）
  previewControlId: CONTROL_IDS.PROCESSED_FILE,

  // OnlyOffice 在线预览服务配置（暂时禁用）
  onlyoffice: {
    // OnlyOffice Document Server 地址
    serverUrl: 'http://10.250.200.231:8088/',
    // 是否启用 OnlyOffice 预览（暂时禁用）
    enabled: false,
    // 支持的文件类型
    supportedFormats: ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'pdf', 'txt'],
    // JWT 配置
    jwt: {
      // JWT 密钥
      secret: 'ddFgc9cO9WkeIyRkm2hDGcg1LFIV6HBW',
      // JWT 头部名称
      header: 'Authorization',
      // 是否启用 JWT
      enabled: true
    },
    // 编辑器配置
    editor: {
      // 编辑模式：view（只读）、edit（编辑）
      mode: 'view',
      // 语言设置
      lang: 'zh-CN'
    }
  }
} as const;

// 其他配置保持不变...
export const FILE_TYPE_MAP = {
  1: 'image',
  2: 'document',
  3: 'video',
  4: 'audio'
} as const;

export const DATA_TYPES = {
  TEXT: 2,
  NUMBER: 6,
  DATE: 15,
  ATTACHMENT: 14,
  USER: 26
} as const;

export const FILTER_TYPES = {
  EQUAL: 1,
  NOT_EQUAL: 2,
  CONTAINS: 3,
  NOT_CONTAINS: 4,
  STARTS_WITH: 5,
  ENDS_WITH: 6,
  GREATER_THAN: 7,
  LESS_THAN: 8,
  GREATER_EQUAL: 9,
  LESS_EQUAL: 10,
  BETWEEN: 11,
  NOT_BETWEEN: 12,
  IS_NULL: 13,
  IS_NOT_NULL: 14
} as const;

export const SPLICE_TYPES = {
  AND: 1,
  OR: 2
} as const;
