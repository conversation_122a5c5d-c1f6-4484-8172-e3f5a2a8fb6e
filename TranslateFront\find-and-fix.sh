#!/bin/bash

echo "🔍 查找并修复项目目录和依赖问题"
echo "=================================="

# 1. 查找项目目录
echo "1. 🔍 查找项目目录..."
echo "当前目录: $(pwd)"
echo "目录内容:"
ls -la

# 查找 package.json 文件
echo ""
echo "查找 package.json 文件:"
PACKAGE_LOCATIONS=$(find /root -name "package.json" -type f 2>/dev/null | grep -v node_modules | head -5)

if [ -z "$PACKAGE_LOCATIONS" ]; then
    echo "❌ 未找到 package.json 文件"
    echo "可能需要重新克隆项目"
    exit 1
else
    echo "找到的 package.json 文件:"
    echo "$PACKAGE_LOCATIONS"
fi

# 选择正确的项目目录
PROJECT_DIR=""
for location in $PACKAGE_LOCATIONS; do
    dir=$(dirname "$location")
    if [[ "$dir" == *"TranslateFront"* ]] && [ -f "$dir/server.cjs" ]; then
        PROJECT_DIR="$dir"
        break
    fi
done

if [ -z "$PROJECT_DIR" ]; then
    # 如果没找到包含 server.cjs 的目录，选择第一个包含 TranslateFront 的目录
    for location in $PACKAGE_LOCATIONS; do
        dir=$(dirname "$location")
        if [[ "$dir" == *"TranslateFront"* ]]; then
            PROJECT_DIR="$dir"
            break
        fi
    done
fi

if [ -z "$PROJECT_DIR" ]; then
    echo "❌ 无法找到正确的项目目录"
    echo "请手动检查项目位置"
    exit 1
fi

echo "✅ 找到项目目录: $PROJECT_DIR"

# 2. 切换到项目目录
echo ""
echo "2. 📁 切换到项目目录..."
cd "$PROJECT_DIR"
echo "当前目录: $(pwd)"
echo "目录内容:"
ls -la

# 3. 检查必要文件
echo ""
echo "3. 📋 检查必要文件..."
if [ -f "package.json" ]; then
    echo "✅ package.json 存在"
else
    echo "❌ package.json 不存在"
    exit 1
fi

if [ -f "server.cjs" ]; then
    echo "✅ server.cjs 存在"
else
    echo "❌ server.cjs 不存在"
    echo "查找 server.cjs 文件:"
    find /root -name "server.cjs" -type f 2>/dev/null
fi

# 4. 修复 CentOS 7 镜像源问题
echo ""
echo "4. 🔧 修复 CentOS 镜像源..."
if [ -f "/etc/centos-release" ]; then
    CENTOS_VERSION=$(cat /etc/centos-release | grep -oE '[0-9]+' | head -1)
    if [ "$CENTOS_VERSION" = "7" ]; then
        echo "检测到 CentOS 7，修复镜像源..."
        
        # 备份原始源
        sudo cp /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup 2>/dev/null
        
        # 使用阿里云镜像源
        sudo tee /etc/yum.repos.d/CentOS-Base.repo > /dev/null << 'EOF'
[base]
name=CentOS-$releasever - Base - Aliyun
baseurl=http://mirrors.aliyun.com/centos/$releasever/os/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[updates]
name=CentOS-$releasever - Updates - Aliyun
baseurl=http://mirrors.aliyun.com/centos/$releasever/updates/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[extras]
name=CentOS-$releasever - Extras - Aliyun
baseurl=http://mirrors.aliyun.com/centos/$releasever/extras/$basearch/
gpgcheck=1
gpgkey=http://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7
EOF
        
        # 清理缓存
        sudo yum clean all
        sudo yum makecache
        
        echo "✅ CentOS 镜像源已修复"
    fi
fi

# 5. 手动安装 Node.js 18（避免使用有问题的源）
echo ""
echo "5. 🚀 手动安装 Node.js 18..."
NODE_VERSION=$(node --version 2>/dev/null | sed 's/v//' | cut -d. -f1)
if [ -z "$NODE_VERSION" ] || [ "$NODE_VERSION" -lt 18 ]; then
    echo "需要安装/升级 Node.js..."
    
    cd /tmp
    
    # 下载 Node.js 18
    echo "下载 Node.js 18.19.0..."
    wget -q https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-x64.tar.xz
    
    if [ $? -eq 0 ]; then
        echo "解压 Node.js..."
        tar -xf node-v18.19.0-linux-x64.tar.xz
        
        echo "安装 Node.js..."
        sudo cp -rf node-v18.19.0-linux-x64/* /usr/local/
        
        # 创建软链接
        sudo ln -sf /usr/local/bin/node /usr/bin/node
        sudo ln -sf /usr/local/bin/npm /usr/bin/npm
        sudo ln -sf /usr/local/bin/npx /usr/bin/npx
        
        # 清理
        rm -rf node-v18.19.0-linux-x64*
        
        echo "✅ Node.js 安装完成"
        echo "版本: $(node --version)"
        echo "npm 版本: $(npm --version)"
    else
        echo "❌ Node.js 下载失败"
        echo "尝试使用现有版本..."
    fi
    
    # 返回项目目录
    cd "$PROJECT_DIR"
else
    echo "✅ Node.js 版本满足要求: v$NODE_VERSION"
fi

# 6. 停止现有的 PM2 进程
echo ""
echo "6. ⏹️  停止现有 PM2 进程..."
pm2 delete all 2>/dev/null || echo "没有运行的进程"

# 7. 清理并重新安装依赖
echo ""
echo "7. 🧹 清理并重新安装依赖..."
rm -rf node_modules package-lock.json .npm
npm cache clean --force 2>/dev/null

echo "安装依赖..."
npm install

# 检查关键依赖
if [ ! -d "node_modules/express" ]; then
    echo "手动安装 express..."
    npm install express
fi

# 8. 尝试构建项目
echo ""
echo "8. 🔨 构建项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "⚠️  构建失败，创建基本的 dist 目录..."
    mkdir -p dist
    cat > dist/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMILE TRANS</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .loading { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SMILE TRANS</h1>
        <h2>Word文档带格式中英文上下对照翻译系统</h2>
        <p class="loading">系统正在启动，请稍候...</p>
        <p>如果页面长时间未加载，请联系管理员。</p>
    </div>
    <script>
        setTimeout(function() {
            window.location.reload();
        }, 5000);
    </script>
</body>
</html>
EOF
    echo "✅ 创建了基本的静态页面"
fi

# 9. 创建 PM2 配置
echo ""
echo "9. ⚙️  创建 PM2 配置..."
cat > ecosystem.config.cjs << EOF
module.exports = {
  apps: [{
    name: 'translate-front',
    script: 'server.cjs',
    cwd: '$PROJECT_DIR',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    min_uptime: '10s',
    max_restarts: 10
  }]
};
EOF

mkdir -p logs

# 10. 启动服务
echo ""
echo "10. 🚀 启动服务..."
if [ -f "server.cjs" ]; then
    pm2 start ecosystem.config.cjs
else
    echo "❌ server.cjs 文件不存在，无法启动服务"
    echo "查找 server.cjs:"
    find /root -name "server.cjs" -type f 2>/dev/null
    exit 1
fi

# 11. 等待并检查服务
echo ""
echo "11. 📊 检查服务状态..."
sleep 5
pm2 status

# 12. 测试连接
echo ""
echo "12. 🧪 测试服务..."
sleep 3

# 安装网络工具
if ! command -v netstat &> /dev/null; then
    echo "安装网络工具..."
    sudo yum install -y net-tools 2>/dev/null || echo "网络工具安装跳过"
fi

# 检查端口
if netstat -tlnp 2>/dev/null | grep :3000 > /dev/null; then
    echo "✅ 端口 3000 正在监听"
else
    echo "❌ 端口 3000 未在监听"
    echo "查看错误日志:"
    pm2 logs translate-front --err --lines 10
fi

# HTTP 测试
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null || echo "000")
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ HTTP 服务正常"
else
    echo "⚠️  HTTP 响应: $HTTP_CODE"
fi

# 13. 保存配置
pm2 save

echo ""
echo "🎉 修复完成！"
echo "=============="
echo ""
echo "📊 最终状态:"
pm2 list
echo ""
echo "🌐 访问地址:"
SERVER_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")
echo "   http://${SERVER_IP}:3000"
echo ""
echo "📝 管理命令:"
echo "   pm2 status"
echo "   pm2 logs translate-front"
echo "   pm2 restart translate-front"
