// API 配置文件
// 翻译系统后端API配置

export const API_CONFIG = {
  // API 基础配置 - 使用我们的翻译系统后端
  baseUrl: 'http://localhost:8000/api/v1',
  endpoints: {
    // 认证相关
    login: '/auth/login',
    register: '/auth/register',
    profile: '/auth/profile',

    // 文件上传相关
    upload: '/upload',
    uploadList: '/upload',

    // 翻译相关
    translate: '/translate',
    translateList: '/translate',
    translateProgress: '/translate/{id}/progress',
    translateResult: '/translate/{id}',
    translateDownload: '/translate/{id}/download',
    translateDownloadDocx: '/translate/{id}/download/docx',
    translatePreviewHtml: '/translate/{id}/preview/html',

    // 管理相关
    users: '/admin/users',
    logs: '/admin/logs'
  },

  // 文件上传相关配置
  upload: {
    // 最大文件大小（MB）
    maxFileSize: 10,
    // 支持的文件类型
    allowedTypes: ['.doc', '.docx'],
    // 上传路径
    uploadPath: '/upload'
  },

  // 分页配置
  pagination: {
    defaultPageSize: 20, // 默认一页20条
    maxPageSize: 100,
    pageSizeOptions: [10, 20, 50, 100] // 分页选项
  },

  // 翻译配置
  translation: {
    // 支持的语言 - 固定为中文翻译英文
    supportedLanguages: {
      'zh': '中文',
      'en': '英语'
    },
    // 固定语言对 - 只支持中文翻译英文
    defaultSourceLang: 'zh',
    defaultTargetLang: 'en',
    // 是否允许更改语言
    allowLanguageChange: false
  },

  // 请求配置
  request: {
    timeout: 30000, // 30秒超时
    retryCount: 3,   // 重试次数
    retryDelay: 1000 // 重试延迟（毫秒）
  }
};

// 明道云API配置（完整翻译系统）
export const MINGDAO_CONFIG = {
  baseUrl: 'https://dmit.duoningbio.com/api/v2/open',
  appKey: 'd88c1d2329c42504',
  sign: 'YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==',

  // 工作表ID
  worksheets: {
    users: '6886e20ba849420e13f69b23',
    translations: '6886f053a849420e13f69b61',
    consumption_records: '6886f9ffa849420e13f69bc5',
    // 兼容旧版本
    fywd: 'fywd'
  },

  endpoints: {
    getRows: '/worksheet/getFilterRows',
    getTotalNum: '/worksheet/getFilterRowsTotalNum',
    addRow: '/worksheet/addRow',
    editRow: '/worksheet/editRow',
    getWorksheetInfo: '/worksheet/getWorksheetInfo'
  },

  // 用户表字段ID
  userFields: {
    username: '6886e20ba849420e13f69b24',
    email: '6886e4c8a849420e13f69b30',
    password_hash: '6886e4c8a849420e13f69b31',
    full_name: '6886e4c8a849420e13f69b32',
    phone: '6886e4c8a849420e13f69b33',
    user_type: '6886e4c8a849420e13f69b34',
    status: '6886e4c8a849420e13f69b35',
    balance: '6886e4c8a849420e13f69b36',
    total_quota: '6886e4c8a849420e13f69b37',
    used_quota: '6886e4c8a849420e13f69b38',
    monthly_quota: '6886e4c8a849420e13f69b39',
    monthly_used: '6886e4c8a849420e13f69b3a',
    monthly_expire_date: '6886e4c8a849420e13f69b3b',
    last_login: '6886e4c8a849420e13f69b3c',
    created_at: '6886e4c8a849420e13f69b3d',
    updated_at: '6886e4c8a849420e13f69b3e'
  },

  // 翻译记录表字段ID
  translationFields: {
    original_file: '6886f7a4a849420e13f69b6f',
    translated_file: '6886f7a4a849420e13f69b70',
    status: '6886f7a4a849420e13f69b71',
    total_characters: '6886f7a4a849420e13f69b72',
    progress: '6886f7a4a849420e13f69b73',
    cost: '6886f7a4a849420e13f69b74',
    payment_type: '6886f7a4a849420e13f69b75',
    confidence_score: '6886f7a4a849420e13f69b76',
    error_message: '6886f7a4a849420e13f69b77',
    remark: '6886f7a4a849420e13f69b78',
    created_at: '6886f7a4a849420e13f69b79',
    updated_at: '6886f7a4a849420e13f69b7a',
    completed_at: '6886f7a4a849420e13f69b7b',
    user_id: '6886f887a849420e13f69b8f'
  },

  // 消费记录表字段ID
  consumptionFields: {
    consumption_id: '6886fbeda849420e13f69bd2',
    user_id: '6886fbeda849420e13f69bd3',
    translation_id: '6886fbeda849420e13f69bd5',
    consumption_type: '6886fbeda849420e13f69bd7',
    characters_used: '6886fbeda849420e13f69bd8',
    amount: '6886fbeda849420e13f69bd9',
    payment_source: '6886fbeda849420e13f69bda',
    before_balance: '6886fbeda849420e13f69bdb',
    after_balance: '6886fbeda849420e13f69bdc',
    before_quota: '6886fbeda849420e13f69bdd',
    after_quota: '6886fbeda849420e13f69bde',
    remark: '6886fbeda849420e13f69bdf'
  },

  // 兼容旧版本的字段ID
  controlIds: {
    originalFile: 'YWJ',
    translatedFile: 'FYWJ',
    remark: '68789e5fa849420e13f65dcf',
    owner: 'ownerid'
  },

  // 收费规则
  pricingRules: {
    monthly: { price: 20, quota: 100000, duration_days: 30 },
    per_translation_small: { min_chars: 1, max_chars: 5000, price: 5 },
    per_translation_large: { min_chars: 5001, price_per_5k: 5 },
    bulk_discount: { min_chars: 300000, price: 100 }
  }
};

// 认证相关配置
export const AUTH_CONFIG = {
  // Token存储键名
  tokenKey: 'translate_token',
  // Token过期时间（小时）
  tokenExpiry: 24,
  // 刷新Token的时间阈值（小时）
  refreshThreshold: 2
} as const;

// 预览相关配置
export const PREVIEW_CONFIG = {
  // OnlyOffice 在线预览服务配置
  onlyoffice: {
    // OnlyOffice Document Server 地址
    serverUrl: 'http://10.250.200.231:8088/',
    // 是否启用 OnlyOffice 预览
    enabled: true,
    // 支持的文件类型
    supportedFormats: ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'pdf', 'txt'],
    // JWT 配置
    jwt: {
      // JWT 密钥
      secret: 'ddFgc9cO9WkeIyRkm2hDGcg1LFIV6HBW',
      // JWT 头部名称
      header: 'Authorization',
      // 是否启用 JWT
      enabled: true
    },
    // 编辑器配置
    editor: {
      // 编辑模式：view（只读）、edit（编辑）
      mode: 'view',
      // 语言设置
      lang: 'zh-CN'
    }
  },

  // HTML预览配置
  html: {
    // 是否启用HTML预览
    enabled: true,
    // 在新窗口打开
    openInNewWindow: true
  }
} as const;

// 翻译状态枚举
export const TRANSLATION_STATUS = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED'
} as const;

// 文件状态枚举
export const FILE_STATUS = {
  UPLOADED: 'UPLOADED',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED'
} as const;

// 用户角色枚举
export const USER_ROLES = {
  USER: 'USER',
  ADMIN: 'ADMIN'
} as const;

// 文件类型映射
export const FILE_TYPE_MAP = {
  'doc': 'Word文档',
  'docx': 'Word文档',
  'pdf': 'PDF文档',
  'txt': '文本文档'
} as const;

// 下载类型枚举
export const DOWNLOAD_TYPES = {
  TEXT: 'text',
  DOCX: 'docx',
  HTML: 'html'
} as const;
