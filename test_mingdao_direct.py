#!/usr/bin/env python3
"""
直接测试明道云API
"""

import requests
import json
from datetime import datetime

def test_mingdao_direct():
    """直接测试明道云API"""
    
    print("🔍 直接测试明道云API")
    print("=" * 60)
    
    # 测试数据
    test_user_rowid = "e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa"
    test_settings = {
        "paragraph": {
            "font_family": "微软雅黑",
            "font_size": 12,
            "bold": False,
            "italic": False,
            "underline": False,
            "text_align": "inherit",
            "color": "#000000"
        },
        "table": {
            "font_family": "宋体",
            "font_size": 8,
            "bold": True,
            "italic": False,
            "underline": False,
            "text_align": "center",
            "color": "#333333"
        },
        "header": {
            "font_family": "黑体",
            "font_size": 14,
            "bold": True,
            "italic": False,
            "underline": True,
            "text_align": "center",
            "color": "#000080"
        },
        "enable_paragraph": True,
        "enable_table": True,
        "enable_header": True
    }
    
    # 1. 测试创建记录
    print("\n1️⃣ 测试创建翻译设置记录")
    try:
        url = "https://dmit.duoningbio.com/api/v2/open/worksheet/addRow"
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        settings_json = json.dumps(test_settings, ensure_ascii=False)
        
        data = {
            "appKey": "d88c1d2329c42504",
            "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
            "worksheetId": "yhfygssz",
            "controls": [
                {
                    "controlId": "6888a7c2a849420e13f69e4a",  # 用户
                    "value": test_user_rowid
                },
                {
                    "controlId": "6888a761a849420e13f69e40",  # 翻译设置JSON
                    "value": settings_json
                },
                {
                    "controlId": "6888a7c2a849420e13f69e4c",  # 创建时间
                    "value": current_time
                },
                {
                    "controlId": "6888a7c2a849420e13f69e4d",  # 更新时间
                    "value": current_time
                }
            ]
        }
        
        print("📤 发送创建请求...")
        print(f"请求URL: {url}")
        print(f"用户rowid: {test_user_rowid}")
        print(f"设置JSON长度: {len(settings_json)} 字符")
        
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        result = response.json()
        print("响应结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            print("✅ 创建记录成功！")
            created_rowid = result.get("data")
            print(f"新记录ID: {created_rowid}")
        else:
            print(f"❌ 创建记录失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 创建记录异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 2. 测试查询记录
    print("\n2️⃣ 测试查询翻译设置记录")
    try:
        url = "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows"
        
        # 构建过滤条件
        filters = [
            {
                "controlId": "6888a7c2a849420e13f69e4a",  # 用户字段
                "dataType": 29,  # 表关联类型
                "spliceType": 1,  # AND条件
                "filterType": 2,  # 等于
                "value": test_user_rowid
            }
        ]
        
        data = {
            "appKey": "d88c1d2329c42504",
            "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
            "worksheetId": "yhfygssz",
            "pageSize": 10,
            "pageIndex": 1,
            "filters": filters
        }
        
        print("📥 发送查询请求...")
        print(f"查询用户: {test_user_rowid}")
        
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        result = response.json()
        print("查询结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get("success"):
            rows = result.get("data", {}).get("rows", [])
            print(f"✅ 查询成功，找到 {len(rows)} 条记录")
            
            if rows:
                for i, row in enumerate(rows):
                    print(f"\n记录 {i+1}:")
                    print(f"  记录ID: {row.get('rowid')}")
                    print(f"  用户: {row.get('6888a7c2a849420e13f69e4a')}")
                    print(f"  用户名: {row.get('6888a7c2a849420e13f69e49')}")
                    settings_json = row.get('6888a761a849420e13f69e40', '')
                    print(f"  设置JSON: {settings_json[:100]}..." if len(settings_json) > 100 else f"  设置JSON: {settings_json}")
                    print(f"  创建时间: {row.get('6888a7c2a849420e13f69e4c')}")
                    print(f"  更新时间: {row.get('6888a7c2a849420e13f69e4d')}")
            else:
                print("📝 没有找到相关记录")
        else:
            print(f"❌ 查询失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 查询记录异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 测试获取所有记录
    print("\n3️⃣ 测试获取所有翻译设置记录")
    try:
        url = "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows"
        
        data = {
            "appKey": "d88c1d2329c42504",
            "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
            "worksheetId": "yhfygssz",
            "pageSize": 50,
            "pageIndex": 1
        }
        
        print("📋 获取所有记录...")
        
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        result = response.json()
        
        if result.get("success"):
            rows = result.get("data", {}).get("rows", [])
            total = result.get("data", {}).get("total", 0)
            print(f"✅ 获取成功，总共 {total} 条记录，当前页 {len(rows)} 条")
            
            if rows:
                print("\n📋 记录列表:")
                for i, row in enumerate(rows):
                    user_info = row.get('6888a7c2a849420e13f69e4a', '')
                    username = row.get('6888a7c2a849420e13f69e49', '')
                    settings_json = row.get('6888a761a849420e13f69e40', '')
                    create_time = row.get('6888a7c2a849420e13f69e4c', '')
                    
                    print(f"  {i+1}. 用户名: {username}")
                    print(f"     记录ID: {row.get('rowid')}")
                    print(f"     创建时间: {create_time}")
                    print(f"     有设置: {'是' if settings_json else '否'}")
                    print()
            else:
                print("📝 表单中暂无记录")
        else:
            print(f"❌ 获取失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 获取记录异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎊 测试完成！")

if __name__ == "__main__":
    test_mingdao_direct()
