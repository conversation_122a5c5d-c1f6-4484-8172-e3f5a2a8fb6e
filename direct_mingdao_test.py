"""
直接测试明道云API
"""
import requests
import json
import asyncio
import aiohttp

# 明道云配置
MINGDAO_CONFIG = {
    "appKey": "d88c1d2329c42504",
    "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
    "baseUrl": "https://dmit.duoningbio.com/api/v2/open"
}

def test_sync_request():
    """同步请求测试"""
    print("🧪 测试同步请求...")
    
    url = f"{MINGDAO_CONFIG['baseUrl']}/worksheet/getWorksheetInfo"
    data = {
        "appKey": MINGDAO_CONFIG["appKey"],
        "sign": MINGDAO_CONFIG["sign"],
        "worksheetId": "users"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        result = response.json()
        
        print(f"✅ 同步请求成功:")
        print(f"   状态码: {response.status_code}")
        print(f"   成功: {result.get('success')}")
        if result.get('success'):
            worksheet_data = result.get('data', {})
            print(f"   工作表名称: {worksheet_data.get('name')}")
            print(f"   字段数量: {len(worksheet_data.get('controls', []))}")
        else:
            print(f"   错误: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 同步请求失败: {e}")
        return False

async def test_async_request():
    """异步请求测试"""
    print("\n🧪 测试异步请求...")
    
    url = f"{MINGDAO_CONFIG['baseUrl']}/worksheet/getWorksheetInfo"
    data = {
        "appKey": MINGDAO_CONFIG["appKey"],
        "sign": MINGDAO_CONFIG["sign"],
        "worksheetId": "translations"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=data,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                result = await response.json()
                
                print(f"✅ 异步请求成功:")
                print(f"   状态码: {response.status}")
                print(f"   成功: {result.get('success')}")
                if result.get('success'):
                    worksheet_data = result.get('data', {})
                    print(f"   工作表名称: {worksheet_data.get('name')}")
                    print(f"   字段数量: {len(worksheet_data.get('controls', []))}")
                else:
                    print(f"   错误: {result}")
                
                return True
                
    except Exception as e:
        print(f"❌ 异步请求失败: {e}")
        return False

async def test_user_query():
    """测试用户查询"""
    print("\n🧪 测试用户查询...")
    
    url = f"{MINGDAO_CONFIG['baseUrl']}/worksheet/getFilterRows"
    data = {
        "appKey": MINGDAO_CONFIG["appKey"],
        "sign": MINGDAO_CONFIG["sign"],
        "worksheetId": "6886e20ba849420e13f69b23",  # users表ID
        "pageSize": 10,
        "pageIndex": 1,
        "listType": 0,
        "controls": [],
        "filters": []
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=data,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                result = await response.json()
                
                print(f"✅ 用户查询成功:")
                print(f"   状态码: {response.status}")
                print(f"   成功: {result.get('success')}")
                if result.get('success'):
                    data_info = result.get('data', {})
                    rows = data_info.get('rows', [])
                    print(f"   用户数量: {len(rows)}")
                    print(f"   总记录数: {data_info.get('total', 0)}")
                    
                    # 显示第一个用户信息
                    if rows:
                        user = rows[0]
                        print(f"   第一个用户:")
                        print(f"     ID: {user.get('rowid')}")
                        # 用户名字段ID: 6886e20ba849420e13f69b24
                        username = user.get('6886e20ba849420e13f69b24', '未知')
                        print(f"     用户名: {username}")
                else:
                    print(f"   错误: {result}")
                
                return True
                
    except Exception as e:
        print(f"❌ 用户查询失败: {e}")
        return False

def test_pricing_calculation():
    """测试收费计算"""
    print("\n🧪 测试收费计算...")
    
    def calculate_cost(char_count):
        if char_count <= 5000:
            return {"cost": 5, "type": "按次付费", "desc": f"{char_count}字符，按次收费"}
        elif char_count >= 300000:
            return {"cost": 100, "type": "批量优惠", "desc": f"{char_count}字符，批量优惠"}
        else:
            cost = ((char_count - 1) // 5000 + 1) * 5
            return {"cost": cost, "type": "按次付费", "desc": f"{char_count}字符，分段收费"}
    
    test_cases = [1000, 5000, 8000, 15000, 300000]
    
    for chars in test_cases:
        result = calculate_cost(chars)
        print(f"   {chars:>6} 字符 → {result['cost']:>3} 元 ({result['type']})")
    
    return True

async def main():
    """主测试函数"""
    print("🚀 明道云API直接测试")
    print("=" * 50)
    
    # 测试收费计算
    test_pricing_calculation()
    
    # 测试同步请求
    sync_success = test_sync_request()
    
    # 测试异步请求
    async_success = await test_async_request()
    
    # 测试用户查询
    user_success = await test_user_query()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   收费计算: ✅")
    print(f"   同步请求: {'✅' if sync_success else '❌'}")
    print(f"   异步请求: {'✅' if async_success else '❌'}")
    print(f"   用户查询: {'✅' if user_success else '❌'}")
    
    if all([sync_success, async_success, user_success]):
        print("\n🎉 所有测试通过！明道云API连接正常")
    else:
        print("\n⚠️  部分测试失败，请检查网络连接和配置")

if __name__ == "__main__":
    asyncio.run(main())
