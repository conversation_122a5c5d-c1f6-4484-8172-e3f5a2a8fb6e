#!/bin/bash

echo "🔧 设置 systemd 服务..."

# 获取当前用户和路径
CURRENT_USER=$(whoami)
CURRENT_PATH=$(pwd)
NODE_PATH=$(which node)

echo "当前用户: $CURRENT_USER"
echo "当前路径: $CURRENT_PATH"
echo "Node.js 路径: $NODE_PATH"

# 创建服务文件
sudo tee /etc/systemd/system/translate-front.service > /dev/null <<EOF
[Unit]
Description=Translate Front Service
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$CURRENT_PATH
ExecStart=$NODE_PATH server.cjs
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=translate-front

# 安全配置
NoNewPrivileges=true
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable translate-front

echo "✅ systemd 服务设置完成！"
echo ""
echo "使用方法："
echo "🚀 启动服务: sudo systemctl start translate-front"
echo "⏹️  停止服务: sudo systemctl stop translate-front"
echo "🔄 重启服务: sudo systemctl restart translate-front"
echo "📊 查看状态: sudo systemctl status translate-front"
echo "📝 查看日志: sudo journalctl -u translate-front -f"
