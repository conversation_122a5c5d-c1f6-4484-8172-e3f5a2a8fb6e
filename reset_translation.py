"""
重置卡住的翻译任务
"""
import sqlite3
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.azure_translator import AzureTranslatorService

def reset_stuck_translation():
    """重置卡住的翻译任务"""
    print("🔧 重置卡住的翻译任务")
    print("=" * 50)
    
    # 连接数据库
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    try:
        # 查找卡住的翻译任务
        cursor.execute("""
            SELECT id, status, progress, original_text 
            FROM translation_histories 
            WHERE status = 'IN_PROGRESS' AND progress < 1.0
        """)
        
        stuck_tasks = cursor.fetchall()
        
        if not stuck_tasks:
            print("✅ 没有发现卡住的翻译任务")
            return
        
        print(f"🔍 发现 {len(stuck_tasks)} 个卡住的翻译任务:")
        
        for task_id, status, progress, original_text in stuck_tasks:
            print(f"  - 任务 {task_id}: {status}, 进度 {progress}")
            print(f"    原文预览: {original_text[:50]}...")
            
            # 重置为 PENDING 状态
            cursor.execute("""
                UPDATE translation_histories 
                SET status = 'PENDING', progress = 0.0, error_message = NULL
                WHERE id = ?
            """, (task_id,))
            
            print(f"  ✅ 任务 {task_id} 已重置为 PENDING 状态")
        
        # 提交更改
        conn.commit()
        print(f"\n🎉 成功重置 {len(stuck_tasks)} 个翻译任务")
        
    except Exception as e:
        print(f"❌ 重置失败: {e}")
        conn.rollback()
    finally:
        conn.close()

async def process_pending_translations():
    """处理待处理的翻译任务"""
    print("\n🚀 开始处理待处理的翻译任务")
    print("=" * 50)
    
    # 连接数据库
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    try:
        # 查找待处理的翻译任务
        cursor.execute("""
            SELECT th.id, th.original_text, th.source_language, th.target_language,
                   uf.original_filename
            FROM translation_histories th
            JOIN uploaded_files uf ON th.file_id = uf.id
            WHERE th.status = 'PENDING'
            ORDER BY th.created_at
        """)
        
        pending_tasks = cursor.fetchall()
        
        if not pending_tasks:
            print("✅ 没有待处理的翻译任务")
            return
        
        print(f"🔍 发现 {len(pending_tasks)} 个待处理的翻译任务")
        
        # 创建翻译器
        translator = AzureTranslatorService()
        
        for task_id, original_text, source_lang, target_lang, filename in pending_tasks:
            print(f"\n📝 处理任务 {task_id}: {filename}")
            print(f"   语言: {source_lang} → {target_lang}")
            
            try:
                # 更新状态为进行中
                cursor.execute("""
                    UPDATE translation_histories 
                    SET status = 'IN_PROGRESS', progress = 0.1
                    WHERE id = ?
                """, (task_id,))
                conn.commit()
                
                # 分割文本为段落
                paragraphs = [p.strip() for p in original_text.split('\n') if p.strip()]
                print(f"   段落数量: {len(paragraphs)}")
                
                # 翻译文本
                print("   🔄 开始翻译...")
                results = await translator.translate_text(
                    texts=paragraphs[:5],  # 只翻译前5段作为测试
                    target_language=target_lang,
                    source_language=source_lang
                )
                
                # 提取翻译结果
                translated_paragraphs = []
                for result in results:
                    if 'translations' in result and len(result['translations']) > 0:
                        translated_paragraphs.append(result['translations'][0]['text'])
                    else:
                        translated_paragraphs.append('[Translation failed]')
                
                # 组合翻译结果
                translated_text = '\n'.join(translated_paragraphs)
                
                # 更新翻译结果
                cursor.execute("""
                    UPDATE translation_histories 
                    SET status = 'COMPLETED', 
                        progress = 1.0,
                        translated_text = ?,
                        translated_characters = ?,
                        completed_at = datetime('now')
                    WHERE id = ?
                """, (translated_text, len(translated_text), task_id))
                
                conn.commit()
                
                print(f"   ✅ 任务 {task_id} 翻译完成")
                print(f"   📊 翻译字符数: {len(translated_text)}")
                
            except Exception as e:
                print(f"   ❌ 任务 {task_id} 翻译失败: {e}")
                
                # 更新为失败状态
                cursor.execute("""
                    UPDATE translation_histories 
                    SET status = 'FAILED', error_message = ?
                    WHERE id = ?
                """, (str(e), task_id))
                conn.commit()
        
        print(f"\n🎉 翻译处理完成")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

async def main():
    """主函数"""
    # 1. 重置卡住的任务
    reset_stuck_translation()
    
    # 2. 处理待处理的任务
    await process_pending_translations()
    
    # 3. 显示最终状态
    print("\n📊 最终状态:")
    print("=" * 50)
    
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT status, COUNT(*) as count
        FROM translation_histories
        GROUP BY status
    """)
    
    status_counts = cursor.fetchall()
    for status, count in status_counts:
        print(f"   {status}: {count} 个任务")
    
    conn.close()

if __name__ == "__main__":
    asyncio.run(main())
