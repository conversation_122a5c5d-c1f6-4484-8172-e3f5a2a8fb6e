"""
API 测试脚本
"""
import asyncio
import httpx
import json

BASE_URL = "http://localhost:8000"

async def test_api():
    """测试API基本功能"""
    async with httpx.AsyncClient() as client:
        print("🚀 测试 FastAPI 翻译服务")
        print("=" * 50)
        
        # 1. 测试健康检查
        print("1. 测试健康检查...")
        try:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("✅ 健康检查通过")
                print(f"   响应: {response.json()}")
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 健康检查错误: {e}")
        
        print()
        
        # 2. 测试根路径
        print("2. 测试根路径...")
        try:
            response = await client.get(f"{BASE_URL}/")
            if response.status_code == 200:
                print("✅ 根路径访问成功")
                print(f"   响应: {response.json()}")
            else:
                print(f"❌ 根路径访问失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 根路径访问错误: {e}")
        
        print()
        
        # 3. 测试用户注册
        print("3. 测试用户注册...")
        try:
            register_data = {
                "username": "testuser",
                "email": "<EMAIL>",
                "password": "testpass123",
                "full_name": "Test User"
            }
            response = await client.post(
                f"{BASE_URL}/api/v1/auth/register",
                json=register_data
            )
            if response.status_code == 201:
                print("✅ 用户注册成功")
                user_data = response.json()
                print(f"   用户ID: {user_data['id']}")
                print(f"   用户名: {user_data['username']}")
            else:
                print(f"❌ 用户注册失败: {response.status_code}")
                print(f"   错误: {response.text}")
        except Exception as e:
            print(f"❌ 用户注册错误: {e}")
        
        print()
        
        # 4. 测试用户登录
        print("4. 测试用户登录...")
        try:
            login_data = {
                "username": "testuser",
                "password": "testpass123"
            }
            response = await client.post(
                f"{BASE_URL}/api/v1/auth/login",
                json=login_data
            )
            if response.status_code == 200:
                print("✅ 用户登录成功")
                token_data = response.json()
                access_token = token_data["access_token"]
                print(f"   获得访问令牌: {access_token[:20]}...")
                
                # 5. 测试获取用户资料
                print("\n5. 测试获取用户资料...")
                headers = {"Authorization": f"Bearer {access_token}"}
                profile_response = await client.get(
                    f"{BASE_URL}/api/v1/auth/profile",
                    headers=headers
                )
                if profile_response.status_code == 200:
                    print("✅ 获取用户资料成功")
                    profile = profile_response.json()
                    print(f"   用户名: {profile['username']}")
                    print(f"   邮箱: {profile['email']}")
                    print(f"   翻译配额: {profile['translation_quota']}")
                    print(f"   已使用配额: {profile['used_quota']}")
                else:
                    print(f"❌ 获取用户资料失败: {profile_response.status_code}")
                
                # 6. 测试获取支持的语言
                print("\n6. 测试获取支持的语言...")
                try:
                    lang_response = await client.get(
                        f"{BASE_URL}/api/v1/translate/languages",
                        headers=headers
                    )
                    if lang_response.status_code == 200:
                        print("✅ 获取支持语言成功")
                        languages = lang_response.json()
                        print(f"   支持的语言数量: {len(languages.get('languages', []))}")
                        # 显示前5种语言
                        for lang in languages.get('languages', [])[:5]:
                            print(f"   - {lang['code']}: {lang['name']}")
                    else:
                        print(f"❌ 获取支持语言失败: {lang_response.status_code}")
                except Exception as e:
                    print(f"⚠️  获取支持语言跳过 (需要Azure配置): {e}")
                
            else:
                print(f"❌ 用户登录失败: {response.status_code}")
                print(f"   错误: {response.text}")
        except Exception as e:
            print(f"❌ 用户登录错误: {e}")
        
        print()
        print("🎉 API 测试完成!")
        print("=" * 50)
        print("📝 注意事项:")
        print("   - 如果要测试翻译功能，请先配置 Azure 翻译服务密钥")
        print("   - 如果要测试文件上传，请确保 uploads 目录存在")
        print("   - 生产环境请修改默认密码和密钥")

if __name__ == "__main__":
    asyncio.run(test_api())
