<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试用户ID</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-item {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .debug-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .debug-value {
            font-family: monospace;
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            word-break: break-all;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #218838;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 调试用户ID和API连接</h1>
        
        <div class="debug-item">
            <div class="debug-label">当前用户信息 (localStorage.currentUser):</div>
            <div class="debug-value" id="currentUser">检查中...</div>
        </div>
        
        <div class="debug-item">
            <div class="debug-label">用户ID:</div>
            <div class="debug-value" id="userId">检查中...</div>
        </div>
        
        <div class="debug-item">
            <div class="debug-label">后端API地址:</div>
            <div class="debug-value" id="apiUrl">http://localhost:8000/api/translation-settings/{user_id}</div>
        </div>
        
        <div class="debug-item">
            <div class="debug-label">测试操作:</div>
            <button class="test-button" onclick="testGetSettings()">📥 测试获取设置</button>
            <button class="test-button" onclick="testSaveSettings()">💾 测试保存设置</button>
            <button class="test-button" onclick="testBackendConnection()">🔗 测试后端连接</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        // 检查用户信息
        function checkUserInfo() {
            try {
                // 检查多个可能的localStorage键
                const currentUserStr = localStorage.getItem('current_user') || localStorage.getItem('currentUser');
                const authToken = localStorage.getItem('auth_token');

                let currentUser = null;
                if (currentUserStr) {
                    currentUser = JSON.parse(currentUserStr);
                }

                document.getElementById('currentUser').textContent = currentUserStr || '未找到用户信息';
                document.getElementById('userId').textContent = currentUser?.id || '未找到用户ID';

                // 显示所有localStorage信息
                const allLocalStorage = {};
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    allLocalStorage[key] = localStorage.getItem(key);
                }
                console.log('所有localStorage数据:', allLocalStorage);

                if (currentUser?.id) {
                    document.getElementById('apiUrl').textContent =
                        `http://localhost:8000/api/translation-settings/${currentUser.id}`;
                }

                return currentUser;
            } catch (error) {
                document.getElementById('currentUser').textContent = `解析错误: ${error.message}`;
                return null;
            }
        }
        
        // 显示结果
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = message;
        }
        
        // 测试后端连接
        async function testBackendConnection() {
            try {
                showResult('🔍 测试后端连接中...', true);
                
                const response = await fetch('http://localhost:8000/docs');
                if (response.ok) {
                    showResult('✅ 后端服务连接正常！', true);
                } else {
                    showResult(`❌ 后端服务响应异常: ${response.status}`, false);
                }
            } catch (error) {
                showResult(`❌ 无法连接到后端服务: ${error.message}<br>请确保后端服务已启动`, false);
            }
        }
        
        // 测试获取设置
        async function testGetSettings() {
            const currentUser = checkUserInfo();
            if (!currentUser?.id) {
                showResult('❌ 用户未登录或用户ID不存在', false);
                return;
            }
            
            try {
                showResult('📥 测试获取翻译设置中...', true);
                
                const response = await fetch(`http://localhost:8000/api/translation-settings/${currentUser.id}`);
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`✅ 获取设置成功！<br><pre>${JSON.stringify(result, null, 2)}</pre>`, true);
                } else {
                    showResult(`❌ 获取设置失败: ${response.status}<br><pre>${JSON.stringify(result, null, 2)}</pre>`, false);
                }
            } catch (error) {
                showResult(`❌ 获取设置异常: ${error.message}`, false);
            }
        }
        
        // 测试保存设置
        async function testSaveSettings() {
            const currentUser = checkUserInfo();
            if (!currentUser?.id) {
                showResult('❌ 用户未登录或用户ID不存在', false);
                return;
            }
            
            const testSettings = {
                paragraph: {
                    font_family: "测试字体",
                    font_size: 12,
                    bold: false,
                    italic: false,
                    underline: false,
                    text_align: "inherit",
                    color: "#000000"
                },
                table: {
                    font_family: "Arial",
                    font_size: 8,
                    bold: false,
                    italic: false,
                    underline: false,
                    text_align: "inherit",
                    color: "#000000"
                },
                header: {
                    font_family: "Arial",
                    font_size: 14,
                    bold: true,
                    italic: false,
                    underline: false,
                    text_align: "inherit",
                    color: "#000000"
                },
                enable_paragraph: true,
                enable_table: true,
                enable_header: true
            };
            
            try {
                showResult('💾 测试保存翻译设置中...', true);
                
                const response = await fetch(`http://localhost:8000/api/translation-settings/${currentUser.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testSettings)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`✅ 保存设置成功！<br><pre>${JSON.stringify(result, null, 2)}</pre>`, true);
                } else {
                    showResult(`❌ 保存设置失败: ${response.status}<br><pre>${JSON.stringify(result, null, 2)}</pre>`, false);
                }
            } catch (error) {
                showResult(`❌ 保存设置异常: ${error.message}`, false);
            }
        }
        
        // 页面加载时检查用户信息
        window.onload = function() {
            checkUserInfo();
        };
    </script>
</body>
</html>
