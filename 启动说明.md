# 🚀 翻译系统启动说明

## 📋 系统概览

您的翻译系统已经完成迁移，包含以下组件：
- **后端**: FastAPI + SQLAlchemy (Python)
- **前端**: React + TypeScript + Vite
- **数据库**: SQLite
- **认证**: JWT Token

## 🔧 启动步骤

### 方法一：使用启动脚本（推荐）

1. **启动后端服务**
   - 双击运行 `start_backend.bat`
   - 等待看到 "Application startup complete" 消息
   - 后端将运行在: http://localhost:8000

2. **启动前端服务**
   - 双击运行 `start_frontend.bat`
   - 等待看到 "Local: http://localhost:5173" 消息
   - 前端将运行在: http://localhost:5173

### 方法二：手动启动

#### 启动后端
```bash
# 1. 激活虚拟环境
.venv\Scripts\activate

# 2. 安装缺失依赖
pip install email-validator

# 3. 启动后端
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 启动前端
```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖
npm install

# 3. 启动前端
npm run dev
```

## 🌐 访问地址

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **数据库管理**: SQLite文件位于项目根目录

## 🔑 首次使用

1. **注册账号**
   - 打开前端应用
   - 点击"登录"按钮
   - 选择"注册"选项卡
   - 填写用户信息并注册

2. **登录系统**
   - 使用注册的账号登录

3. **上传文档**
   - 点击"文件上传"区域
   - 选择Word文档(.doc/.docx)
   - 等待上传完成

4. **创建翻译**
   - 点击"创建翻译"按钮
   - 选择已上传的文件
   - 选择源语言和目标语言
   - 点击"创建翻译任务"

5. **查看结果**
   - 等待翻译完成
   - 点击"HTML预览"查看结果
   - 点击"下载"获取翻译文档

## 🎯 主要功能

### ✅ 已实现功能
- 用户注册/登录/登出
- 文件上传（Word文档）
- 翻译任务创建
- 翻译进度实时监控
- HTML预览翻译结果
- 下载翻译文本/Word文档
- 批量下载功能
- 翻译历史管理

### 🔧 技术特性
- JWT认证机制
- 实时进度更新
- 响应式界面设计
- 错误处理和用户反馈
- OnlyOffice预览支持（已配置）

## 🐛 故障排除

### 后端启动问题
- 确保Python虚拟环境已激活
- 检查是否安装了所有依赖：`pip install -r requirements.txt`
- 检查端口8000是否被占用

### 前端启动问题
- 确保Node.js已安装
- 删除node_modules文件夹，重新运行`npm install`
- 检查端口5173是否被占用

### 数据库问题
- 数据库文件会自动创建
- 如需重置，删除`test.db`文件即可

## 📞 支持信息

- **项目目录**: `D:\mywork\Translate`
- **后端代码**: `app/` 目录
- **前端代码**: `frontend/` 目录
- **配置文件**: `frontend/src/config/api.config.ts`

## 🎉 迁移完成

恭喜！您的翻译系统已成功从明道云迁移到自建后端，现在拥有：
- 更快的响应速度
- 更好的功能扩展性
- 完全的数据控制权
- 现代化的技术栈

享受您的新翻译系统吧！🎊
