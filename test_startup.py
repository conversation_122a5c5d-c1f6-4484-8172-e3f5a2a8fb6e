"""
测试启动脚本和服务可用性
"""
import asyncio
import httpx
import subprocess
import time
import os
import signal

def test_startup_script():
    """测试启动脚本是否正常工作"""
    print("🔍 测试启动脚本")
    print("=" * 50)
    
    # 检查启动脚本是否存在
    if not os.path.exists('start.py'):
        print("❌ start.py 文件不存在")
        return False
    
    print("✅ start.py 文件存在")
    
    # 检查依赖文件
    required_files = [
        'requirements.txt',
        'app/main.py',
        'frontend/index.html',
        '.env.example'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            return False
    
    return True

async def test_service_endpoints():
    """测试服务端点是否可访问"""
    print("\n🌐 测试服务端点")
    print("=" * 50)
    
    endpoints = [
        ("后端健康检查", "http://localhost:8000/health"),
        ("API 文档", "http://localhost:8000/docs"),
        ("前端界面", "http://localhost:3000"),
    ]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        for name, url in endpoints:
            try:
                response = await client.get(url)
                if response.status_code == 200:
                    print(f"✅ {name}: {url} - 状态码 {response.status_code}")
                else:
                    print(f"⚠️  {name}: {url} - 状态码 {response.status_code}")
            except Exception as e:
                print(f"❌ {name}: {url} - 连接失败: {e}")

def test_environment_setup():
    """测试环境配置"""
    print("\n⚙️  测试环境配置")
    print("=" * 50)
    
    # 检查 Python 版本
    import sys
    python_version = sys.version_info
    print(f"🐍 Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version >= (3, 8):
        print("✅ Python 版本符合要求 (>= 3.8)")
    else:
        print("❌ Python 版本过低，需要 >= 3.8")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
    else:
        print("⚠️  未检测到虚拟环境，建议使用虚拟环境")
    
    # 检查关键依赖
    try:
        import fastapi
        print(f"✅ FastAPI 版本: {fastapi.__version__}")
    except ImportError:
        print("❌ FastAPI 未安装")
    
    try:
        import uvicorn
        print(f"✅ Uvicorn 已安装")
    except ImportError:
        print("❌ Uvicorn 未安装")
    
    try:
        import docx
        print(f"✅ python-docx 已安装")
    except ImportError:
        print("❌ python-docx 未安装")
    
    # 检查环境变量文件
    if os.path.exists('.env'):
        print("✅ .env 文件存在")
        
        # 读取关键配置
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
            
        if 'AZURE_TRANSLATOR_KEY' in env_content:
            print("✅ Azure 翻译密钥已配置")
        else:
            print("⚠️  Azure 翻译密钥未配置")
            
        if 'SECRET_KEY' in env_content:
            print("✅ JWT 密钥已配置")
        else:
            print("⚠️  JWT 密钥未配置")
    else:
        print("⚠️  .env 文件不存在，请从 .env.example 复制")

def print_startup_instructions():
    """打印启动说明"""
    print("\n📋 启动说明")
    print("=" * 50)
    print("""
🚀 快速启动：
   python start.py

🔧 分别启动：
   后端: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   前端: cd frontend && python -m http.server 3000

🌐 访问地址：
   Web 界面: http://localhost:3000
   API 文档: http://localhost:8000/docs
   健康检查: http://localhost:8000/health

👤 测试账号：
   用户名: testuser
   密码: testpass123

🔍 故障排除：
   1. 检查端口是否被占用: netstat -an | findstr :8000
   2. 检查依赖是否安装: pip install -r requirements.txt
   3. 检查环境变量: 复制 .env.example 到 .env 并配置
   4. 查看详细日志: 启动时观察控制台输出
""")

async def main():
    """主测试函数"""
    print("🧪 翻译服务启动测试")
    print("=" * 60)
    
    # 测试启动脚本
    if not test_startup_script():
        print("\n❌ 启动脚本测试失败")
        return
    
    # 测试环境配置
    test_environment_setup()
    
    # 测试服务端点（如果服务正在运行）
    print("\n⏳ 等待 3 秒后测试服务端点...")
    await asyncio.sleep(3)
    await test_service_endpoints()
    
    # 打印启动说明
    print_startup_instructions()
    
    print("\n🎉 测试完成！")
    print("如果所有检查都通过，您可以使用 'python start.py' 启动服务")

if __name__ == "__main__":
    asyncio.run(main())
