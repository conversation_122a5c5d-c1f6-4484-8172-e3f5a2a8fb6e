[Unit]
Description=Translate Front Service
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/your/TranslateFront
ExecStart=/usr/bin/node server.cjs
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=translate-front

# 安全配置
NoNewPrivileges=true
PrivateTmp=true

[Install]
WantedBy=multi-user.target
