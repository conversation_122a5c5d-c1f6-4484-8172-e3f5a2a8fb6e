import React, { useState } from 'react';
import { X, User, Lock, LogIn, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { authService } from '../services/auth';
import { LoginRequest } from '../types/document';
import './LoginModal.css';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoginSuccess: (user: any) => void;
  onSwitchToRegister?: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  onLoginSuccess,
  onSwitchToRegister
}) => {
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<LoginRequest>>({});
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // 处理输入变化
  const handleInputChange = (field: keyof LoginRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // 清除结果消息
    if (result) {
      setResult(null);
    }
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Partial<LoginRequest> = {};

    if (!formData.username.trim()) {
      newErrors.username = '请输入用户名';
    }

    if (!formData.password) {
      newErrors.password = '请输入密码';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理登录提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await authService.login(formData);

      if (response.success && response.data) {
        setResult({
          success: true,
          message: '登录成功！'
        });

        // 通知父组件登录成功
        onLoginSuccess(response.data);

        // 延迟关闭模态框
        setTimeout(() => {
          onClose();
          resetForm();
        }, 1500);
      } else {
        setResult({
          success: false,
          message: response.message || '登录失败'
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: '登录过程中发生错误，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({ username: '', password: '' });
    setErrors({});
    setResult(null);
    setShowPassword(false);
  };

  // 处理模态框关闭
  const handleClose = () => {
    if (!isLoading) {
      onClose();
      resetForm();
    }
  };

  // 处理背景点击
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="login-modal-overlay" onClick={handleBackdropClick}>
      <div className="login-modal">
        {/* 模态框头部 */}
        <div className="login-modal-header">
          <h2>
            <LogIn size={24} />
            用户登录
          </h2>
          <button 
            className="close-btn" 
            onClick={handleClose}
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* 结果消息 */}
        {result && (
          <div className={`result-message ${result.success ? 'success' : 'error'}`}>
            {result.success ? (
              <CheckCircle size={20} />
            ) : (
              <AlertCircle size={20} />
            )}
            <span>{result.message}</span>
          </div>
        )}

        {/* 登录表单 */}
        <form onSubmit={handleSubmit} className="login-form">
          {/* 用户名 */}
          <div className="form-group">
            <label htmlFor="username">
              <User size={16} />
              用户名
            </label>
            <input
              id="username"
              type="text"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              placeholder="请输入用户名"
              className={errors.username ? 'error' : ''}
              disabled={isLoading}
              autoComplete="username"
            />
            {errors.username && (
              <span className="error-message">{errors.username}</span>
            )}
          </div>

          {/* 密码 */}
          <div className="form-group">
            <label htmlFor="password">
              <Lock size={16} />
              密码
            </label>
            <div className="password-input">
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                placeholder="请输入密码"
                className={errors.password ? 'error' : ''}
                disabled={isLoading}
                autoComplete="current-password"
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {errors.password && (
              <span className="error-message">{errors.password}</span>
            )}
          </div>

          {/* 提交按钮 */}
          <div className="form-actions">
            <button
              type="submit"
              className="login-btn"
              disabled={isLoading || result?.success}
            >
              {isLoading ? (
                <>
                  <div className="loading-spinner"></div>
                  登录中...
                </>
              ) : result?.success ? (
                <>
                  <CheckCircle size={16} />
                  登录成功
                </>
              ) : (
                <>
                  <LogIn size={16} />
                  登录
                </>
              )}
            </button>

            <button
              type="button"
              className="cancel-btn"
              onClick={handleClose}
              disabled={isLoading}
            >
              取消
            </button>
          </div>
        </form>

        {/* 注册链接 */}
        {onSwitchToRegister && (
          <div className="register-link">
            <p>
              还没有账号？
              <button
                type="button"
                className="link-btn"
                onClick={onSwitchToRegister}
                disabled={isLoading}
              >
                立即注册
              </button>
            </p>
          </div>
        )}

        {/* 登录说明 */}
        <div className="login-info">
          <h4>登录后可享受</h4>
          <ul>
            <li>🔍 查看详细的翻译历史</li>
            <li>📊 实时配额和余额管理</li>
            <li>💳 灵活的充值方式选择</li>
            <li>📱 跨设备同步翻译记录</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default LoginModal;
