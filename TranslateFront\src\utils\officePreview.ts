import { DocumentFile } from '../types/document';

/**
 * Office Online Viewer 预览配置
 */
export const OFFICE_PREVIEW_CONFIG = {
  baseUrl: 'https://view.officeapps.live.com/op/view.aspx',
  supportedExtensions: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
};

/**
 * 检查文件是否支持 Office Online Viewer 预览
 * @param fileName 文件名
 * @returns 是否支持预览
 */
export const isSupportedByOfficeViewer = (fileName: string): boolean => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  return extension ? OFFICE_PREVIEW_CONFIG.supportedExtensions.includes(extension) : false;
};

/**
 * 生成 Office Online Viewer 预览链接
 * @param fileUrl 文件的完整 URL
 * @returns 预览链接
 */
export const generateOfficePreviewUrl = (fileUrl: string): string => {
  const encodedUrl = encodeURIComponent(fileUrl);
  return `${OFFICE_PREVIEW_CONFIG.baseUrl}?src=${encodedUrl}`;
};

/**
 * 获取文件的预览 URL
 * @param file 文档文件对象
 * @returns 预览 URL 或 null
 */
export const getFilePreviewUrl = (file: DocumentFile): string | null => {
  if (!file.allow_view || !file.DownloadUrl) {
    return null;
  }

  if (!isSupportedByOfficeViewer(file.original_file_name)) {
    return null;
  }

  // 使用 DownloadUrl 字段进行预览
  return generateOfficePreviewUrl(file.DownloadUrl);
};

/**
 * 检查文档URL是否可访问
 * @param fileUrl 文件URL
 * @returns Promise<boolean>
 */
export const checkFileAccessibility = async (fileUrl: string): Promise<boolean> => {
  try {
    await fetch(fileUrl, {
      method: 'HEAD',
      mode: 'no-cors' // 避免CORS问题
    });
    return true; // 如果没有抛出错误，说明URL可访问
  } catch (error) {
    console.error('文件访问检查失败:', error);
    return false;
  }
};

/**
 * 在新窗口打开 Office Online Viewer 预览（保留兼容性）
 * @param file 文档文件对象
 * @returns 是否成功打开预览
 */
export const openOfficePreview = async (file: DocumentFile): Promise<boolean> => {
  const previewUrl = getFilePreviewUrl(file);

  if (!previewUrl) {
    console.warn(`文件 ${file.original_file_name} 不支持预览`);
    alert('该文件格式不支持在线预览');
    return false;
  }

  try {
    console.log('预览文件:', file.original_file_name);
    console.log('文件URL:', file.original_file_full_path);
    console.log('预览URL:', previewUrl);

    // 检查文件是否可访问
    const isAccessible = await checkFileAccessibility(file.original_file_full_path);
    if (!isAccessible) {
      console.warn('文件可能无法访问:', file.original_file_full_path);
      alert('文件可能无法访问，但仍尝试打开预览...');
    }

    window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
    return true;
  } catch (error) {
    console.error('打开预览失败:', error);
    alert('打开预览失败，请稍后重试');
    return false;
  }
};