// API 配置和接口调用服务
import { API_CONFIG, CONTROL_IDS, MINGDAO_AUTH_CONFIG } from '../config/api.config';
import { authService } from './auth';

// 过滤器接口
export interface ApiFilter {
  controlId: string;
  dataType: number;
  spliceType: number;
  filterType: number;
  value: string;
}

// 获取文档列表的请求参数
export interface GetDocumentsRequest {
  viewId?: string;
  pageSize?: number;
  pageIndex?: number;
  listType?: number;
  controls?: any[];
  filters?: ApiFilter[];
  sortId?: string;
  isAsc?: boolean;
  notGetTotal?: boolean;
  useControlId?: boolean;
  getSystemControl?: boolean;
}

// 文件上传控制项
export interface FileControl {
  controlId: string;
  value?: string;
  editType?: number; // 0=覆盖，1=新增
  valueType?: number; // 1=外部文件链接，2=文件流字节编码
  controlFiles?: {
    baseFile: string; // base64字符串
    fileName: string; // 文件名称，带后缀
  }[];
}

// 新建行记录的请求参数
export interface AddRowRequest {
  triggerWorkflow?: boolean;
  controls: (FileControl | {
    controlId: string;
    value: string;
  })[];
}

// API 响应基础接口
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

/**
 * 获取文档总行数
 */
export const getDocumentsTotalNum = async (): Promise<number> => {
  // 获取当前登录用户
  const currentUser = authService.getCurrentUser();
  if (!currentUser || !authService.isAuthenticated()) {
    return 0; // 未登录返回0
  }

  // 构建用户过滤条件
  const filters = [
    {
      controlId: MINGDAO_AUTH_CONFIG.translationFields.user,
      dataType: 29, // 人员字段类型
      spliceType: 2,
      filterType: 11,
      value: currentUser.id // 当前用户的rowid
    }
  ];

  const requestData = {
    appKey: MINGDAO_AUTH_CONFIG.appKey,
    sign: MINGDAO_AUTH_CONFIG.sign,
    worksheetId: MINGDAO_AUTH_CONFIG.worksheets.translations,
    pageSize: 50,
    pageIndex: 1,
    listType: 0,
    controls: [],
    filters: filters // 添加用户过滤条件
  };

  try {
    console.log('获取总行数，发送请求到:', `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.getTotalNum}`);
    console.log('请求数据:', requestData);

    const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/getFilterRowsTotalNum`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('总行数响应:', result);

    if (result.success && result.data) {
      // 数据可能是字符串，需要转换为数字
      const totalNum = typeof result.data === 'string' ? parseInt(result.data, 10) : result.data;
      console.log('解析后的总行数:', totalNum);
      return totalNum;
    } else {
      console.error('获取总行数失败:', result.message || result.error_code);
      return 0;
    }
  } catch (error) {
    console.error('获取总行数错误:', error);
    return 0;
  }
};

/**
 * 获取文档列表
 */
export const getDocuments = async (params: Partial<GetDocumentsRequest> = {}): Promise<ApiResponse> => {
  // 获取当前登录用户
  const currentUser = authService.getCurrentUser();
  if (!currentUser || !authService.isAuthenticated()) {
    throw new Error('用户未登录，无法获取文档列表');
  }

  // 构建用户过滤条件
  const filters = [
    {
      controlId: MINGDAO_AUTH_CONFIG.translationFields.user,
      dataType: 29, // 人员字段类型
      spliceType: 2,
      filterType: 11,
      value: currentUser.id // 当前用户的rowid
    }
  ];

  // 使用固定的简化参数格式
  const requestData = {
    appKey: MINGDAO_AUTH_CONFIG.appKey,
    sign: MINGDAO_AUTH_CONFIG.sign,
    worksheetId: MINGDAO_AUTH_CONFIG.worksheets.translations,
    viewId: params.viewId || '',
    pageSize: params.pageSize || API_CONFIG.pagination.defaultPageSize, // 默认200条
    pageIndex: params.pageIndex || 1,
    listType: params.listType || 0,
    filters: filters // 添加用户过滤条件
  };

  try {
    console.log('发送请求数据:', requestData);

    console.log('发送请求到:', `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.getRows}`);
    console.log('请求数据:', requestData);

    const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/getFilterRows`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    console.log('响应状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API 错误响应:', errorText);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('API 响应数据:', result);
    return result;
  } catch (error) {
    console.error('获取文档列表失败:', error);

    // 如果是网络错误或 CORS 错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('网络连接失败或存在跨域问题，请检查网络连接或联系管理员');
    }

    throw new Error(`获取文档列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

/**
 * 新建行记录（上传文档）
 */
export const addDocumentRow = async (params: AddRowRequest): Promise<ApiResponse> => {
  const requestData = {
    appKey: MINGDAO_AUTH_CONFIG.appKey,
    sign: MINGDAO_AUTH_CONFIG.sign,
    worksheetId: MINGDAO_AUTH_CONFIG.worksheets.translations,
    triggerWorkflow: params.triggerWorkflow !== false, // 默认为 true
    controls: params.controls
  };

  try {
    const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/addRow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('新建文档记录失败:', error);
    throw new Error('新建文档记录失败，请检查网络连接');
  }
};

/**
 * 将文件转换为 base64
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const result = reader.result as string;
      // 移除 data:xxx;base64, 前缀，只保留 base64 字符串
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = error => reject(error);
  });
};

/**
 * 上传单个文件
 */
export const uploadFile = async (
  file: File,
  controlId: string = MINGDAO_AUTH_CONFIG.translationFields.original_file, // 默认上传到原文件字段
  ownerId?: string
): Promise<ApiResponse> => {
  try {
    // 将文件转换为 base64
    const base64Content = await fileToBase64(file);

    // 构建控制项数组
    const controls: any[] = [
      {
        controlId: controlId,
        valueType: 2, // 使用文件流字节编码
        controlFiles: [
          {
            baseFile: base64Content,
            fileName: file.name
          }
        ]
      }
    ];

    // 如果提供了 ownerId，添加到控制项中
    if (ownerId) {
      controls.push({
        controlId: CONTROL_IDS.OWNER,
        value: ownerId
      });
    }

    return await addDocumentRow({
      triggerWorkflow: API_CONFIG.upload.triggerWorkflow,
      controls
    });
  } catch (error) {
    console.error('上传文件失败:', error);
    throw error;
  }
};

/**
 * 批量上传文件
 */
// 创建翻译任务
export const createTranslationTask = async (
  fileId: string,
  sourceLanguage: string = 'zh',
  targetLanguage: string = 'en'
): Promise<ApiResponse> => {
  try {
    const response = await fetch('/api/translate/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({
        file_id: fileId,
        source_language: sourceLanguage,
        target_language: targetLanguage
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result,
      message: '翻译任务创建成功'
    };
  } catch (error) {
    console.error('创建翻译任务失败:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : '创建翻译任务失败'
    };
  }
};

// 仅上传文件到明道云（不自动开始翻译）
export const uploadFileOnly = async (
  file: File,
  sourceLanguage: string = 'zh',
  targetLanguage: string = 'en',
  onProgress?: (progress: number) => void
): Promise<ApiResponse> => {
  try {
    if (onProgress) onProgress(10);

    // 第一步：上传文件到明道云
    console.log('开始上传文件到明道云:', file.name);
    const uploadResult = await uploadFileToMingdao(file, onProgress);

    if (!uploadResult.success) {
      console.error('文件上传失败:', uploadResult.message);
      return uploadResult;
    }

    console.log('文件上传成功');
    if (onProgress) onProgress(60);

    // 检查是否有rowId
    const rowId = uploadResult.data?.rowId;
    if (!rowId) {
      console.warn('没有获取到rowId，但文件已上传成功');
      if (onProgress) onProgress(100);
      return {
        success: true,
        data: uploadResult.data,
        message: '文件上传成功，可在列表中手动开始翻译'
      };
    }

    // 第二步：设置基本的翻译记录信息（不开始翻译）
    console.log('设置翻译记录基本信息, rowId:', rowId);
    try {
      const basicInfo = await setBasicTranslationInfo(
        rowId,
        file.name,
        sourceLanguage,
        targetLanguage
      );

      if (onProgress) onProgress(100);
      return {
        success: true,
        data: { ...uploadResult.data, ...basicInfo.data },
        message: '文件上传成功，可在列表中手动开始翻译'
      };
    } catch (error) {
      console.warn('设置翻译记录基本信息失败，但文件已上传:', error);
      if (onProgress) onProgress(100);
      return {
        success: true,
        data: uploadResult.data,
        message: '文件上传成功，可在列表中手动开始翻译'
      };
    }
  } catch (error) {
    console.error('上传文件并创建翻译失败:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : '上传文件并创建翻译失败'
    };
  }
};

// 上传文件到明道云
export const uploadFileToMingdao = async (
  file: File,
  onProgress?: (progress: number) => void
): Promise<ApiResponse> => {
  try {
    // 将文件转换为base64
    const base64Content = await fileToBase64(file);

    // 构建明道云API请求
    const controls = [
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.original_file,
        valueType: 2, // 文件流字节编码
        controlFiles: [
          {
            baseFile: base64Content,
            fileName: file.name
          }
        ]
      }
    ];

    // 如果用户已登录，添加用户关联
    const currentUser = authService.getCurrentUser();
    if (currentUser && authService.isAuthenticated()) {
      controls.push({
        controlId: MINGDAO_AUTH_CONFIG.translationFields.user,
        value: currentUser.id  // 直接传入用户的rowid
      });
    }

    const requestData = {
      appKey: MINGDAO_AUTH_CONFIG.appKey,
      sign: MINGDAO_AUTH_CONFIG.sign,
      worksheetId: MINGDAO_AUTH_CONFIG.worksheets.translations,
      triggerWorkflow: true,
      controls: controls
    };

    const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/addRow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('明道云上传响应:', result);

    // 明道云API成功的判断条件
    if (result.success === true || result.error_code === 1) {
      const rowId = result.data?.rowid || result.data?.id || result.rowid;
      console.log('文件上传成功, rowId:', rowId);

      return {
        success: true,
        data: {
          rowId: rowId,
          ...result.data
        },
        message: '文件上传成功'
      };
    } else {
      console.error('明道云API返回错误:', result);
      throw new Error(result.message || result.error_msg || '文件上传失败');
    }
  } catch (error) {
    console.error('上传文件到明道云失败:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : '文件上传失败'
    };
  }
};

// 设置基本翻译信息（不开始翻译）
export const setBasicTranslationInfo = async (
  rowId: string,
  fileName: string,
  sourceLanguage: string,
  targetLanguage: string
): Promise<ApiResponse> => {
  try {
    console.log('设置基本翻译信息:', { rowId, fileName, sourceLanguage, targetLanguage });

    if (!rowId) {
      throw new Error('rowId 不能为空');
    }

    // 计算预估字符数和费用
    const estimatedChars = Math.max(1000, fileName.length * 50);
    const cost = estimatedChars * 0.001;

    // 只设置基本信息，状态为"未开始"
    const controls = [
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.total_chars,
        value: estimatedChars.toString()
      },
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.cost,
        value: cost.toFixed(2)
      },
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.created_at,
        value: new Date().toISOString()
      }
      // 注意：不设置status，保持为空或默认状态
    ];

    const requestData = {
      appKey: MINGDAO_AUTH_CONFIG.appKey,
      sign: MINGDAO_AUTH_CONFIG.sign,
      worksheetId: MINGDAO_AUTH_CONFIG.worksheets.translations,
      rowId: rowId,
      triggerWorkflow: false, // 不触发工作流
      controls: controls
    };

    const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/editRow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('设置基本信息响应:', result);

    if (result.success === true || result.error_code === 1) {
      console.log('基本翻译信息设置成功');
      return {
        success: true,
        data: result.data,
        message: '基本翻译信息设置成功'
      };
    } else {
      console.error('设置基本信息失败:', result);
      throw new Error(result.message || result.error_msg || '设置基本信息失败');
    }
  } catch (error) {
    console.error('设置基本翻译信息失败:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : '设置基本翻译信息失败'
    };
  }
};

// 开始翻译（手动触发，包含扣费逻辑）
export const startTranslation = async (
  rowId: string,
  sourceLanguage: string,
  targetLanguage: string,
  translationSettings?: any
): Promise<ApiResponse> => {
  try {
    console.log('开始翻译处理:', { rowId, sourceLanguage, targetLanguage });

    if (!rowId) {
      throw new Error('rowId 不能为空');
    }

    // 第一步：检查用户余额和配额
    const currentUser = authService.getCurrentUser();
    if (!currentUser || !authService.isAuthenticated()) {
      throw new Error('用户未登录，请先登录');
    }

    // 第二步：更新翻译状态为"待处理"并设置付费方式
    const controls = [
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.status,
        value: '1784937f-c546-43f5-9d78-02b326a72bde' // 待处理状态
      },
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.payment_type,
        value: 'e63e40ab-b732-4601-abfb-054f017ab320' // 免费额度，后续可根据用户情况调整
      },
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.updated_at,
        value: new Date().toISOString()
      }
    ];

    const requestData = {
      appKey: MINGDAO_AUTH_CONFIG.appKey,
      sign: MINGDAO_AUTH_CONFIG.sign,
      worksheetId: MINGDAO_AUTH_CONFIG.worksheets.translations,
      rowId: rowId,
      triggerWorkflow: true, // 触发翻译工作流
      controls: controls
    };

    const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/editRow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('开始翻译响应:', result);

    if (result.success === true || result.error_code === 1) {
      // 第三步：触发后端翻译处理
      try {
        await triggerBackendTranslation(rowId, sourceLanguage, targetLanguage, translationSettings);
      } catch (e) {
        console.warn('触发后端翻译失败:', e);
      }

      return {
        success: true,
        data: result.data,
        message: '翻译已开始，请等待处理完成'
      };
    } else {
      console.error('开始翻译失败:', result);
      throw new Error(result.message || result.error_msg || '开始翻译失败');
    }
  } catch (error) {
    console.error('开始翻译失败:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : '开始翻译失败'
    };
  }
};

// 原有的创建翻译记录函数（保留兼容性）
export const createTranslationRecord = async (
  rowId: string,
  fileName: string,
  sourceLanguage: string,
  targetLanguage: string
): Promise<ApiResponse> => {
  try {
    console.log('开始更新翻译记录:', { rowId, fileName, sourceLanguage, targetLanguage });

    if (!rowId) {
      throw new Error('rowId 不能为空');
    }

    // 计算预估字符数（简单估算）
    const estimatedChars = Math.max(1000, fileName.length * 50); // 更合理的估算
    const cost = estimatedChars * 0.001; // 每1000字符1元

    // 更新翻译记录的其他字段
    const controls = [
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.status,
        value: '1784937f-c546-43f5-9d78-02b326a72bde' // 待处理状态
      },
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.total_chars,
        value: estimatedChars.toString()
      },
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.cost,
        value: cost.toFixed(2)
      },
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.payment_type,
        value: 'e63e40ab-b732-4601-abfb-054f017ab320' // 免费额度
      },
      {
        controlId: MINGDAO_AUTH_CONFIG.translationFields.created_at,
        value: new Date().toISOString()
      }
    ];

    const requestData = {
      appKey: MINGDAO_AUTH_CONFIG.appKey,
      sign: MINGDAO_AUTH_CONFIG.sign,
      worksheetId: MINGDAO_AUTH_CONFIG.worksheets.translations,
      rowId: rowId,
      triggerWorkflow: true, // 重要：触发明道云工作流
      controls: controls
    };

    const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/editRow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('更新翻译记录响应:', result);

    // 明道云editRow API的成功判断
    if (result.success === true || result.error_code === 1) {
      console.log('翻译记录更新成功');
      return {
        success: true,
        data: result.data,
        message: '翻译记录创建成功，翻译将通过工作流自动处理'
      };
    } else {
      console.error('更新翻译记录失败:', result);
      throw new Error(result.message || result.error_msg || '创建翻译记录失败');
    }
  } catch (error) {
    console.error('创建翻译记录失败:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : '创建翻译记录失败'
    };
  }
};

// 触发后端翻译处理（可选，主要依赖明道云工作流）
export const triggerBackendTranslation = async (
  rowId: string,
  sourceLanguage: string,
  targetLanguage: string,
  translationSettings?: any
): Promise<void> => {
  try {
    // 如果后端服务可用，尝试触发翻译
    const response = await fetch('http://localhost:8000/api/v1/translate/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        row_id: rowId,
        source_language: sourceLanguage,
        target_language: targetLanguage,
        translation_settings: translationSettings
      })
    });

    if (response.ok) {
      console.log('后端翻译服务已触发');
    } else {
      console.warn('后端翻译服务不可用，依赖明道云工作流处理');
    }
  } catch (error) {
    console.warn('后端翻译服务连接失败，依赖明道云工作流处理:', error);
  }
};



export const uploadMultipleFiles = async (
  files: File[],
  sourceLanguage: string = 'zh',
  targetLanguage: string = 'en',
  onProgress?: (progress: number, fileName: string) => void
): Promise<ApiResponse[]> => {
  const results: ApiResponse[] = [];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];

    try {
      if (onProgress) {
        onProgress((i / files.length) * 100, file.name);
      }

      // 使用新的仅上传文件流程
      const result = await uploadFileOnly(
        file,
        sourceLanguage,
        targetLanguage,
        (fileProgress) => {
          if (onProgress) {
            const totalProgress = ((i + fileProgress / 100) / files.length) * 100;
            onProgress(totalProgress, file.name);
          }
        }
      );

      results.push(result);

      // 添加延迟避免请求过于频繁
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error(`上传文件并创建翻译 ${file.name} 失败:`, error);
      results.push({
        success: false,
        data: null,
        message: `上传文件并创建翻译 ${file.name} 失败`
      });
    }
  }

  if (onProgress) {
    onProgress(100, '上传完成');
  }

  return results;
};

/**
 * 创建默认过滤器（根据你提供的示例）
 */
export const createDefaultFilters = (): ApiFilter[] => {
  return API_CONFIG.defaultFilters;
};
