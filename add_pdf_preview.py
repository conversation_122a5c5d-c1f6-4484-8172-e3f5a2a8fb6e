"""
添加PDF预览功能的脚本
"""
import os
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import sqlite3

def create_pdf_preview(translation_id, output_path):
    """创建翻译结果的PDF预览"""
    try:
        # 从数据库获取翻译结果
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT th.translated_text, th.source_language, th.target_language,
                   uf.original_filename
            FROM translation_histories th
            JOIN uploaded_files uf ON th.file_id = uf.id
            WHERE th.id = ? AND th.status = 'COMPLETED'
        ''', (translation_id,))
        
        result = cursor.fetchone()
        if not result:
            print(f"未找到翻译ID {translation_id} 的完成记录")
            return False
        
        translated_text, source_lang, target_lang, filename = result
        conn.close()
        
        # 创建PDF文档
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        story = []
        
        # 获取样式
        styles = getSampleStyleSheet()
        
        # 标题样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # 居中
        )
        
        # 正文样式
        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            leftIndent=20,
            rightIndent=20
        )
        
        # 添加标题
        title = f"翻译文档预览 - {filename}"
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 20))
        
        # 添加语言信息
        lang_info = f"翻译方向：{source_lang} → {target_lang}"
        story.append(Paragraph(lang_info, body_style))
        story.append(Spacer(1, 20))
        
        # 添加翻译内容
        if translated_text:
            # 分段处理
            paragraphs = translated_text.split('\n')
            for para in paragraphs:
                if para.strip():
                    # 转义HTML特殊字符
                    para = para.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                    story.append(Paragraph(para, body_style))
                    story.append(Spacer(1, 6))
        else:
            story.append(Paragraph("翻译内容为空", body_style))
        
        # 生成PDF
        doc.build(story)
        print(f"PDF预览已生成：{output_path}")
        return True
        
    except Exception as e:
        print(f"生成PDF预览失败：{e}")
        return False

def test_pdf_preview():
    """测试PDF预览功能"""
    print("🔍 测试PDF预览功能")
    print("=" * 50)
    
    # 获取最新的完成翻译
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, translated_characters
        FROM translation_histories 
        WHERE status = 'COMPLETED'
        ORDER BY id DESC 
        LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if not result:
        print("❌ 没有找到已完成的翻译记录")
        return
    
    translation_id, char_count = result
    print(f"📄 使用翻译ID: {translation_id} ({char_count} 字符)")
    
    # 生成PDF预览
    output_path = f"translation_preview_{translation_id}.pdf"
    success = create_pdf_preview(translation_id, output_path)
    
    if success:
        print(f"✅ PDF预览生成成功: {output_path}")
        print(f"📁 文件大小: {os.path.getsize(output_path)} 字节")
    else:
        print("❌ PDF预览生成失败")
    
    conn.close()

if __name__ == "__main__":
    test_pdf_preview()
