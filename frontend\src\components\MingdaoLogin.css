.mingdao-login {
  max-width: 450px;
  margin: 0 auto;
  padding: 30px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 头部 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #333;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.login-header p {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

/* 登录结果 */
.login-result {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.login-result.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.login-result.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

/* 表单 */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.form-group input {
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
  background: white;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error {
  border-color: #dc3545;
}

.form-group input:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

/* 密码输入框 */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  padding-right: 45px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.password-toggle:hover {
  color: #333;
}

.password-toggle:disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* 错误信息 */
.error-message {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 4px;
}

/* 表单操作 */
.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 10px;
}

.login-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, opacity 0.2s;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancel-btn {
  padding: 14px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn:hover:not(:disabled) {
  background: #5a6268;
}

.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 演示账号 */
.demo-accounts {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.demo-accounts h4 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.demo-accounts h4:before {
  content: "🎭";
  font-size: 1.2rem;
}

.demo-account-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.demo-account {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.account-info strong {
  color: #333;
  font-size: 0.9rem;
}

.account-info span {
  color: #666;
  font-size: 0.8rem;
}

.account-info small {
  color: #888;
  font-size: 0.75rem;
}

.quick-login-btn {
  padding: 6px 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.quick-login-btn:hover:not(:disabled) {
  background: #218838;
}

.quick-login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 注册链接 */
.register-link {
  text-align: center;
  margin-bottom: 25px;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 8px;
  border: 1px solid #2196f3;
}

.register-link p {
  margin: 0;
  color: #333;
  font-size: 0.9rem;
}

.link-btn {
  background: none;
  border: none;
  color: #2196f3;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  margin-left: 5px;
  transition: color 0.2s;
}

.link-btn:hover:not(:disabled) {
  color: #1976d2;
}

.link-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 登录说明 */
.login-info {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.login-info h4 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.login-info h4:before {
  content: "✨";
  font-size: 1.2rem;
}

.login-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.login-info li {
  font-size: 0.9rem;
  color: #555;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.login-info li:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mingdao-login {
    margin: 10px;
    padding: 20px;
  }
  
  .login-header h2 {
    font-size: 1.3rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .login-btn,
  .cancel-btn {
    width: 100%;
  }
  
  .demo-account {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .quick-login-btn {
    align-self: stretch;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .mingdao-login {
    margin: 5px;
    padding: 15px;
  }
  
  .form-group input {
    padding: 10px 12px;
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  .password-input input {
    padding-right: 40px;
  }
  
  .password-toggle {
    right: 10px;
  }
  
  .login-btn,
  .cancel-btn {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .demo-accounts,
  .login-info {
    padding: 15px;
  }
}

/* 焦点状态增强 */
.form-group input:focus,
.login-btn:focus,
.cancel-btn:focus,
.password-toggle:focus,
.quick-login-btn:focus,
.link-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* 成功状态样式 */
.login-btn.success {
  background: #28a745;
}

.login-btn.success:hover {
  background: #218838;
}

/* 动画效果 */
.mingdao-login {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-result {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
