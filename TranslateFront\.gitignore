# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建产物
dist/
build/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log
app.log

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
.nyc_output/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# PM2
.pm2/

# 测试
test-results/
playwright-report/
