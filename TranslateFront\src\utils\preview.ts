import { DocumentRow, DocumentFile } from '../types/document';
import { PREVIEW_CONFIG, CONTROL_IDS } from '../config/api.config';
// import { signOnlyOfficeConfig } from './jwt'; // OnlyOffice 已禁用

/**
 * 生成预览地址
 * @param rowid 行记录ID
 * @returns 预览地址
 */
export const generatePreviewUrl = (rowid: string): string => {
  // 暂时禁用 OnlyOffice，使用原始预览方式
  // if (PREVIEW_CONFIG.onlyoffice.enabled) {
  //   // OnlyOffice 预览需要在前端页面中嵌入，这里返回一个标识
  //   return `onlyoffice:${rowid}`;
  // } else {
    // 使用原始预览方式
    return `${PREVIEW_CONFIG.baseUrl}/${rowid}/`;
  // }
};

/**
 * 从文档行数据中获取翻译后的文件列表
 * @param row 文档行数据
 * @returns 翻译后的文件列表
 */
export const getProcessedFiles = (row: DocumentRow): DocumentFile[] => {
  // 尝试多种字段名获取翻译后文件数据
  const processedFileData = row['translated_file'] || row[CONTROL_IDS.PROCESSED_FILE] || row['6886f7a4a849420e13f69b70'];

  if (!processedFileData || typeof processedFileData !== 'string') {
    return [];
  }

  try {
    // 检查是否是JSON字符串格式
    if (processedFileData.startsWith('[{')) {
      const files = JSON.parse(processedFileData);
      console.log('解析到的翻译文件:', files);
      return Array.isArray(files) ? files : [];
    }
    return [];
  } catch (error) {
    console.warn('解析翻译后文件数据失败:', error);
    return [];
  }
};

/**
 * 检查文档是否有翻译后的文件（可以预览）
 * @param row 文档行数据
 * @returns 是否可以预览
 */
export const hasProcessedFiles = (row: DocumentRow): boolean => {
  const processedFiles = getProcessedFiles(row);
  return processedFiles.length > 0;
};

/**
 * 获取文档的预览信息
 * @param row 文档行数据
 * @returns 预览信息对象
 */
export const getPreviewInfo = (row: DocumentRow) => {
  const hasFiles = hasProcessedFiles(row);
  const previewUrl = hasFiles ? generatePreviewUrl(row.rowid) : null;
  const processedFiles = hasFiles ? getProcessedFiles(row) : [];

  return {
    canPreview: hasFiles,
    previewUrl,
    processedFiles,
    rowid: row.rowid
  };
};

/**
 * 从文档行数据中获取原始文件列表
 * @param row 文档行数据
 * @returns 原始文件列表
 */
export const getOriginalFiles = (row: DocumentRow): DocumentFile[] => {
  // 尝试多种字段名获取原文件数据
  const originalFileData = row['original_file'] || row[CONTROL_IDS.ORIGINAL_FILE] || row['6886f7a4a849420e13f69b6f'];

  if (!originalFileData || typeof originalFileData !== 'string') {
    console.warn('原文件数据不存在或格式错误:', { originalFileData, rowKeys: Object.keys(row) });
    return [];
  }

  try {
    if (originalFileData.startsWith('[{')) {
      const files = JSON.parse(originalFileData);
      console.log('解析到的原文件:', files);
      return Array.isArray(files) ? files : [];
    }
    return [];
  } catch (error) {
    console.warn('解析原始文件数据失败:', error);
    return [];
  }
};

/**
 * 获取文档的所有文件信息
 * @param row 文档行数据
 * @returns 文件信息对象
 */
export const getDocumentFiles = (row: DocumentRow) => {
  const originalFiles = getOriginalFiles(row);
  const processedFiles = getProcessedFiles(row);
  const previewInfo = getPreviewInfo(row);

  return {
    originalFiles,
    processedFiles,
    previewInfo,
    hasOriginalFiles: originalFiles.length > 0,
    hasProcessedFiles: processedFiles.length > 0
  };
};

/**
 * 打开预览窗口
 * @param rowid 行记录ID
 */
export const openPreviewWindow = (rowid: string) => {
  const previewUrl = generatePreviewUrl(rowid);
  window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
};

/**
 * 检查文件是否为支持预览的格式
 * @param fileName 文件名
 * @returns 是否支持预览
 */
export const isSupportedPreviewFormat = (fileName: string): boolean => {
  const supportedExtensions = [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', 
    '.ppt', '.pptx', '.txt', '.jpg', '.jpeg', 
    '.png', '.gif', '.bmp'
  ];
  
  const extension = '.' + fileName.split('.').pop()?.toLowerCase();
  return supportedExtensions.includes(extension);
};

/**
 * 获取文件类型图标类名
 * @param fileName 文件名
 * @returns 图标类名
 */
export const getFileTypeClass = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return 'file-pdf';
    case 'doc':
    case 'docx':
      return 'file-word';
    case 'xls':
    case 'xlsx':
      return 'file-excel';
    case 'ppt':
    case 'pptx':
      return 'file-powerpoint';
    case 'txt':
      return 'file-text';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
      return 'file-image';
    default:
      return 'file-unknown';
  }
};

/**
 * 检查文件是否支持 OnlyOffice 预览（暂时禁用）
 * @param fileName 文件名
 * @returns 是否支持预览
 */
// export const isSupportedByOnlyOffice = (fileName: string): boolean => {
//   const extension = fileName.split('.').pop()?.toLowerCase();
//   return extension ? PREVIEW_CONFIG.onlyoffice.supportedFormats.includes(extension) : false;
// };

/**
 * 生成 OnlyOffice 配置对象（暂时禁用）
 * @param rowid 行记录ID
 * @param fileName 文件名
 * @returns OnlyOffice 配置对象（带 JWT 签名）
 */
// export const generateOnlyOfficeConfig = async (rowid: string, fileName: string) => {
//   const fileUrl = `${PREVIEW_CONFIG.baseUrl}/${rowid}/`;
//   const fileType = fileName.split('.').pop()?.toLowerCase() || 'docx';

//   const config = {
//     document: {
//       fileType: fileType,
//       key: `${rowid}_${Date.now()}`, // 唯一标识符
//       title: fileName,
//       url: fileUrl
//     },
//     documentType: getOnlyOfficeDocumentType(fileType),
//     editorConfig: {
//       mode: PREVIEW_CONFIG.onlyoffice.editor.mode,
//       lang: PREVIEW_CONFIG.onlyoffice.editor.lang,
//       customization: {
//         autosave: false,
//         chat: false,
//         comments: false,
//         help: false,
//         hideRightMenu: true,
//         plugins: false,
//         review: false,
//         toolbar: false,
//         zoom: 100
//       }
//     },
//     height: '100%',
//     width: '100%'
//   };

//   // 如果启用了 JWT，则对配置进行签名
//   return await signOnlyOfficeConfig(config);
// };

/**
 * 根据文件类型获取 OnlyOffice 文档类型（暂时禁用）
 * @param fileType 文件扩展名
 * @returns OnlyOffice 文档类型
 */
// export const getOnlyOfficeDocumentType = (fileType: string): string => {
//   const wordFormats = ['doc', 'docx', 'txt', 'rtf', 'odt'];
//   const cellFormats = ['xls', 'xlsx', 'csv', 'ods'];
//   const slideFormats = ['ppt', 'pptx', 'odp'];

//   if (wordFormats.includes(fileType)) {
//     return 'word';
//   } else if (cellFormats.includes(fileType)) {
//     return 'cell';
//   } else if (slideFormats.includes(fileType)) {
//     return 'slide';
//   } else {
//     return 'word'; // 默认
//   }
// };
