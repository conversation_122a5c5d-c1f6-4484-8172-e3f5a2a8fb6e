"""
翻译相关的 Pydantic 模型
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from app.models.translation import TranslationStatus


class TranslationFormatSettings(BaseModel):
    """单个区域的翻译格式设置"""
    font_family: str = Field(default="Arial", description="字体名称")
    font_size: float = Field(default=10.5, ge=6, le=72, description="字体大小(pt)")
    bold: bool = Field(default=False, description="粗体")
    italic: bool = Field(default=False, description="斜体")
    underline: bool = Field(default=False, description="下划线")
    text_align: str = Field(default="inherit", description="对齐方式")
    text_indent: float = Field(default=0, ge=0, le=10, description="首行缩进(字符)")
    line_height: str = Field(default="inherit", description="行间距")
    margin_top: float = Field(default=0, ge=0, le=50, description="段前间距(pt)")
    color: str = Field(default="#000000", description="文字颜色")
    background_color: str = Field(default="#ffffff", description="背景颜色")


class TranslationSettings(BaseModel):
    """完整的翻译设置（包含三个区域）"""
    paragraph: TranslationFormatSettings = Field(default_factory=lambda: TranslationFormatSettings(font_size=10.5))
    table: TranslationFormatSettings = Field(default_factory=lambda: TranslationFormatSettings(font_size=6))
    header: TranslationFormatSettings = Field(default_factory=lambda: TranslationFormatSettings(font_size=10.5))

    enable_paragraph: bool = Field(default=True, description="是否启用正文翻译")
    enable_table: bool = Field(default=True, description="是否启用表格翻译")
    enable_header: bool = Field(default=True, description="是否启用页眉翻译")


class TranslationRequest(BaseModel):
    """翻译请求模型"""
    file_id: int = Field(..., description="要翻译的文件ID")
    source_language: str = Field(..., max_length=10, description="源语言代码 (如: en, zh)")
    target_language: str = Field(..., max_length=10, description="目标语言代码 (如: zh, en)")
    preserve_formatting: bool = Field(default=True, description="是否保留格式")
    include_original: bool = Field(default=True, description="是否包含原文")
    translation_settings: Optional[TranslationSettings] = Field(default=None, description="翻译格式设置")


class TranslationResponse(BaseModel):
    """翻译响应模型"""
    id: int
    file_id: int
    source_language: str
    target_language: str
    status: TranslationStatus
    progress: float
    total_characters: int
    translated_characters: int
    confidence_score: Optional[float]
    cost: float
    created_at: str
    updated_at: Optional[str]
    completed_at: Optional[str]
    
    class Config:
        from_attributes = True


class TranslationResult(BaseModel):
    """翻译结果模型"""
    id: int
    status: TranslationStatus
    progress: float
    original_text: Optional[str]
    translated_text: Optional[str]
    confidence_score: Optional[float]
    error_message: Optional[str]
    terms_checked: bool
    term_corrections: Optional[str]


class TranslationHistory(BaseModel):
    """翻译历史模型"""
    id: int
    file_id: int
    filename: str
    source_language: str
    target_language: str
    status: TranslationStatus
    progress: float
    total_characters: int
    translated_characters: int
    confidence_score: Optional[float]
    cost: float
    created_at: str
    completed_at: Optional[str]
    
    class Config:
        from_attributes = True


class TranslationListResponse(BaseModel):
    """翻译历史列表响应"""
    translations: List[TranslationHistory]
    total: int
    page: int
    page_size: int


class LanguageInfo(BaseModel):
    """语言信息模型"""
    code: str
    name: str
    native_name: str


class SupportedLanguagesResponse(BaseModel):
    """支持的语言列表响应"""
    languages: List[LanguageInfo]


class TranslationProgress(BaseModel):
    """翻译进度模型"""
    translation_id: int
    status: TranslationStatus
    progress: float = Field(ge=0.0, le=1.0)
    current_step: str
    estimated_time_remaining: Optional[int] = Field(None, description="预计剩余时间(秒)")
    error_message: Optional[str]


class BatchTranslationRequest(BaseModel):
    """批量翻译请求模型"""
    file_ids: List[int] = Field(..., min_items=1, max_items=10, description="文件ID列表")
    source_language: str = Field(..., max_length=10)
    target_language: str = Field(..., max_length=10)
    preserve_formatting: bool = Field(default=True)
    include_original: bool = Field(default=True)


class BatchTranslationResponse(BaseModel):
    """批量翻译响应模型"""
    batch_id: str
    translation_ids: List[int]
    total_files: int
    estimated_cost: float
    estimated_time: int = Field(description="预计完成时间(秒)")


class TranslationStatistics(BaseModel):
    """翻译统计模型"""
    total_translations: int
    completed_translations: int
    failed_translations: int
    total_characters_translated: int
    total_cost: float
    average_confidence: float
    most_used_source_language: str
    most_used_target_language: str
