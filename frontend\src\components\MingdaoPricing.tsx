import React, { useState } from 'react';
import { Calculator, DollarSign, Users, Zap, Gift } from 'lucide-react';
import { MINGDAO_CONFIG } from '../config/api.config';
import './MingdaoPricing.css';

interface PricingCalculatorProps {
  onCalculate?: (charCount: number, cost: number, paymentType: string) => void;
}

const MingdaoPricing: React.FC<PricingCalculatorProps> = ({ onCalculate }) => {
  const [charCount, setCharCount] = useState<number>(8000);
  const [calculationResult, setCalculationResult] = useState<{
    cost: number;
    paymentType: string;
    description: string;
  } | null>(null);

  // 计算翻译费用
  const calculateCost = (chars: number) => {
    const rules = MINGDAO_CONFIG.pricingRules;
    
    if (chars <= 0) {
      return { cost: 0, paymentType: '无效输入', description: '字符数必须大于0' };
    }

    if (chars <= rules.per_translation_small.max_chars) {
      return {
        cost: rules.per_translation_small.price,
        paymentType: '按次付费',
        description: `${chars}字符，按次收费`
      };
    } else if (chars >= rules.bulk_discount.min_chars) {
      return {
        cost: rules.bulk_discount.price,
        paymentType: '批量优惠',
        description: `${chars}字符，批量优惠`
      };
    } else {
      const cost = Math.ceil(chars / 5000) * rules.per_translation_large.price_per_5k;
      return {
        cost,
        paymentType: '按次付费',
        description: `${chars}字符，分段收费`
      };
    }
  };

  const handleCalculate = () => {
    const result = calculateCost(charCount);
    setCalculationResult(result);
    onCalculate?.(charCount, result.cost, result.paymentType);
  };

  const handleCharCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    setCharCount(value);
    
    // 实时计算
    if (value > 0) {
      const result = calculateCost(value);
      setCalculationResult(result);
      onCalculate?.(value, result.cost, result.paymentType);
    }
  };

  const rules = MINGDAO_CONFIG.pricingRules;

  return (
    <div className="mingdao-pricing">
      <div className="pricing-header">
        <h2>
          <DollarSign size={24} />
          明道云翻译收费规则
        </h2>
        <p>透明的收费标准，多种付费方式可选</p>
      </div>

      {/* 收费计算器 */}
      <div className="pricing-calculator">
        <h3>
          <Calculator size={20} />
          费用计算器
        </h3>
        <div className="calculator-input">
          <label htmlFor="charCount">输入字符数：</label>
          <input
            id="charCount"
            type="number"
            value={charCount}
            onChange={handleCharCountChange}
            min="1"
            max="1000000"
            placeholder="请输入字符数"
          />
          <button onClick={handleCalculate} className="calculate-btn">
            计算费用
          </button>
        </div>

        {calculationResult && (
          <div className="calculation-result">
            <div className="result-card">
              <h4>计算结果</h4>
              <div className="result-details">
                <div className="result-item">
                  <span className="label">字符数：</span>
                  <span className="value">{charCount.toLocaleString()}</span>
                </div>
                <div className="result-item">
                  <span className="label">费用：</span>
                  <span className="value cost">¥{calculationResult.cost}</span>
                </div>
                <div className="result-item">
                  <span className="label">付费方式：</span>
                  <span className="value">{calculationResult.paymentType}</span>
                </div>
                <div className="result-item">
                  <span className="label">说明：</span>
                  <span className="value">{calculationResult.description}</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 收费规则表格 */}
      <div className="pricing-rules">
        <h3>收费标准</h3>
        <div className="rules-grid">
          <div className="rule-card free">
            <div className="rule-icon">
              <Gift size={32} />
            </div>
            <h4>免费额度</h4>
            <div className="rule-price">¥0</div>
            <div className="rule-desc">新用户1000字符</div>
            <ul className="rule-features">
              <li>注册即送</li>
              <li>无需付费</li>
              <li>体验翻译服务</li>
            </ul>
          </div>

          <div className="rule-card small">
            <div className="rule-icon">
              <Zap size={32} />
            </div>
            <h4>按次付费(小)</h4>
            <div className="rule-price">¥{rules.per_translation_small.price}</div>
            <div className="rule-desc">1-{rules.per_translation_small.max_chars}字符</div>
            <ul className="rule-features">
              <li>适合小文档</li>
              <li>即用即付</li>
              <li>无需包月</li>
            </ul>
          </div>

          <div className="rule-card large">
            <div className="rule-icon">
              <Calculator size={32} />
            </div>
            <h4>按次付费(大)</h4>
            <div className="rule-price">¥{rules.per_translation_large.price_per_5k}/5K字</div>
            <div className="rule-desc">5001字符以上</div>
            <ul className="rule-features">
              <li>分段计费</li>
              <li>每5000字{rules.per_translation_large.price_per_5k}元</li>
              <li>适合大文档</li>
            </ul>
          </div>

          <div className="rule-card monthly">
            <div className="rule-icon">
              <Users size={32} />
            </div>
            <h4>月付套餐</h4>
            <div className="rule-price">¥{rules.monthly.price}/月</div>
            <div className="rule-desc">{(rules.monthly.quota / 10000).toFixed(0)}万字符/月</div>
            <ul className="rule-features">
              <li>包月优惠</li>
              <li>大量翻译</li>
              <li>性价比高</li>
            </ul>
          </div>

          <div className="rule-card bulk">
            <div className="rule-icon">
              <DollarSign size={32} />
            </div>
            <h4>批量优惠</h4>
            <div className="rule-price">¥{rules.bulk_discount.price}</div>
            <div className="rule-desc">{(rules.bulk_discount.min_chars / 10000).toFixed(0)}万字符</div>
            <ul className="rule-features">
              <li>超值优惠</li>
              <li>大批量翻译</li>
              <li>企业首选</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 示例计算 */}
      <div className="pricing-examples">
        <h3>费用示例</h3>
        <div className="examples-table">
          <table>
            <thead>
              <tr>
                <th>字符数</th>
                <th>费用</th>
                <th>付费方式</th>
                <th>适用场景</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1,000</td>
                <td>¥0</td>
                <td>免费额度</td>
                <td>新用户体验</td>
              </tr>
              <tr>
                <td>3,000</td>
                <td>¥5</td>
                <td>按次付费</td>
                <td>短文档翻译</td>
              </tr>
              <tr>
                <td>8,000</td>
                <td>¥10</td>
                <td>按次付费</td>
                <td>中等文档</td>
              </tr>
              <tr>
                <td>15,000</td>
                <td>¥15</td>
                <td>按次付费</td>
                <td>长文档</td>
              </tr>
              <tr>
                <td>50,000</td>
                <td>¥20</td>
                <td>月付套餐</td>
                <td>月度翻译</td>
              </tr>
              <tr>
                <td>300,000</td>
                <td>¥100</td>
                <td>批量优惠</td>
                <td>大批量翻译</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 付费说明 */}
      <div className="pricing-notes">
        <h3>付费说明</h3>
        <div className="notes-content">
          <div className="note-item">
            <h4>配额优先级</h4>
            <p>系统会按以下顺序扣除配额：月付配额 → 免费配额 → 余额付费</p>
          </div>
          <div className="note-item">
            <h4>月付套餐</h4>
            <p>月付套餐有效期30天，到期后自动转为按次付费模式</p>
          </div>
          <div className="note-item">
            <h4>批量优惠</h4>
            <p>单次翻译达到30万字符时自动享受批量优惠价格</p>
          </div>
          <div className="note-item">
            <h4>余额充值</h4>
            <p>余额不足时可随时充值，支持多种支付方式</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MingdaoPricing;
