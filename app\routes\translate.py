"""
翻译相关路由
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import StreamingResponse, Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import io
import os
from datetime import datetime

from app.db.database import get_async_db
from app.models.file import UploadedFile
from app.models.translation import TranslationHistory
from app.schemas.translation import (
    TranslationRequest, TranslationResponse, TranslationResult,
    TranslationListResponse, TranslationHistory as TranslationHistorySchema,
    SupportedLanguagesResponse, LanguageInfo, TranslationProgress
)
from app.services.translation_service import TranslationService
from app.services.azure_translator import azure_translator
from app.services.docx_processor import docx_processor
from app.utils.dependencies import get_current_active_user, check_user_quota
from app.utils.logger import logger


router = APIRouter()


@router.post("/", response_model=TranslationResponse, status_code=status.HTTP_201_CREATED)
async def create_translation(
    request: TranslationRequest,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """
    创建翻译任务
    
    支持的语言代码示例：
    - en: 英语
    - zh: 中文
    - ja: 日语
    - ko: 韩语
    - fr: 法语
    - de: 德语
    - es: 西班牙语
    """
    try:
        translation = await TranslationService.create_translation(db, request, current_user)
        
        # 在数据库会话中获取所有需要的数据，避免延迟加载问题
        translation_data = {
            'id': translation.id,
            'file_id': translation.file_id,
            'source_language': translation.source_language,
            'target_language': translation.target_language,
            'status': translation.status,
            'progress': translation.progress,
            'total_characters': translation.total_characters,
            'translated_characters': translation.translated_characters,
            'confidence_score': translation.confidence_score,
            'cost': translation.cost,
            'created_at': translation.created_at.isoformat() if translation.created_at else None,
            'updated_at': translation.updated_at.isoformat() if translation.updated_at else None,
            'completed_at': translation.completed_at.isoformat() if translation.completed_at else None
        }

        return TranslationResponse(**translation_data)
    except Exception as e:
        logger.error(f"Create translation error: {e}")
        raise


@router.get("/languages", response_model=SupportedLanguagesResponse)
async def get_supported_languages():
    """获取支持的翻译语言列表"""
    try:
        languages_data = await azure_translator.get_supported_languages()
        translation_langs = languages_data.get('translation', {})
        
        languages = []
        for code, info in translation_langs.items():
            languages.append(LanguageInfo(
                code=code,
                name=info.get('name', code),
                native_name=info.get('nativeName', info.get('name', code))
            ))
        
        # 按语言代码排序
        languages.sort(key=lambda x: x.code)
        
        return SupportedLanguagesResponse(languages=languages)
    except Exception as e:
        logger.error(f"Get supported languages error: {e}")
        raise


@router.get("/", response_model=TranslationListResponse)
async def get_translations(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取用户的翻译历史列表"""
    try:
        skip = (page - 1) * page_size
        translations, total = await TranslationService.get_user_translations(
            db, current_user, skip, page_size
        )
        
        translation_list = []
        for translation in translations:
            # 获取文件信息
            file_result = await db.execute(
                select(UploadedFile).where(UploadedFile.id == translation.file_id)
            )
            file_record = file_result.scalar_one_or_none()
            filename = file_record.original_filename if file_record else "Unknown"
            
            translation_list.append(TranslationHistorySchema(
                id=translation.id,
                file_id=translation.file_id,
                filename=filename,
                source_language=translation.source_language,
                target_language=translation.target_language,
                status=translation.status,
                progress=translation.progress,
                total_characters=translation.total_characters,
                translated_characters=translation.translated_characters,
                confidence_score=translation.confidence_score,
                cost=translation.cost,
                created_at=translation.created_at.isoformat() if translation.created_at else None,
                completed_at=translation.completed_at.isoformat() if translation.completed_at else None
            ))
        
        return TranslationListResponse(
            translations=translation_list,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        logger.error(f"Get translations error: {e}")
        raise


@router.get("/{translation_id}", response_model=TranslationResult)
async def get_translation(
    translation_id: int,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取翻译结果详情"""
    try:
        translation = await TranslationService.get_translation_by_id(
            db, translation_id, current_user
        )
        
        if not translation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Translation not found"
            )
        
        return TranslationResult(
            id=translation.id,
            status=translation.status,
            progress=translation.progress,
            original_text=translation.original_text,
            translated_text=translation.translated_text,
            confidence_score=translation.confidence_score,
            error_message=translation.error_message,
            terms_checked=translation.terms_checked,
            term_corrections=translation.term_corrections
        )
    except Exception as e:
        logger.error(f"Get translation error: {e}")
        raise


@router.get("/{translation_id}/progress", response_model=TranslationProgress)
async def get_translation_progress(
    translation_id: int,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取翻译进度"""
    try:
        translation = await TranslationService.get_translation_by_id(
            db, translation_id, current_user
        )
        
        if not translation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Translation not found"
            )
        
        # 确定当前步骤
        current_step = "准备中"
        if translation.status == "pending":
            current_step = "等待处理"
        elif translation.status == "in_progress":
            if translation.progress < 0.2:
                current_step = "初始化翻译"
            elif translation.progress < 0.8:
                current_step = "翻译进行中"
            else:
                current_step = "完成处理"
        elif translation.status == "completed":
            current_step = "翻译完成"
        elif translation.status == "failed":
            current_step = "翻译失败"
        
        # 估算剩余时间
        estimated_time_remaining = None
        if translation.status == "in_progress" and translation.progress > 0:
            remaining_chars = translation.total_characters - translation.translated_characters
            estimated_time_remaining = azure_translator.estimate_translation_time(remaining_chars)
        
        return TranslationProgress(
            translation_id=translation.id,
            status=translation.status,
            progress=translation.progress,
            current_step=current_step,
            estimated_time_remaining=estimated_time_remaining,
            error_message=translation.error_message
        )
    except Exception as e:
        logger.error(f"Get translation progress error: {e}")
        raise


@router.post("/{translation_id}/cancel")
async def cancel_translation(
    translation_id: int,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """取消翻译任务"""
    try:
        translation = await TranslationService.get_translation_by_id(
            db, translation_id, current_user
        )
        
        if not translation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Translation not found"
            )
        
        if translation.status not in ["pending", "in_progress"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot cancel completed or failed translation"
            )
        
        # 更新状态为已取消
        translation.status = "cancelled"
        await db.commit()
        
        logger.info(f"Translation cancelled: {translation_id} by user {current_user.username}")
        
        return {"message": "Translation cancelled successfully"}
    except Exception as e:
        logger.error(f"Cancel translation error: {e}")
        raise


@router.post("/process")
async def process_translation(
    request: dict,
    db: AsyncSession = Depends(get_async_db)
):
    """
    处理明道云触发的翻译请求

    请求格式:
    {
        "row_id": "明道云记录ID",
        "source_language": "源语言代码",
        "target_language": "目标语言代码",
        "translation_settings": {
            "paragraph": {...},
            "table": {...},
            "header": {...},
            "enable_paragraph": true,
            "enable_table": true,
            "enable_header": true
        }
    }
    """
    try:
        row_id = request.get('row_id')
        source_language = request.get('source_language', 'zh')
        target_language = request.get('target_language', 'en')
        translation_settings = request.get('translation_settings')

        if not row_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="row_id is required"
            )

        logger.info(f"Processing translation request for row_id: {row_id}, {source_language} -> {target_language}")
        if translation_settings:
            logger.info(f"Translation settings provided: {translation_settings}")

        # 调用翻译服务处理明道云记录
        result = await TranslationService.process_mingdao_translation(
            db, row_id, source_language, target_language, translation_settings
        )

        return {
            "success": True,
            "message": "Translation processing started",
            "row_id": row_id,
            "result": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Process translation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Translation processing failed: {str(e)}"
        )


@router.post("/detect-language")
async def detect_language(
    text: str = Query(..., max_length=1000, description="要检测语言的文本"),
    current_user = Depends(get_current_active_user)
):
    """检测文本语言"""
    try:
        result = await azure_translator.detect_language(text)
        return {
            "detected_language": result.get('language'),
            "confidence": result.get('score', 0.0),
            "is_translation_supported": result.get('isTranslationSupported', False),
            "is_transliteration_supported": result.get('isTransliterationSupported', False)
        }
    except Exception as e:
        logger.error(f"Detect language error: {e}")
        raise


@router.get("/{translation_id}/download")
async def download_translation(
    translation_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user = Depends(get_current_active_user)
):
    """下载翻译后的文档"""
    try:
        # 获取翻译记录
        translation = await TranslationService.get_translation_by_id(db, translation_id, current_user)
        if not translation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Translation not found"
            )

        if translation.status.upper() != "COMPLETED":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Translation is not completed yet"
            )

        # 获取原文件信息
        result = await db.execute(
            select(UploadedFile).where(UploadedFile.id == translation.file_id)
        )
        original_file = result.scalar_one_or_none()

        if not original_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Original file not found"
            )

        # 生成翻译后的文档内容
        translated_content = _generate_translated_document(
            translation,
            original_file.original_filename
        )

        # 生成文件名（处理中文字符）
        original_name = original_file.original_filename
        name_without_ext = original_name.rsplit('.', 1)[0] if '.' in original_name else original_name

        # 使用安全的文件名，避免中文字符编码问题
        safe_filename = f"translated_document_{translation.id}_{translation.source_language}_to_{translation.target_language}.txt"

        # 对文件名进行 URL 编码以支持中文
        import urllib.parse
        encoded_filename = urllib.parse.quote(safe_filename.encode('utf-8'))

        return StreamingResponse(
            io.BytesIO(translated_content.encode('utf-8')),
            media_type="text/plain; charset=utf-8",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}",
                "Content-Type": "text/plain; charset=utf-8"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Download translation error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{translation_id}/download/docx")
async def download_translation_docx(
    translation_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user = Depends(get_current_active_user)
):
    """下载双语 Word 文档（原文+译文）"""
    try:
        # 获取翻译记录
        result = await db.execute(
            select(TranslationHistory).where(
                TranslationHistory.id == translation_id,
                TranslationHistory.user_id == current_user.id
            )
        )
        translation = result.scalar_one_or_none()

        if not translation:
            raise HTTPException(status_code=404, detail="Translation not found")

        if translation.status.upper() != "COMPLETED":
            raise HTTPException(status_code=400, detail="Translation is not completed yet")

        # 获取原始文件
        file_result = await db.execute(
            select(UploadedFile).where(UploadedFile.id == translation.file_id)
        )
        original_file = file_result.scalar_one_or_none()

        if not original_file:
            raise HTTPException(status_code=404, detail="Original file not found")

        # 检查原始文件是否存在
        if not os.path.exists(original_file.file_path):
            raise HTTPException(status_code=404, detail="Original file not found on disk")

        # 检查文件格式
        if not original_file.original_filename.lower().endswith(('.docx', '.doc')):
            raise HTTPException(status_code=400, detail="Only Word documents (.docx, .doc) are supported")

        # 解析翻译结果
        translation_dict = docx_processor.parse_translation_result(translation.translated_text)

        if not translation_dict:
            raise HTTPException(status_code=400, detail="No translation data found")

        # 创建双语文档
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            output_path = temp_file.name

        try:
            bilingual_doc_path = docx_processor.create_bilingual_document(
                original_file.file_path,
                translation_dict,
                output_path
            )

            # 生成文件名
            original_name = original_file.original_filename
            name_without_ext = original_name.rsplit('.', 1)[0] if '.' in original_name else original_name
            safe_filename = f"{name_without_ext}_bilingual_{translation.source_language}_to_{translation.target_language}.docx"

            # 对文件名进行 URL 编码
            import urllib.parse
            encoded_filename = urllib.parse.quote(safe_filename.encode('utf-8'))

            # 读取文件内容
            with open(bilingual_doc_path, 'rb') as f:
                file_content = f.read()

            # 清理临时文件
            try:
                os.unlink(bilingual_doc_path)
            except:
                pass

            return StreamingResponse(
                io.BytesIO(file_content),
                media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                headers={
                    "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}",
                    "Content-Type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                }
            )

        except Exception as e:
            # 清理临时文件
            try:
                os.unlink(output_path)
            except:
                pass
            raise

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Download docx translation error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

    except Exception as e:
        logger.error(f"Download translation error: {e}")
        raise


@router.get("/{translation_id}/preview/html")
async def preview_translation_html(
    translation_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user = Depends(get_current_active_user)
):
    """生成翻译结果的HTML预览"""
    try:
        # 获取翻译记录
        result = await db.execute(
            select(TranslationHistory).where(
                TranslationHistory.id == translation_id,
                TranslationHistory.user_id == current_user.id
            )
        )
        translation = result.scalar_one_or_none()

        if not translation:
            raise HTTPException(status_code=404, detail="Translation not found")

        if translation.status.upper() != "COMPLETED":
            raise HTTPException(status_code=400, detail="Translation is not completed yet")

        # 获取原始文件信息
        file_result = await db.execute(
            select(UploadedFile).where(UploadedFile.id == translation.file_id)
        )
        original_file = file_result.scalar_one_or_none()

        if not original_file:
            raise HTTPException(status_code=404, detail="Original file not found")

        # 生成HTML预览内容
        html_content = _generate_html_preview(translation, original_file)

        return Response(
            content=html_content,
            media_type="text/html; charset=utf-8"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Generate HTML preview error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


def _generate_html_preview(translation: TranslationHistory, uploaded_file: UploadedFile) -> str:
    """生成HTML预览内容"""
    import html

    # 语言名称映射
    lang_names = {
        'zh': '中文', 'en': '英语', 'ja': '日语', 'ko': '韩语',
        'fr': '法语', 'de': '德语', 'es': '西班牙语'
    }

    source_lang_name = lang_names.get(translation.source_language, translation.source_language)
    target_lang_name = lang_names.get(translation.target_language, translation.target_language)

    # 计算统计信息
    original_chars = len(translation.original_text) if translation.original_text else 0
    translated_chars = len(translation.translated_text) if translation.translated_text else 0
    confidence = translation.confidence_score or 0

    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译预览 - {html.escape(uploaded_file.original_filename)}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}

        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}

        .header h1 {{
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }}

        .meta-info {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }}

        .meta-item {{
            text-align: center;
        }}

        .meta-label {{
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }}

        .meta-value {{
            font-size: 16px;
            font-weight: 600;
            color: #495057;
        }}

        .content {{
            padding: 30px;
        }}

        .section {{
            margin-bottom: 40px;
        }}

        .section-title {{
            font-size: 20px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }}

        .text-content {{
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            white-space: pre-wrap;
            font-size: 14px;
            line-height: 1.8;
            max-height: 400px;
            overflow-y: auto;
        }}

        .dual-view {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }}

        .original {{
            border-left-color: #28a745;
        }}

        .translated {{
            border-left-color: #007bff;
        }}

        .stats {{
            display: flex;
            justify-content: space-around;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }}

        .stat-item {{
            text-align: center;
        }}

        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            display: block;
        }}

        .stat-label {{
            font-size: 12px;
            opacity: 0.8;
        }}

        @media (max-width: 768px) {{
            .dual-view {{
                grid-template-columns: 1fr;
            }}

            .meta-info {{
                grid-template-columns: 1fr;
            }}
        }}

        .print-btn {{
            position: fixed;
            top: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }}

        .print-btn:hover {{
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }}

        @media print {{
            .print-btn {{ display: none; }}
            body {{ background: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <button class="print-btn" onclick="window.print()">🖨️ 打印</button>

    <div class="container">
        <div class="header">
            <h1>📄 翻译文档预览</h1>
            <p>{html.escape(uploaded_file.original_filename)}</p>
        </div>

        <div class="meta-info">
            <div class="meta-item">
                <div class="meta-label">翻译方向</div>
                <div class="meta-value">{source_lang_name} → {target_lang_name}</div>
            </div>
            <div class="meta-item">
                <div class="meta-label">字符数</div>
                <div class="meta-value">{translated_chars:,}</div>
            </div>
            <div class="meta-item">
                <div class="meta-label">置信度</div>
                <div class="meta-value">{int(confidence * 100)}%</div>
            </div>
            <div class="meta-item">
                <div class="meta-label">完成时间</div>
                <div class="meta-value">{translation.completed_at or 'N/A'}</div>
            </div>
        </div>

        <div class="content">
"""

    # 如果有原文，显示双栏对比
    if translation.original_text and translation.original_text.strip():
        html_content += f"""
            <div class="dual-view">
                <div class="section">
                    <div class="section-title">🔤 原文内容</div>
                    <div class="text-content original">{html.escape(translation.original_text)}</div>
                </div>

                <div class="section">
                    <div class="section-title">🌍 翻译结果</div>
                    <div class="text-content translated">{html.escape(translation.translated_text or '翻译内容为空')}</div>
                </div>
            </div>
"""
    else:
        # 只显示翻译结果
        html_content += f"""
            <div class="section">
                <div class="section-title">🌍 翻译结果</div>
                <div class="text-content">{html.escape(translation.translated_text or '翻译内容为空')}</div>
            </div>
"""

    # 添加统计信息
    html_content += f"""
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">{original_chars:,}</span>
                    <span class="stat-label">原文字符</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{translated_chars:,}</span>
                    <span class="stat-label">译文字符</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{int(confidence * 100)}%</span>
                    <span class="stat-label">翻译置信度</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
"""

    return html_content


def _generate_translated_document(translation, original_filename: str) -> str:
    """生成翻译后的文档内容"""

    # 文档头部信息
    header = f"""翻译文档 / Translated Document
{'=' * 60}
原文件名 / Original File: {original_filename}
翻译时间 / Translation Time: {translation.completed_at or translation.created_at}
源语言 / Source Language: {translation.source_language}
目标语言 / Target Language: {translation.target_language}
翻译字符数 / Translated Characters: {translation.translated_characters}
置信度 / Confidence Score: {translation.confidence_score or 'N/A'}
{'=' * 60}

"""

    # 翻译内容
    content = translation.translated_text or "翻译内容为空 / Translation content is empty"

    # 文档尾部信息
    footer = f"""

{'=' * 60}
翻译完成 / Translation Completed
生成时间 / Generated Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'=' * 60}
"""

    return header + content + footer
