.onlyoffice-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.onlyoffice-preview-container {
  background-color: white;
  border-radius: 12px;
  width: 95%;
  height: 95%;
  max-width: 1400px;
  max-height: 900px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.onlyoffice-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #dee2e6;
  background-color: #f8f9fa;
  flex-shrink: 0;
}

.preview-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  word-break: break-all;
}

.preview-info {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.close-btn {
  padding: 8px 12px;
  font-size: 16px;
  font-weight: bold;
  min-width: auto;
}

.onlyoffice-preview-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}

.onlyoffice-editor-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* OnlyOffice 编辑器样式覆盖 */
.onlyoffice-editor-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .onlyoffice-preview-modal {
    padding: 10px;
  }
  
  .onlyoffice-preview-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
  }
  
  .onlyoffice-preview-header {
    padding: 12px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .preview-title h3 {
    font-size: 16px;
  }
  
  .close-btn {
    align-self: flex-end;
  }
}

/* 加载状态 */
.onlyoffice-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
}

.onlyoffice-loading .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.onlyoffice-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #dc3545;
  text-align: center;
  padding: 40px;
}

.onlyoffice-error h4 {
  margin: 0 0 12px 0;
  font-size: 20px;
  color: #495057;
}

.onlyoffice-error p {
  margin: 0 0 20px 0;
  font-size: 16px;
  max-width: 400px;
  line-height: 1.5;
}
