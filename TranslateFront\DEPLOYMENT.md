# SMILE TRANS - 生产环境部署指南

🔥 **解决服务器断开连接问题的完整解决方案**

## 🎯 快速部署 (推荐方案)

### 方案1: PM2 一键部署

```bash
# 1. 给脚本执行权限
chmod +x *.sh

# 2. 安装 PM2
./install-pm2.sh

# 3. 一键启动服务
./start-production.sh

# 4. 查看状态
pm2 status
```

### 方案2: nohup 简单部署

```bash
# 启动服务
./start-nohup.sh

# 查看日志
tail -f app.log

# 停止服务
./stop-nohup.sh
```

### 方案3: systemd 系统服务

```bash
# 设置系统服务
./setup-systemd.sh

# 启动服务
sudo systemctl start translate-front
```

**✅ 部署完成后，你的服务将在断开SSH连接后继续运行！**

---

## 📦 构建产物

构建成功！生成的文件位于 `dist/` 目录：

```
dist/
├── index.html                 # 主页面
├── assets/
    ├── index-a9480506.css    # 样式文件
    └── index-c77828f4.js     # JavaScript 文件
```

## 🚀 部署方式

### 方式一：Node.js 服务器（推荐）

使用内置的 Express 代理服务器，解决 CORS 问题：

```bash
# 安装依赖
npm install

# 构建并启动生产服务器
npm run serve

# 或者分步执行
npm run build
npm run start
```

服务器将运行在 `http://localhost:3000`，自动处理：
- 静态文件服务
- API 代理到明道云
- SPA 路由支持

### 方式二：静态文件服务器（需要额外配置）

⚠️ **注意**: 静态部署需要服务器配置 API 代理，否则会有 CORS 问题。

将 `dist/` 目录中的所有文件上传到你的 Web 服务器：

```bash
# 复制构建产物到服务器
scp -r dist/* user@your-server:/var/www/html/translate-front/
```

### 方式三：Nginx 配置（静态部署 + 代理）

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /var/www/html/translate-front;
    index index.html;
    
    # 处理 SPA 路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API 代理（如果需要）
    location /api/ {
        proxy_pass https://dmit.duoningbio.com;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 方式四：Apache 配置（静态部署 + 代理）

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/html/translate-front
    
    # 处理 SPA 路由
    <Directory "/var/www/html/translate-front">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
</VirtualHost>
```

## ⚙️ 配置说明

### API 配置

系统已配置为直接连接生产环境 API：
- **API 地址**: `https://dmit.duoningbio.com/api/v2/open`
- **工作表 ID**: `fywd`

### 功能特性

✅ **已启用功能**：
- 文档列表展示
- 文件上传
- Office Online 预览
- 批量下载（支持选择原文档/翻译文档/全部）
- 搜索和筛选

❌ **已禁用功能**：
- OnlyOffice 预览（因配置问题暂时禁用）
- 调试工具和系统状态

## 🔧 本地开发

如需继续开发：

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📝 注意事项

1. **CORS 配置**:
   - ⚠️ **重要**: API 服务器当前不支持 OPTIONS 预检请求，会导致 CORS 错误
   - 系统已实现备用方案：POST 失败时自动尝试 GET 请求
   - 建议在 API 服务器上配置 CORS 支持：
     ```nginx
     add_header Access-Control-Allow-Origin "*";
     add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
     add_header Access-Control-Allow-Headers "Content-Type, Accept";
     ```

2. **HTTPS**: 建议在生产环境使用 HTTPS

3. **缓存策略**: 静态资源已配置长期缓存

4. **错误处理**: 系统包含完整的错误处理和用户提示

5. **API 兼容性**:
   - 当前使用 POST 请求到 `/worksheet/getFilterRows`
   - 如果 CORS 失败，会自动降级到 GET 请求到 `/worksheet/rows`

## 🎯 下一步计划

- [ ] 修复 OnlyOffice 预览功能
- [ ] 添加用户认证
- [ ] 优化移动端体验
- [ ] 添加更多文件格式支持

---

**构建时间**: 2025-07-22  
**版本**: v1.0.0  
**状态**: ✅ 生产就绪
