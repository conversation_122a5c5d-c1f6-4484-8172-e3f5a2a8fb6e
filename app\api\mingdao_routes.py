"""
基于明道云的API路由
"""
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
import jwt
import tempfile
import os
from datetime import datetime, timedelta

from app.services.mingdao_full_service import mingdao_full_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/mingdao", tags=["明道云API"])
security = HTTPBearer()

# JWT配置
JWT_SECRET = "your-secret-key-here"  # 在生产环境中应该使用环境变量
JWT_ALGORITHM = "HS256"

def create_access_token(user_data: Dict[str, Any]) -> str:
    """创建访问令牌"""
    expire = datetime.utcnow() + timedelta(hours=24)
    to_encode = {
        "user_id": user_data["id"],
        "username": user_data["username"],
        "exp": expire
    }
    return jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """获取当前用户"""
    try:
        payload = jwt.decode(credentials.credentials, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        user_id = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="无效的令牌")
        
        user = await mingdao_full_service.get_user_by_id(user_id)
        if user is None:
            raise HTTPException(status_code=401, detail="用户不存在")
        
        return user
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="无效的令牌")

# ==================== 用户认证 ====================

@router.post("/auth/register")
async def register(
    username: str = Form(...),
    email: str = Form(...),
    password: str = Form(...),
    full_name: str = Form("")
):
    """用户注册"""
    try:
        result = await mingdao_full_service.create_user(username, email, password, full_name)
        
        if result["success"]:
            return {
                "success": True,
                "message": "注册成功",
                "user_id": result["user_id"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])
            
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/auth/login")
async def login(
    username: str = Form(...),
    password: str = Form(...)
):
    """用户登录"""
    try:
        result = await mingdao_full_service.authenticate_user(username, password)
        
        if result["success"]:
            user = result["user"]
            access_token = create_access_token(user)
            
            return {
                "success": True,
                "message": "登录成功",
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "id": user["id"],
                    "username": user["username"],
                    "email": user["email"],
                    "full_name": user["full_name"],
                    "user_type": user["user_type"],
                    "balance": user["balance"],
                    "total_quota": user["total_quota"],
                    "used_quota": user["used_quota"],
                    "monthly_quota": user["monthly_quota"],
                    "monthly_used": user["monthly_used"]
                }
            }
        else:
            raise HTTPException(status_code=401, detail=result["message"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/auth/profile")
async def get_profile(current_user: Dict = Depends(get_current_user)):
    """获取用户资料"""
    return {
        "success": True,
        "user": {
            "id": current_user["id"],
            "username": current_user["username"],
            "email": current_user["email"],
            "full_name": current_user["full_name"],
            "user_type": current_user["user_type"],
            "balance": current_user["balance"],
            "total_quota": current_user["total_quota"],
            "used_quota": current_user["used_quota"],
            "monthly_quota": current_user["monthly_quota"],
            "monthly_used": current_user["monthly_used"],
            "monthly_expire_date": current_user["monthly_expire_date"],
            "last_login": current_user["last_login"],
            "created_at": current_user["created_at"]
        }
    }

# ==================== 翻译管理 ====================

@router.post("/translate/upload")
async def upload_and_translate(
    file: UploadFile = File(...),
    current_user: Dict = Depends(get_current_user)
):
    """上传文件并开始翻译"""
    try:
        # 检查文件类型
        if not file.filename.endswith(('.docx', '.doc')):
            raise HTTPException(status_code=400, detail="只支持Word文档格式")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # 估算字符数（简单估算，实际应该解析文档）
            char_count = len(content) // 2  # 粗略估算
            
            # 创建翻译记录
            result = await mingdao_full_service.create_translation(
                current_user["id"], 
                temp_file_path, 
                file.filename, 
                char_count
            )
            
            if result["success"]:
                # 异步开始翻译任务
                translation_id = result["translation_id"]
                
                # 这里应该启动后台翻译任务
                # 暂时返回成功响应
                return {
                    "success": True,
                    "message": "文件上传成功，翻译任务已创建",
                    "translation_id": translation_id,
                    "cost": result["cost"],
                    "estimated_characters": char_count
                }
            else:
                raise HTTPException(status_code=400, detail=result["message"])
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传翻译失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/translate/history")
async def get_translation_history(
    page: int = 1,
    page_size: int = 20,
    current_user: Dict = Depends(get_current_user)
):
    """获取翻译历史"""
    try:
        result = await mingdao_full_service.get_translation_history(
            current_user["id"], page, page_size
        )
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"获取翻译历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/translate/{translation_id}/progress")
async def get_translation_progress(
    translation_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """获取翻译进度"""
    try:
        # 这里应该查询具体的翻译记录
        # 暂时返回模拟数据
        return {
            "success": True,
            "translation_id": translation_id,
            "progress": 100,
            "status": "COMPLETED",
            "message": "翻译完成"
        }
        
    except Exception as e:
        logger.error(f"获取翻译进度失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 健康检查 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "mingdao-translation-api",
        "timestamp": datetime.now().isoformat()
    }
