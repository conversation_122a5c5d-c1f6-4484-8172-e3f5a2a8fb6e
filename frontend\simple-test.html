<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .online {
            background: #d4edda;
            color: #155724;
        }
        
        .offline {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 明道云API简单测试</h1>
        
        <div id="status" class="status offline">代理服务器状态：检测中...</div>
        
        <h2>🔗 代理服务器测试</h2>
        <button onclick="testProxy()">测试代理服务器</button>
        <button onclick="testPricing()">测试收费计算</button>
        <button onclick="testMingdaoConnection()">测试明道云连接</button>
        
        <h2>💰 收费计算</h2>
        <input type="number" id="charInput" placeholder="输入字符数" value="8000" style="padding: 8px; margin: 5px;">
        <button onclick="calculateCost()">计算费用</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const PROXY_URL = 'http://localhost:9000';

        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }

        function updateStatus(isOnline, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${isOnline ? 'online' : 'offline'}`;
            statusDiv.textContent = `代理服务器状态：${message}`;
        }

        async function testProxy() {
            try {
                const response = await fetch(`${PROXY_URL}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    updateStatus(true, '在线');
                    showResult(data);
                } else {
                    updateStatus(false, '响应异常');
                    showResult(data, true);
                }
            } catch (error) {
                updateStatus(false, '离线');
                showResult({ error: error.message }, true);
            }
        }

        async function testPricing() {
            try {
                const response = await fetch(`${PROXY_URL}/api/mingdao/pricing`);
                const data = await response.json();
                showResult(data, !response.ok);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        async function calculateCost() {
            const charCount = parseInt(document.getElementById('charInput').value);
            
            if (!charCount || charCount <= 0) {
                showResult({ error: '请输入有效的字符数' }, true);
                return;
            }

            try {
                const response = await fetch(`${PROXY_URL}/api/mingdao/calculate-cost`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ charCount })
                });

                const data = await response.json();
                showResult(data, !response.ok);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        async function testMingdaoConnection() {
            try {
                const response = await fetch(`${PROXY_URL}/api/mingdao/test`);
                const data = await response.json();
                showResult(data, !response.ok);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        // 页面加载时自动测试
        window.addEventListener('load', function() {
            console.log('页面已加载，开始测试...');
            testProxy();
        });
    </script>
</body>
</html>
