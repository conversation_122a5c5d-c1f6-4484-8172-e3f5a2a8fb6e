# 🚀 简单部署指南（跳过 Docker）

## 📋 推荐部署方式

由于 Docker 镜像下载可能有网络问题，推荐使用 Node.js 直接部署：

### 🎯 方式一：本地测试部署

```bash
# 1. 确保已安装 Node.js 18+
node --version

# 2. 安装依赖
npm install

# 3. 构建项目
npm run build

# 4. 启动生产服务器
npm run start
```

访问：`http://localhost:3000`

### 🌐 方式二：服务器部署

#### 1. 上传代码到服务器
```bash
# 方式一：使用 git
git clone https://gitee.com/he-yiming/TranslateFront.git
cd TranslateFront

# 方式二：使用 scp 上传
scp -r ./TranslateFront user@your-server:/var/www/
```

#### 2. 服务器上执行
```bash
# 进入项目目录
cd /var/www/TranslateFront

# 安装依赖
npm install

# 构建项目
npm run build

# 启动服务
npm run start
```

#### 3. 使用 PM2 管理进程（推荐）
```bash
# 安装 PM2
sudo npm install -g pm2

# 启动应用
pm2 start server.cjs --name "translate-front"

# 设置开机自启
pm2 startup
pm2 save

# 查看状态
pm2 status
pm2 logs translate-front
```

### 🔧 配置域名访问（可选）

#### Nginx 反向代理
```bash
# 安装 Nginx
sudo apt install nginx

# 创建配置文件
sudo nano /etc/nginx/sites-available/translate-front
```

配置内容：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# 启用配置
sudo ln -s /etc/nginx/sites-available/translate-front /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🎉 部署完成

现在你的翻译文档管理系统已经成功部署！

### ✅ 功能验证
- [ ] 页面正常加载
- [ ] 文档列表能获取数据
- [ ] 文件上传功能正常
- [ ] 文档预览功能正常
- [ ] 批量下载功能正常

### 📞 如果遇到问题

1. **端口被占用**：
   ```bash
   sudo lsof -i :3000
   sudo kill -9 <PID>
   ```

2. **权限问题**：
   ```bash
   sudo chown -R $USER:$USER /var/www/TranslateFront
   ```

3. **防火墙设置**：
   ```bash
   sudo ufw allow 3000
   ```

4. **查看日志**：
   ```bash
   pm2 logs translate-front
   # 或
   tail -f app.log
   ```

## 🔄 Docker 部署（网络好的时候）

如果你的网络环境允许，可以稍后尝试 Docker 部署：

```bash
# 配置 Docker 镜像加速器后
docker build -t translate-front .
docker run -d -p 3000:3000 --name translate-front translate-front
```

---

**推荐先使用 Node.js 方式部署，稳定可靠！** 🚀
