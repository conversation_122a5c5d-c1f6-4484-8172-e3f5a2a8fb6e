"""
管理员服务
"""
import os
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_

from app.models.user import User
from app.models.file import UploadedFile
from app.models.translation import TranslationHistory, TranslationStatus
from app.models.term import Term
from app.schemas.admin import SystemStats, UserStats, SystemHealth, ErrorStats
from app.config import settings
from app.utils.logger import logger


class AdminService:
    """管理员服务类"""
    
    @staticmethod
    async def get_system_stats(db: AsyncSession) -> SystemStats:
        """获取系统统计信息"""
        try:
            # 用户统计
            total_users_result = await db.execute(select(func.count(User.id)))
            total_users = total_users_result.scalar()
            
            active_users_result = await db.execute(
                select(func.count(User.id)).where(User.is_active == True)
            )
            active_users = active_users_result.scalar()
            
            # 文件统计
            total_files_result = await db.execute(select(func.count(UploadedFile.id)))
            total_files = total_files_result.scalar()
            
            # 翻译统计
            total_translations_result = await db.execute(select(func.count(TranslationHistory.id)))
            total_translations = total_translations_result.scalar()
            
            completed_translations_result = await db.execute(
                select(func.count(TranslationHistory.id)).where(
                    TranslationHistory.status == TranslationStatus.COMPLETED
                )
            )
            completed_translations = completed_translations_result.scalar()
            
            failed_translations_result = await db.execute(
                select(func.count(TranslationHistory.id)).where(
                    TranslationHistory.status == TranslationStatus.FAILED
                )
            )
            failed_translations = failed_translations_result.scalar()
            
            # 术语统计
            total_terms_result = await db.execute(select(func.count(Term.id)))
            total_terms = total_terms_result.scalar()
            
            # 系统资源统计（简化版本）
            disk_usage = AdminService._get_disk_usage()
            memory_usage = AdminService._get_memory_usage()
            cpu_usage = 0.0  # 简化版本，不使用 psutil
            
            return SystemStats(
                total_users=total_users or 0,
                active_users=active_users or 0,
                total_files=total_files or 0,
                total_translations=total_translations or 0,
                completed_translations=completed_translations or 0,
                failed_translations=failed_translations or 0,
                total_terms=total_terms or 0,
                disk_usage=disk_usage,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage
            )
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            raise
    
    @staticmethod
    def _get_disk_usage() -> Dict[str, Any]:
        """获取磁盘使用情况（简化版本）"""
        try:
            # 简化版本，使用 os.statvfs 或返回模拟数据
            return {
                "total": 1000000000,  # 1GB 模拟数据
                "used": 500000000,    # 500MB 模拟数据
                "free": 500000000,    # 500MB 模拟数据
                "percent": 50.0
            }
        except Exception:
            return {"total": 0, "used": 0, "free": 0, "percent": 0}

    @staticmethod
    def _get_memory_usage() -> Dict[str, Any]:
        """获取内存使用情况（简化版本）"""
        try:
            # 简化版本，返回模拟数据
            return {
                "total": 8000000000,   # 8GB 模拟数据
                "used": 4000000000,    # 4GB 模拟数据
                "available": 4000000000, # 4GB 模拟数据
                "percent": 50.0
            }
        except Exception:
            return {"total": 0, "used": 0, "available": 0, "percent": 0}
    
    @staticmethod
    async def get_user_stats(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 50
    ) -> tuple[List[UserStats], int]:
        """获取用户统计信息"""
        try:
            # 获取总数
            count_result = await db.execute(select(func.count(User.id)))
            total = count_result.scalar()
            
            # 获取用户列表
            users_result = await db.execute(
                select(User)
                .order_by(User.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            users = users_result.scalars().all()
            
            user_stats = []
            for user in users:
                # 获取用户文件数量
                files_count_result = await db.execute(
                    select(func.count(UploadedFile.id)).where(UploadedFile.user_id == user.id)
                )
                files_count = files_count_result.scalar() or 0
                
                # 获取用户翻译数量
                translations_count_result = await db.execute(
                    select(func.count(TranslationHistory.id)).where(TranslationHistory.user_id == user.id)
                )
                translations_count = translations_count_result.scalar() or 0
                
                user_stats.append(UserStats(
                    id=user.id,
                    username=user.username,
                    email=user.email,
                    role=user.role.value,
                    is_active=user.is_active,
                    translation_quota=user.translation_quota,
                    used_quota=user.used_quota,
                    files_count=files_count,
                    translations_count=translations_count,
                    last_login=user.last_login.isoformat() if user.last_login else None,
                    created_at=user.created_at.isoformat() if user.created_at else None
                ))
            
            return user_stats, total or 0
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            raise
    
    @staticmethod
    async def get_system_health() -> SystemHealth:
        """获取系统健康状态"""
        try:
            # 检查数据库状态
            database_status = "healthy"  # 简化实现，实际应该测试数据库连接
            
            # 检查Redis状态 (如果配置了)
            redis_status = None
            if settings.redis_url:
                redis_status = "healthy"  # 简化实现
            
            # 系统资源检查（简化版本）
            disk_space = AdminService._get_disk_usage()
            memory_usage = AdminService._get_memory_usage()
            cpu_usage = 0.0  # 简化版本

            # 确定整体状态
            status = "healthy"
            if disk_space["percent"] > 90 or memory_usage["percent"] > 90:
                status = "warning"
            if disk_space["percent"] > 95 or memory_usage["percent"] > 95:
                status = "critical"

            # 获取活跃连接数 (简化实现)
            active_connections = 10  # 模拟数据

            # 获取系统运行时间（简化版本）
            uptime = 3600  # 1小时模拟数据
            
            return SystemHealth(
                status=status,
                database_status=database_status,
                redis_status=redis_status,
                disk_space=disk_space,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                active_connections=active_connections,
                uptime=uptime,
                last_check=datetime.utcnow().isoformat()
            )
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return SystemHealth(
                status="critical",
                database_status="unknown",
                redis_status=None,
                disk_space={"total": 0, "used": 0, "free": 0, "percent": 0},
                memory_usage={"total": 0, "used": 0, "available": 0, "percent": 0},
                cpu_usage=0,
                active_connections=0,
                uptime=0,
                last_check=datetime.utcnow().isoformat()
            )
    
    @staticmethod
    def read_log_file(
        log_file: str = None,
        lines: int = 100,
        level: str = None
    ) -> List[Dict[str, Any]]:
        """读取日志文件"""
        try:
            log_file = log_file or settings.log_file
            
            if not os.path.exists(log_file):
                return []
            
            logs = []
            with open(log_file, 'r', encoding='utf-8') as f:
                # 读取最后N行
                file_lines = f.readlines()
                recent_lines = file_lines[-lines:] if len(file_lines) > lines else file_lines
                
                for line in recent_lines:
                    try:
                        # 解析日志行 (假设格式: timestamp | level | module:function:line - message)
                        parts = line.strip().split(' | ')
                        if len(parts) >= 4:
                            timestamp = parts[0]
                            log_level = parts[1].strip()
                            location = parts[2]
                            message = ' | '.join(parts[3:])
                            
                            # 解析位置信息
                            location_parts = location.split(':')
                            module = location_parts[0] if len(location_parts) > 0 else ""
                            function = location_parts[1] if len(location_parts) > 1 else ""
                            line_num = location_parts[2] if len(location_parts) > 2 else "0"
                            
                            # 过滤日志级别
                            if level and log_level.lower() != level.lower():
                                continue
                            
                            logs.append({
                                "timestamp": timestamp,
                                "level": log_level,
                                "message": message,
                                "module": module,
                                "function": function,
                                "line": int(line_num) if line_num.isdigit() else 0
                            })
                    except Exception:
                        # 跳过无法解析的行
                        continue
            
            return logs
        except Exception as e:
            logger.error(f"Error reading log file: {e}")
            return []
    
    @staticmethod
    async def cleanup_old_data(db: AsyncSession, days: int = 30) -> Dict[str, int]:
        """清理旧数据"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            # 清理旧的翻译记录 (保留完成的翻译)
            old_translations_result = await db.execute(
                select(TranslationHistory).where(
                    and_(
                        TranslationHistory.created_at < cutoff_date,
                        TranslationHistory.status.in_([
                            TranslationStatus.FAILED,
                            TranslationStatus.CANCELLED
                        ])
                    )
                )
            )
            old_translations = old_translations_result.scalars().all()
            
            for translation in old_translations:
                await db.delete(translation)
            
            # 清理孤立的文件记录 (没有关联翻译的文件)
            orphaned_files_result = await db.execute(
                select(UploadedFile).where(
                    and_(
                        UploadedFile.created_at < cutoff_date,
                        ~UploadedFile.translation_histories.any()
                    )
                )
            )
            orphaned_files = orphaned_files_result.scalars().all()
            
            for file_record in orphaned_files:
                # 删除物理文件
                if os.path.exists(file_record.file_path):
                    os.remove(file_record.file_path)
                await db.delete(file_record)
            
            await db.commit()
            
            return {
                "deleted_translations": len(old_translations),
                "deleted_files": len(orphaned_files)
            }
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            await db.rollback()
            raise
