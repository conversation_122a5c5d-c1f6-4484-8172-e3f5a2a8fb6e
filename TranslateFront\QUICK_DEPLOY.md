# 🚀 快速部署指南

## 📋 部署前准备

### 1. 服务器要求
- **操作系统**: Linux (Ubuntu 18+/CentOS 7+) 或 Windows Server
- **内存**: 最少 1GB，推荐 2GB+
- **存储**: 最少 5GB 可用空间
- **网络**: 能访问外网（需要下载依赖）

### 2. 必需软件
- **Node.js**: 18.0+ 版本
- **npm**: 8.0+ 版本
- **Git**: 用于代码拉取（可选）

## 🎯 一键部署（推荐）

### Linux/macOS 服务器
```bash
# 1. 上传项目文件到服务器
scp -r ./TranslateFront user@your-server:/var/www/

# 2. 登录服务器
ssh user@your-server

# 3. 进入项目目录
cd /var/www/TranslateFront

# 4. 运行部署脚本
./deploy.sh
```

### Windows 服务器
```cmd
# 1. 上传项目文件到服务器
# 2. 进入项目目录
cd C:\inetpub\wwwroot\TranslateFront

# 3. 运行部署脚本
deploy.bat
```

## 🐳 Docker 部署（推荐）

### 使用 Docker Compose
```bash
# 1. 确保安装了 Docker 和 Docker Compose
docker --version
docker-compose --version

# 2. 启动服务
docker-compose up -d

# 3. 查看状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f
```

### 使用 Docker 命令
```bash
# 1. 构建镜像
docker build -t translate-front .

# 2. 运行容器
docker run -d -p 3000:3000 --name translate-front translate-front

# 3. 查看日志
docker logs translate-front
```

## 🔧 手动部署

### 1. 安装 Node.js
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

### 2. 部署应用
```bash
# 进入项目目录
cd /path/to/TranslateFront

# 安装依赖
npm install

# 构建项目
npm run build

# 启动服务
npm run start
```

### 3. 使用 PM2 管理进程
```bash
# 安装 PM2
sudo npm install -g pm2

# 启动应用
pm2 start server.cjs --name "translate-front"

# 设置开机自启
pm2 startup
pm2 save
```

## 🌐 配置反向代理

### Nginx 配置
```bash
# 安装 Nginx
sudo apt install nginx

# 创建配置文件
sudo nano /etc/nginx/sites-available/translate-front
```

配置内容：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# 启用配置
sudo ln -s /etc/nginx/sites-available/translate-front /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔍 部署验证

访问 `http://your-server:3000` 或你的域名，检查：

- [ ] 页面正常加载
- [ ] 文档列表能获取数据
- [ ] 文件上传功能正常
- [ ] 文档预览功能正常
- [ ] 批量下载功能正常

## 🚨 故障排除

### 1. 端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep :3000
# 或
sudo lsof -i :3000

# 杀死进程
sudo kill -9 <PID>
```

### 2. 权限问题
```bash
# 修改文件权限
sudo chown -R $USER:$USER /path/to/TranslateFront
chmod +x deploy.sh
```

### 3. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 3000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

### 4. 查看日志
```bash
# PM2 日志
pm2 logs translate-front

# Docker 日志
docker logs translate-front

# 应用日志
tail -f app.log
```

## 📞 技术支持

如果遇到问题，请检查：
1. Node.js 版本是否正确
2. 网络连接是否正常
3. 防火墙设置是否正确
4. 日志文件中的错误信息

---

**部署完成后，你的翻译文档管理系统就可以正常使用了！** 🎉
