.translation-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.translation-settings {
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  position: relative;
}

.settings-header {
  text-align: center;
  padding: 30px 30px 20px;
  border-bottom: 1px solid #e1e8ed;
  position: relative;
}

.settings-header h3 {
  color: #2c3e50;
  margin: 0 0 8px 0;
  font-size: 24px;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  color: #7f8c8d;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.settings-description {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.global-switches {
  padding: 20px 30px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.switch-group {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.switch-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.switch-item:hover {
  background: rgba(52, 152, 219, 0.1);
}

.switch-item input {
  margin-right: 8px;
  transform: scale(1.2);
}

.switch-label {
  font-weight: 500;
  color: #2c3e50;
}

.tab-navigation {
  display: flex;
  background: #f1f2f6;
  border-bottom: 1px solid #e1e8ed;
}

.tab-btn {
  flex: 1;
  padding: 15px 20px;
  border: none;
  background: transparent;
  color: #7f8c8d;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover:not(:disabled) {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.tab-btn.active {
  background: white;
  color: #3498db;
  border-bottom-color: #3498db;
}

.tab-btn:disabled {
  color: #bdc3c7;
  cursor: not-allowed;
  opacity: 0.5;
}

.tab-info {
  padding: 20px 30px;
  background: #fff;
  border-bottom: 1px solid #f1f2f6;
}

.tab-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
}

.tab-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.settings-content {
  padding: 30px;
}

.setting-group {
  margin-bottom: 30px;
  padding: 25px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.setting-label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 16px;
}

.setting-row {
  display: flex;
  gap: 25px;
  flex-wrap: wrap;
}

.setting-item {
  flex: 1;
  min-width: 200px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #34495e;
  font-size: 14px;
}

.setting-item input,
.setting-item select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.setting-item input:focus,
.setting-item select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.input-with-unit {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-with-unit input {
  flex: 1;
}

.unit {
  color: #7f8c8d;
  font-size: 12px;
  font-weight: 500;
  min-width: 30px;
}

.checkbox-item {
  flex: 0 0 auto;
  min-width: auto;
}

.checkbox-item label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.checkbox-item label:hover {
  background: rgba(52, 152, 219, 0.1);
}

.checkbox-item input {
  width: auto;
  margin-right: 8px;
  transform: scale(1.2);
}

.color-picker {
  width: 80px !important;
  height: 40px !important;
  padding: 2px !important;
  border-radius: 6px !important;
  cursor: pointer;
  border: 2px solid #ddd !important;
}

.color-picker:hover {
  border-color: #3498db !important;
}

.preview-section {
  margin: 30px;
  padding: 25px;
  background: #f1f2f6;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
}

.preview-section h4 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 18px;
}

.preview-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.preview-original,
.preview-translated {
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.original-text {
  margin: 0;
  font-family: "Microsoft YaHei", sans-serif;
  font-size: 14pt;
  line-height: 1.6;
  color: #2c3e50;
}

.translated-text {
  margin: 0;
  line-height: 1.6;
  transition: all 0.3s ease;
}

.format-info {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.format-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.format-item {
  background: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #495057;
  border: 1px solid #dee2e6;
}

.settings-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 30px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 30px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
  box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(149, 165, 166, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .translation-settings-overlay {
    padding: 10px;
  }

  .translation-settings {
    max-height: 95vh;
  }

  .settings-header {
    padding: 20px 20px 15px;
  }

  .global-switches {
    padding: 15px 20px;
  }

  .switch-group {
    flex-direction: column;
    gap: 15px;
  }

  .tab-navigation {
    flex-direction: column;
  }

  .tab-btn {
    padding: 12px 15px;
  }

  .tab-info {
    padding: 15px 20px;
  }

  .settings-content {
    padding: 20px;
  }

  .setting-group {
    padding: 20px;
    margin-bottom: 20px;
  }

  .setting-row {
    flex-direction: column;
    gap: 15px;
  }

  .setting-item {
    min-width: auto;
  }

  .preview-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .preview-section {
    margin: 20px;
    padding: 20px;
  }

  .format-details {
    flex-direction: column;
    gap: 10px;
  }

  .settings-actions {
    padding: 20px;
    flex-direction: column;
    gap: 15px;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .settings-header h3 {
    font-size: 20px;
  }
  
  .close-btn {
    top: 15px;
    right: 15px;
    width: 35px;
    height: 35px;
    font-size: 20px;
  }
  
  .setting-group {
    padding: 15px;
  }
  
  .preview-original,
  .preview-translated {
    padding: 15px;
  }
  
  .original-text,
  .translated-text {
    font-size: 12pt;
  }
}

/* 滚动条样式 */
.translation-settings::-webkit-scrollbar {
  width: 8px;
}

.translation-settings::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.translation-settings::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.translation-settings::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
