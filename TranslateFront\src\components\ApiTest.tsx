import React, { useState } from 'react';
import { Play, CheckCircle, XCircle, Loader } from 'lucide-react';
// import { getDocuments } from '../services/api'; // 未使用
import { testApiConnection } from '../utils/apiTest';

interface TestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

const ApiTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<TestResult | null>(null);

  const testGetDocuments = async () => {
    setTesting(true);
    setResult(null);

    try {
      console.log('开始测试获取文档列表 API...');

      // 使用简化的测试函数
      const testResult = await testApiConnection();

      if (testResult.success) {
        setResult({
          success: true,
          message: `API 连接成功！${testResult.data?.data?.rows ? `获取到 ${testResult.data.data.rows.length} 条记录` : ''}`,
          data: testResult.data
        });
      } else {
        setResult({
          success: false,
          message: 'API 连接失败',
          error: testResult.message
        });
      }
    } catch (error) {
      console.error('API 测试失败:', error);
      setResult({
        success: false,
        message: 'API 测试异常',
        error: error instanceof Error ? error.message : '未知错误'
      });
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="api-test">
      <div className="api-test-header">
        <h3>API 连接测试</h3>
        <p>测试与服务器的连接是否正常</p>
      </div>

      <div className="api-test-actions">
        <button
          className="btn btn-primary"
          onClick={testGetDocuments}
          disabled={testing}
        >
          {testing ? (
            <>
              <Loader className="spinner" size={16} />
              测试中...
            </>
          ) : (
            <>
              <Play size={16} />
              测试获取文档列表
            </>
          )}
        </button>
      </div>

      {result && (
        <div className={`api-test-result ${result.success ? 'success' : 'error'}`}>
          <div className="result-header">
            {result.success ? (
              <CheckCircle size={20} className="success-icon" />
            ) : (
              <XCircle size={20} className="error-icon" />
            )}
            <span className="result-message">{result.message}</span>
          </div>

          {result.error && (
            <div className="result-error">
              <strong>错误详情:</strong> {result.error}
            </div>
          )}

          {result.data && (
            <div className="result-data">
              <details>
                <summary>查看响应数据</summary>
                <pre>{JSON.stringify(result.data, null, 2)}</pre>
              </details>
            </div>
          )}
        </div>
      )}

      <div className="api-test-info">
        <h4>API 配置信息</h4>
        <ul>
          <li><strong>接口地址:</strong> https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows</li>
          <li><strong>工作表ID:</strong> fywd</li>
          <li><strong>请求方法:</strong> POST</li>
          <li><strong>代理状态:</strong> 生产环境 - 直接请求</li>
        </ul>

        <div className="debug-info">
          <h5>调试信息</h5>
          <p>如果遇到 CORS 错误，请确保：</p>
          <ul>
            <li>开发环境下使用 Vite 代理（已配置）</li>
            <li>生产环境需要服务器支持 CORS</li>
            <li>检查 API 密钥和签名是否正确</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ApiTest;
