import React, { useState, useEffect, useMemo } from 'react';
import { TranslationSettings, TranslationFormatSettings, DEFAULT_TRANSLATION_SETTINGS, TranslationSettingsUtils } from '../types/translation';
import './TranslationSettings.css';

interface TranslationSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  onSettingsChange: (settings: TranslationSettings) => void;
}

const TranslationSettingsComponent: React.FC<TranslationSettingsProps> = ({
  isOpen,
  onClose,
  onSettingsChange
}) => {
  const [settings, setSettings] = useState<TranslationSettings>({ ...DEFAULT_TRANSLATION_SETTINGS });
  const [activeTab, setActiveTab] = useState<'paragraph' | 'table' | 'header'>('paragraph');

  // 预览样式（基于当前选中的标签页）
  const previewStyle = useMemo(() => {
    const currentSettings = settings[activeTab];
    const style: React.CSSProperties = {
      fontFamily: currentSettings.fontFamily,
      fontSize: `${currentSettings.fontSize}pt`,
      fontWeight: currentSettings.bold ? 'bold' : 'normal',
      fontStyle: currentSettings.italic ? 'italic' : 'normal',
      textDecoration: currentSettings.underline ? 'underline' : 'none',
      color: currentSettings.color,
      backgroundColor: currentSettings.backgroundColor,
      marginTop: `${currentSettings.marginTop}pt`,
      transition: 'all 0.3s ease'
    };

    if (currentSettings.textAlign !== 'inherit') {
      style.textAlign = currentSettings.textAlign as any;
    }

    if (currentSettings.lineHeight !== 'inherit') {
      style.lineHeight = currentSettings.lineHeight;
    }

    if (currentSettings.textIndent > 0) {
      style.textIndent = `${currentSettings.textIndent}em`;
    }

    return style;
  }, [settings, activeTab]);

  // 更新当前标签页的格式设置
  const updateFormatSetting = <K extends keyof TranslationFormatSettings>(
    key: K,
    value: TranslationFormatSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      [activeTab]: {
        ...prev[activeTab],
        [key]: value
      }
    }));
  };

  // 更新全局设置
  const updateGlobalSetting = <K extends keyof Pick<TranslationSettings, 'enableParagraph' | 'enableTable' | 'enableHeader'>>(
    key: K,
    value: TranslationSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // 恢复默认设置
  const resetToDefault = () => {
    setSettings({ ...DEFAULT_TRANSLATION_SETTINGS });
  };

  // 保存设置
  const saveSettings = async () => {
    try {
      const success = await TranslationSettingsUtils.saveSettings(settings);
      if (success) {
        onSettingsChange(settings);
        alert('翻译格式设置已保存到云端！');
      } else {
        onSettingsChange(settings);
        alert('翻译格式设置已保存到本地，但云端保存失败。请检查网络连接。');
      }
    } catch (error) {
      console.error('保存设置失败:', error);
      alert('保存设置失败，请重试');
    }
  };

  // 加载保存的设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const loadedSettings = await TranslationSettingsUtils.loadSettings();
        setSettings(loadedSettings);
      } catch (error) {
        console.error('加载设置失败:', error);
        // 使用同步版本作为备用
        const fallbackSettings = TranslationSettingsUtils.loadSettingsSync();
        setSettings(fallbackSettings);
      }
    };

    loadSettings();
  }, []);

  if (!isOpen) return null;

  return (
    <div className="translation-settings-overlay">
      <div className="translation-settings">
        <div className="settings-header">
          <h3>🎨 翻译格式设置</h3>
          <button className="close-btn" onClick={onClose}>✕</button>
          <p className="settings-description">
            分别设置正文、表格、页眉的翻译格式，译文将先继承原文格式，再应用您的设置
          </p>
        </div>

        {/* 全局开关 */}
        <div className="global-switches">
          <div className="switch-group">
            <label className="switch-item">
              <input
                type="checkbox"
                checked={settings.enableParagraph}
                onChange={(e) => updateGlobalSetting('enableParagraph', e.target.checked)}
              />
              <span className="switch-label">📝 翻译正文段落</span>
            </label>
            <label className="switch-item">
              <input
                type="checkbox"
                checked={settings.enableTable}
                onChange={(e) => updateGlobalSetting('enableTable', e.target.checked)}
              />
              <span className="switch-label">📊 翻译表格内容</span>
            </label>
            <label className="switch-item">
              <input
                type="checkbox"
                checked={settings.enableHeader}
                onChange={(e) => updateGlobalSetting('enableHeader', e.target.checked)}
              />
              <span className="switch-label">📄 翻译页眉内容</span>
            </label>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="tab-navigation">
          <button
            className={`tab-btn ${activeTab === 'paragraph' ? 'active' : ''}`}
            onClick={() => setActiveTab('paragraph')}
            disabled={!settings.enableParagraph}
          >
            📝 正文段落
          </button>
          <button
            className={`tab-btn ${activeTab === 'table' ? 'active' : ''}`}
            onClick={() => setActiveTab('table')}
            disabled={!settings.enableTable}
          >
            📊 表格内容
          </button>
          <button
            className={`tab-btn ${activeTab === 'header' ? 'active' : ''}`}
            onClick={() => setActiveTab('header')}
            disabled={!settings.enableHeader}
          >
            📄 页眉内容
          </button>
        </div>

        <div className="settings-content">
          {/* 当前标签页提示 */}
          <div className="tab-info">
            <h4>
              {activeTab === 'paragraph' && '📝 正文段落格式设置'}
              {activeTab === 'table' && '📊 表格内容格式设置'}
              {activeTab === 'header' && '📄 页眉内容格式设置'}
            </h4>
            <p className="tab-description">
              {activeTab === 'paragraph' && '设置正文段落译文的字体、样式和格式'}
              {activeTab === 'table' && '设置表格单元格译文的字体、样式和格式'}
              {activeTab === 'header' && '设置页眉内容译文的字体、样式和格式'}
            </p>
          </div>

          {/* 字体设置 */}
          <div className="setting-group">
            <label className="setting-label">📝 字体设置</label>
            <div className="setting-row">
              <div className="setting-item">
                <label>字体名称</label>
                <select
                  value={settings[activeTab].fontFamily}
                  onChange={(e) => updateFormatSetting('fontFamily', e.target.value)}
                >
                  <option value="Arial">Arial</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Calibri">Calibri</option>
                  <option value="宋体">宋体</option>
                  <option value="微软雅黑">微软雅黑</option>
                  <option value="黑体">黑体</option>
                </select>
              </div>
              <div className="setting-item">
                <label>字体大小</label>
                <div className="input-with-unit">
                  <input
                    type="number"
                    value={settings[activeTab].fontSize}
                    min="6"
                    max="72"
                    step="0.5"
                    onChange={(e) => updateFormatSetting('fontSize', parseFloat(e.target.value))}
                  />
                  <span className="unit">pt</span>
                </div>
              </div>
            </div>
          </div>

          {/* 字体样式 */}
          <div className="setting-group">
            <label className="setting-label">✨ 字体样式</label>
            <div className="setting-row">
              <div className="setting-item checkbox-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings[activeTab].bold}
                    onChange={(e) => updateFormatSetting('bold', e.target.checked)}
                  />
                  <strong>粗体</strong>
                </label>
              </div>
              <div className="setting-item checkbox-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings[activeTab].italic}
                    onChange={(e) => updateFormatSetting('italic', e.target.checked)}
                  />
                  <em>斜体</em>
                </label>
              </div>
              <div className="setting-item checkbox-item">
                <label>
                  <input
                    type="checkbox"
                    checked={settings[activeTab].underline}
                    onChange={(e) => updateFormatSetting('underline', e.target.checked)}
                  />
                  <u>下划线</u>
                </label>
              </div>
            </div>
          </div>

          {/* 段落格式 */}
          <div className="setting-group">
            <label className="setting-label">📐 段落格式</label>
            <div className="setting-row">
              <div className="setting-item">
                <label>对齐方式</label>
                <select
                  value={settings[activeTab].textAlign}
                  onChange={(e) => updateFormatSetting('textAlign', e.target.value)}
                >
                  <option value="inherit">跟随原文</option>
                  <option value="left">左对齐</option>
                  <option value="center">居中</option>
                  <option value="right">右对齐</option>
                  <option value="justify">两端对齐</option>
                </select>
              </div>
              <div className="setting-item">
                <label>首行缩进</label>
                <div className="input-with-unit">
                  <input
                    type="number"
                    value={settings[activeTab].textIndent}
                    min="0"
                    max="10"
                    step="0.5"
                    onChange={(e) => updateFormatSetting('textIndent', parseFloat(e.target.value))}
                  />
                  <span className="unit">字符</span>
                </div>
              </div>
            </div>
          </div>

          {/* 间距设置 */}
          <div className="setting-group">
            <label className="setting-label">📏 间距设置</label>
            <div className="setting-row">
              <div className="setting-item">
                <label>行间距</label>
                <select
                  value={settings[activeTab].lineHeight}
                  onChange={(e) => updateFormatSetting('lineHeight', e.target.value)}
                >
                  <option value="inherit">跟随原文</option>
                  <option value="1">单倍行距</option>
                  <option value="1.15">1.15倍行距</option>
                  <option value="1.5">1.5倍行距</option>
                  <option value="2">双倍行距</option>
                </select>
              </div>
              <div className="setting-item">
                <label>段前间距</label>
                <div className="input-with-unit">
                  <input
                    type="number"
                    value={settings[activeTab].marginTop}
                    min="0"
                    max="50"
                    step="1"
                    onChange={(e) => updateFormatSetting('marginTop', parseFloat(e.target.value))}
                  />
                  <span className="unit">pt</span>
                </div>
              </div>
            </div>
          </div>

          {/* 颜色设置 */}
          <div className="setting-group">
            <label className="setting-label">🎨 颜色设置</label>
            <div className="setting-row">
              <div className="setting-item">
                <label>文字颜色</label>
                <input
                  type="color"
                  value={settings[activeTab].color}
                  onChange={(e) => updateFormatSetting('color', e.target.value)}
                  className="color-picker"
                />
              </div>
              <div className="setting-item">
                <label>背景颜色</label>
                <input
                  type="color"
                  value={settings[activeTab].backgroundColor}
                  onChange={(e) => updateFormatSetting('backgroundColor', e.target.value)}
                  className="color-picker"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 实时预览 */}
        <div className="preview-section">
          <h4>
            📖 效果预览 -
            {activeTab === 'paragraph' && '正文段落'}
            {activeTab === 'table' && '表格内容'}
            {activeTab === 'header' && '页眉内容'}
          </h4>
          <div className="preview-container">
            <div className="preview-original">
              <p className="original-text">
                {activeTab === 'paragraph' && '原文示例：这是一段中文正文段落，用于展示正文的格式效果。'}
                {activeTab === 'table' && '原文示例：表格单元格内容'}
                {activeTab === 'header' && '原文示例：页眉标题内容'}
              </p>
            </div>
            <div className="preview-translated">
              <p className="translated-text" style={previewStyle}>
                {activeTab === 'paragraph' && '译文示例：This is an English paragraph translation example to show the paragraph format effect.'}
                {activeTab === 'table' && '译文示例：Table cell content'}
                {activeTab === 'header' && '译文示例：Header title content'}
              </p>
            </div>
          </div>

          {/* 格式信息显示 */}
          <div className="format-info">
            <div className="format-details">
              <span className="format-item">字体: {settings[activeTab].fontFamily}</span>
              <span className="format-item">大小: {settings[activeTab].fontSize}pt</span>
              <span className="format-item">
                样式: {[
                  settings[activeTab].bold && '粗体',
                  settings[activeTab].italic && '斜体',
                  settings[activeTab].underline && '下划线'
                ].filter(Boolean).join(', ') || '无'}
              </span>
              <span className="format-item">对齐: {
                settings[activeTab].textAlign === 'inherit' ? '跟随原文' :
                settings[activeTab].textAlign === 'left' ? '左对齐' :
                settings[activeTab].textAlign === 'center' ? '居中' :
                settings[activeTab].textAlign === 'right' ? '右对齐' :
                settings[activeTab].textAlign === 'justify' ? '两端对齐' : settings[activeTab].textAlign
              }</span>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="settings-actions">
          <button onClick={resetToDefault} className="btn-secondary">
            🔄 恢复默认
          </button>
          <button onClick={saveSettings} className="btn-primary">
            💾 保存设置
          </button>
        </div>
      </div>
    </div>
  );
};

export default TranslationSettingsComponent;
