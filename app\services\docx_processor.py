"""
Word 文档处理服务
用于在原始文档中插入翻译内容，保持原文格式
"""
import os
import tempfile
from typing import Dict, List, Tuple
from docx import Document
from docx.shared import RGBColor
from docx.enum.text import WD_COLOR_INDEX
from docx.oxml.shared import OxmlElement, qn
from app.utils.logger import logger


class DocxProcessor:
    """Word 文档处理器"""
    
    def __init__(self):
        self.translation_style_name = "TranslationText"
    
    def create_bilingual_document(
        self,
        original_file_path: str,
        translation_data: Dict[str, str],
        output_path: str = None
    ) -> str:
        """
        创建双语文档，在原文下方插入译文

        Args:
            original_file_path: 原始文档路径
            translation_data: 翻译数据 {原文: 译文}
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            输出文件路径
        """
        try:
            # 打开原始文档
            doc = Document(original_file_path)
            logger.info(f"Processing document: {original_file_path}")

            # 添加翻译样式
            self._add_translation_style(doc)

            # 处理页眉和页脚
            self._process_headers_footers(doc, translation_data)

            # 处理段落（避免重复翻译）
            self._process_paragraphs_improved(doc, translation_data)

            # 处理表格
            self._process_tables(doc, translation_data)

            # 生成输出路径
            if output_path is None:
                output_path = self._generate_output_path(original_file_path)

            # 保存文档
            doc.save(output_path)
            logger.info(f"Bilingual document saved: {output_path}")

            return output_path

        except Exception as e:
            logger.error(f"Error creating bilingual document: {e}")
            raise
    
    def _add_translation_style(self, doc: Document):
        """添加翻译文本样式"""
        try:
            styles = doc.styles
            
            # 检查样式是否已存在
            try:
                translation_style = styles[self.translation_style_name]
                return translation_style
            except KeyError:
                pass
            
            # 创建新样式
            translation_style = styles.add_style(self.translation_style_name, 1)  # 1 = WD_STYLE_TYPE.PARAGRAPH
            
            # 设置样式属性 - Arial 小四号，不加粗，黑色，不斜体
            font = translation_style.font
            font.name = 'Arial'
            font.size = 120000  # 小四号 = 12pt = 120000 twentieths of a point
            font.color.rgb = RGBColor(0, 0, 0)  # 黑色
            font.bold = False
            font.italic = False
            
            # 设置段落格式
            paragraph_format = translation_style.paragraph_format
            paragraph_format.space_before = 0
            paragraph_format.space_after = 6  # 6pt 间距
            paragraph_format.left_indent = 360000  # 0.25 inch 缩进 (1 inch = 1440000 EMU)
            
            logger.info("Translation style added successfully")
            return translation_style
            
        except Exception as e:
            logger.error(f"Error adding translation style: {e}")
            # 如果添加样式失败，继续处理但不使用特殊样式
            return None

    def _process_headers_footers(self, doc: Document, translation_data: Dict[str, str]):
        """处理文档页眉和页脚"""
        try:
            translation_count = 0

            # 处理所有节的页眉和页脚
            for section in doc.sections:
                # 处理页眉
                for header in [section.header, section.even_page_header, section.first_page_header]:
                    if header:
                        translation_count += self._process_header_footer_paragraphs(header, translation_data)

                # 处理页脚
                for footer in [section.footer, section.even_page_footer, section.first_page_footer]:
                    if footer:
                        translation_count += self._process_header_footer_paragraphs(footer, translation_data)

            logger.info(f"Processed {translation_count} header/footer translations")

        except Exception as e:
            logger.error(f"Error processing headers/footers: {e}")

    def _process_header_footer_paragraphs(self, header_footer, translation_data: Dict[str, str]) -> int:
        """处理页眉或页脚中的段落"""
        try:
            translation_count = 0
            paragraphs_to_add = []

            for i, paragraph in enumerate(header_footer.paragraphs):
                original_text = paragraph.text.strip()

                if original_text and original_text in translation_data:
                    translated_text = translation_data[original_text]
                    paragraphs_to_add.append((i, translated_text))

            # 从后往前插入，避免索引变化
            for original_index, translated_text in reversed(paragraphs_to_add):
                self._insert_translation_in_header_footer(header_footer, original_index, translated_text)
                translation_count += 1

            return translation_count

        except Exception as e:
            logger.error(f"Error processing header/footer paragraphs: {e}")
            return 0

    def _insert_translation_in_header_footer(self, header_footer, after_index: int, translated_text: str):
        """在页眉或页脚中插入翻译"""
        try:
            # 在页眉/页脚中添加新段落
            new_paragraph = header_footer.add_paragraph()
            new_paragraph.text = translated_text

            # 应用翻译样式
            try:
                new_paragraph.style = self.translation_style_name
            except:
                # 手动设置格式
                if new_paragraph.runs:
                    run = new_paragraph.runs[0]
                    run.font.name = 'Arial'
                    run.font.size = 120000  # 小四号 = 12pt
                    run.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
                    run.font.bold = False
                    run.font.italic = False

        except Exception as e:
            logger.error(f"Error inserting translation in header/footer: {e}")

    def _process_paragraphs_improved(self, doc: Document, translation_data: Dict[str, str]):
        """改进的段落处理，避免重复翻译"""
        try:
            paragraphs_to_add = []  # 存储要添加的翻译段落
            processed_texts = set()  # 记录已处理的文本，避免重复

            for i, paragraph in enumerate(doc.paragraphs):
                original_text = paragraph.text.strip()

                # 跳过空文本、已处理的文本、以及已经是翻译文本的段落
                if (original_text and
                    original_text not in processed_texts and
                    original_text in translation_data and
                    not self._is_translation_paragraph(paragraph)):

                    translated_text = translation_data[original_text]

                    # 记录要在此段落后插入的翻译
                    paragraphs_to_add.append((i, translated_text))
                    processed_texts.add(original_text)

            # 从后往前插入，避免索引变化
            for original_index, translated_text in reversed(paragraphs_to_add):
                self._insert_translation_paragraph_improved(doc, original_index, translated_text)

            logger.info(f"Processed {len(paragraphs_to_add)} paragraph translations (avoided duplicates)")

        except Exception as e:
            logger.error(f"Error processing paragraphs: {e}")

    def _is_translation_paragraph(self, paragraph) -> bool:
        """检查段落是否已经是翻译段落"""
        try:
            # 检查段落样式
            if hasattr(paragraph, 'style') and paragraph.style:
                if paragraph.style.name == self.translation_style_name:
                    return True

            # 检查段落的字体格式 - 更宽松的检查
            for run in paragraph.runs:
                # 检查是否是Arial字体且大小在11-12pt之间（可能有轻微差异）
                if (run.font.name == 'Arial' and
                    run.font.size and
                    110000 <= run.font.size <= 130000):  # 11pt到13pt之间

                    # 检查颜色是否是黑色或接近黑色
                    if run.font.color.rgb:
                        rgb = run.font.color.rgb
                        if rgb.red <= 50 and rgb.green <= 50 and rgb.blue <= 50:  # 接近黑色
                            return True

            return False

        except Exception:
            return False
    
    def _insert_translation_paragraph_improved(self, doc: Document, after_index: int, translated_text: str):
        """改进的段落插入方法，确保正确的格式"""
        try:
            # 获取原段落
            original_paragraph = doc.paragraphs[after_index]

            # 在原段落后插入新段落
            new_paragraph = self._insert_paragraph_after(doc, original_paragraph)

            # 清空段落内容，重新添加
            new_paragraph.clear()

            # 添加翻译文本
            run = new_paragraph.add_run(translated_text)

            # 设置正确的格式：Arial 小四号，不加粗，黑色，不斜体
            run.font.name = 'Arial'
            run.font.size = 120000  # 小四号 = 12pt = 120000 twentieths of a point
            run.font.color.rgb = RGBColor(0, 0, 0)  # 黑色
            run.font.bold = False
            run.font.italic = False

            # 尝试应用翻译样式
            try:
                new_paragraph.style = self.translation_style_name
            except Exception as style_error:
                logger.warning(f"Could not apply translation style: {style_error}")

        except Exception as e:
            logger.error(f"Error inserting translation paragraph: {e}")

    def _insert_translation_paragraph(self, doc: Document, after_index: int, translated_text: str):
        """在指定段落后插入翻译段落（保留原方法以兼容）"""
        return self._insert_translation_paragraph_improved(doc, after_index, translated_text)
    
    def _insert_paragraph_after(self, doc: Document, paragraph):
        """在指定段落后插入新段落"""
        try:
            # 获取段落的 XML 元素
            p_element = paragraph._element
            
            # 创建新段落元素
            new_p = OxmlElement("w:p")
            
            # 在原段落后插入新段落
            p_element.getparent().insert(
                p_element.getparent().index(p_element) + 1, 
                new_p
            )
            
            # 创建新的段落对象
            from docx.text.paragraph import Paragraph
            new_paragraph = Paragraph(new_p, paragraph._parent)
            
            return new_paragraph
            
        except Exception as e:
            logger.error(f"Error inserting paragraph: {e}")
            # 如果插入失败，在文档末尾添加
            return doc.add_paragraph()
    
    def _process_tables(self, doc: Document, translation_data: Dict[str, str]):
        """处理文档表格，在原文单元格下方添加译文"""
        try:
            translation_count = 0
            
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        # 处理单元格中的段落
                        for paragraph in cell.paragraphs:
                            original_text = paragraph.text.strip()
                            
                            if original_text and original_text in translation_data:
                                translated_text = translation_data[original_text]
                                
                                # 在单元格中添加翻译段落
                                translation_paragraph = cell.add_paragraph()
                                translation_paragraph.text = translated_text
                                
                                # 应用翻译样式
                                try:
                                    translation_paragraph.style = self.translation_style_name
                                except:
                                    # 手动设置格式
                                    if translation_paragraph.runs:
                                        run = translation_paragraph.runs[0]
                                        run.font.color.rgb = RGBColor(0, 100, 0)
                                        run.font.italic = True
                                
                                translation_count += 1
            
            logger.info(f"Processed {translation_count} table cell translations")
            
        except Exception as e:
            logger.error(f"Error processing tables: {e}")
    
    def _generate_output_path(self, original_path: str) -> str:
        """生成输出文件路径"""
        try:
            # 获取文件信息
            dir_path = os.path.dirname(original_path)
            filename = os.path.basename(original_path)
            name, ext = os.path.splitext(filename)
            
            # 生成新文件名
            output_filename = f"{name}_bilingual{ext}"
            output_path = os.path.join(dir_path, output_filename)
            
            return output_path
            
        except Exception as e:
            logger.error(f"Error generating output path: {e}")
            # 如果生成失败，使用临时文件
            return tempfile.mktemp(suffix='.docx')
    
    def parse_translation_result(self, translation_text: str) -> Dict[str, str]:
        """
        改进的翻译结果解析，避免重复翻译

        Args:
            translation_text: 翻译结果文本

        Returns:
            字典 {原文: 译文}
        """
        try:
            translation_dict = {}
            lines = translation_text.split('\n')

            current_original = None
            current_translation = None
            processed_originals = set()  # 记录已处理的原文，避免重复

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检查是否是原文标记
                if line.startswith('[原文]'):
                    current_original = line.replace('[原文]', '').strip()
                    current_translation = None
                elif line.startswith('[译文]'):
                    current_translation = line.replace('[译文]', '').strip()

                    # 如果有原文和译文，且原文未被处理过，添加到字典
                    if (current_original and
                        current_translation and
                        current_original not in processed_originals):

                        translation_dict[current_original] = current_translation
                        processed_originals.add(current_original)
                        current_original = None
                        current_translation = None
                else:
                    # 如果没有标记，尝试按行配对
                    if current_original is None:
                        current_original = line
                    elif current_translation is None:
                        current_translation = line

                        # 检查是否重复
                        if current_original not in processed_originals:
                            translation_dict[current_original] = current_translation
                            processed_originals.add(current_original)

                        current_original = None
                        current_translation = None

            logger.info(f"Parsed {len(translation_dict)} unique translation pairs (avoided duplicates)")

            # 调试信息：显示解析的翻译对
            for orig, trans in list(translation_dict.items())[:5]:  # 只显示前5个
                logger.debug(f"Translation pair: '{orig[:50]}...' -> '{trans[:50]}...'")

            return translation_dict

        except Exception as e:
            logger.error(f"Error parsing translation result: {e}")
            return {}


# 创建全局实例
docx_processor = DocxProcessor()
