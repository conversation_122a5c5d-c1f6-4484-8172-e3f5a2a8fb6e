#!/bin/bash

echo "🚀 SMILE TRANS 一键修复和启动"
echo "============================="

# 1. 停止现有服务
echo "1. ⏹️  停止现有服务..."
pm2 delete all 2>/dev/null || echo "没有运行的服务"

# 2. 创建必要目录
echo "2. 📁 创建日志目录..."
mkdir -p logs

# 3. 重新启动服务
echo "3. 🚀 启动服务..."
pm2 start ecosystem.config.cjs

# 4. 等待服务启动
echo "4. ⏳ 等待服务启动..."
sleep 5

# 5. 检查服务状态
echo "5. 📊 检查服务状态..."
pm2 status

# 6. 配置防火墙
echo "6. 🛡️  配置防火墙..."
if command -v ufw &> /dev/null; then
    echo "配置 Ubuntu/Debian 防火墙..."
    sudo ufw allow 3000 2>/dev/null || echo "防火墙配置可能需要手动执行"
elif command -v firewall-cmd &> /dev/null; then
    echo "配置 CentOS/RHEL 防火墙..."
    sudo firewall-cmd --permanent --add-port=3000/tcp 2>/dev/null || echo "防火墙配置可能需要手动执行"
    sudo firewall-cmd --reload 2>/dev/null || echo "防火墙重载可能需要手动执行"
fi

# 7. 测试服务
echo "7. 🧪 测试服务..."
sleep 2

# 检查端口
if netstat -tlnp | grep :3000 > /dev/null; then
    echo "✅ 端口 3000 正在监听"
    
    # 测试 HTTP 响应
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ HTTP 服务正常"
    else
        echo "⚠️  HTTP 响应异常 (状态码: $HTTP_CODE)"
    fi
else
    echo "❌ 端口 3000 未在监听"
fi

# 8. 保存配置
echo "8. 💾 保存 PM2 配置..."
pm2 save

# 9. 显示结果
echo ""
echo "🎉 修复完成！"
echo "=============="

# 获取服务器信息
SERVER_IP=$(hostname -I | awk '{print $1}')
EXTERNAL_IP=$(curl -s ifconfig.me 2>/dev/null || echo "无法获取")

echo ""
echo "📊 服务状态:"
pm2 list

echo ""
echo "🌐 访问地址:"
echo "   内网: http://${SERVER_IP}:3000"
if [ "$EXTERNAL_IP" != "无法获取" ]; then
    echo "   外网: http://${EXTERNAL_IP}:3000"
fi

echo ""
echo "📝 常用命令:"
echo "   查看状态: pm2 status"
echo "   查看日志: pm2 logs translate-front"
echo "   重启服务: pm2 restart translate-front"
echo "   停止服务: pm2 stop translate-front"

echo ""
echo "🔧 如果仍无法访问，请检查:"
echo "   1. 云服务器安全组是否开放 3000 端口"
echo "   2. 运行诊断: ./diagnose.sh"
echo "   3. 查看详细日志: pm2 logs translate-front"

echo ""
echo "✅ 现在你的服务应该可以在断开 SSH 连接后继续运行了！"
