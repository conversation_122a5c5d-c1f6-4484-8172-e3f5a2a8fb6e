#!/bin/bash

echo "🔧 修复 PM2 配置并重新启动服务..."

# 1. 停止所有 PM2 进程
echo "⏹️  停止现有 PM2 进程..."
pm2 delete all 2>/dev/null || echo "没有运行的 PM2 进程"

# 2. 创建日志目录
echo "📁 创建日志目录..."
mkdir -p logs

# 3. 检查配置文件
if [ ! -f "ecosystem.config.cjs" ]; then
    echo "❌ 配置文件不存在，请确保 ecosystem.config.cjs 文件已创建"
    exit 1
fi

# 4. 检查服务器文件
if [ ! -f "server.cjs" ]; then
    echo "❌ server.cjs 文件不存在"
    exit 1
fi

# 5. 启动 PM2 服务
echo "🚀 启动 PM2 服务..."
pm2 start ecosystem.config.cjs

# 6. 检查服务状态
echo "📊 检查服务状态..."
pm2 status

# 7. 检查端口监听
echo "🔍 检查端口监听..."
sleep 3
netstat -tlnp | grep :3000 || echo "⚠️  端口 3000 未在监听"

# 8. 保存 PM2 配置
echo "💾 保存 PM2 配置..."
pm2 save

echo ""
echo "✅ 修复完成！"
echo ""
echo "📊 查看服务状态: pm2 status"
echo "📝 查看日志: pm2 logs translate-front"
echo "🌐 访问地址: http://$(hostname -I | awk '{print $1}'):3000"
echo ""
echo "如果仍然无法访问，请检查："
echo "1. 防火墙设置: sudo ufw allow 3000"
echo "2. 服务器安全组设置（云服务器）"
echo "3. 查看错误日志: pm2 logs translate-front --err"
