.mingdao-system-status {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 整体状态 */
.overall-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 25px;
  transition: all 0.3s ease;
}

.overall-status.healthy {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  border: 2px solid #4caf50;
}

.overall-status.warning {
  background: linear-gradient(135deg, #fff3e0, #ffcc02);
  border: 2px solid #ff9800;
}

.overall-status.error {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  border: 2px solid #f44336;
}

.overall-status-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.overall-status-text h3 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
  color: #333;
}

.overall-status-text p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.overall-icon.healthy { color: #4caf50; }
.overall-icon.warning { color: #ff9800; }
.overall-icon.error { color: #f44336; }

.refresh-status-btn {
  background: rgba(255,255,255,0.8);
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 8px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
}

.refresh-status-btn:hover {
  background: rgba(255,255,255,0.9);
  transform: translateY(-1px);
}

.refresh-status-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 状态详情 */
.status-details {
  margin-bottom: 25px;
}

.status-details h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s;
}

.status-item.healthy {
  background: #f8fff8;
  border-color: #4caf50;
}

.status-item.warning {
  background: #fffbf0;
  border-color: #ff9800;
}

.status-item.error {
  background: #fff5f5;
  border-color: #f44336;
}

.status-item.checking {
  background: #f0f8ff;
  border-color: #2196f3;
}

.status-item-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.status-name {
  font-weight: 600;
  color: #333;
  flex: 1;
}

.response-time {
  font-size: 0.8rem;
  color: #666;
  background: rgba(0,0,0,0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

.status-message {
  font-size: 0.9rem;
  color: #555;
  margin-bottom: 5px;
}

.last-check {
  font-size: 0.8rem;
  color: #888;
}

.status-icon.healthy { color: #4caf50; }
.status-icon.warning { color: #ff9800; }
.status-icon.error { color: #f44336; }
.status-icon.checking { 
  color: #2196f3;
  animation: spin 1s linear infinite;
}

/* 系统信息 */
.system-info {
  margin-bottom: 25px;
}

.system-info h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-item svg {
  color: #666;
  flex-shrink: 0;
}

.info-label {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 2px;
}

.info-value {
  font-size: 0.85rem;
  color: #333;
  font-weight: 600;
  word-break: break-all;
}

/* 最后检查时间 */
.last-check-time {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  color: #666;
}

/* 收费规则概览 */
.pricing-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.pricing-summary h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  margin-bottom: 15px;
  font-size: 1rem;
}

.pricing-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.pricing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.pricing-label {
  font-size: 0.85rem;
  color: #666;
}

.pricing-value {
  font-size: 0.85rem;
  color: #333;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mingdao-system-status {
    padding: 15px;
  }
  
  .overall-status {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .overall-status-content {
    flex-direction: column;
    gap: 10px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .pricing-items {
    grid-template-columns: 1fr;
  }
  
  .status-item-header {
    flex-wrap: wrap;
  }
  
  .response-time {
    order: 3;
    width: 100%;
    text-align: center;
    margin-top: 5px;
  }
}
