// API 配置文件
// 你可以根据实际情况修改这些配置

export const API_CONFIG = {
  // API 基础配置 - 固定使用指定接口
  baseUrl: '/api/v2/open',
  endpoints: {
    getRows: '/worksheet/getFilterRows', // 获取数据接口
    getTotalNum: '/worksheet/getFilterRowsTotalNum' // 获取总行数接口
  },
  appKey: 'd88c1d2329c42504',
  sign: 'YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==',
  worksheetId: 'fywd',

  // 文件上传相关配置
  upload: {
    // 原始文件控制项ID
    originalFileControlId: 'YWJ',
    // 处理后文件控制项ID
    processedFileControlId: 'FYWJ',
    // 备注控制项ID
    remarkControlId: '68789e5fa849420e13f65dcf',
    // 默认拥有者ID（可选）
    defaultOwnerId: '',
    // 最大文件大小（MB）
    maxFileSize: 50,
    // 支持的文件类型
    allowedTypes: ['.doc', '.docx', '.pdf', '.txt', '.xlsx', '.xls', '.pptx', '.ppt'],
    // 是否触发工作流
    triggerWorkflow: true
  },

  // 分页配置
  pagination: {
    defaultPageSize: 100, // 默认一页100条
    maxPageSize: 200,
    pageSizeOptions: [50, 100, 200] // 分页选项
  },

  // 默认过滤器（简化版，不使用过滤条件）
  defaultFilters: [],

  // 请求配置
  request: {
    timeout: 30000, // 30秒超时
    retryCount: 3,   // 重试次数
    retryDelay: 1000 // 重试延迟（毫秒）
  }
};

// 控制项ID映射（方便管理）
export const CONTROL_IDS = {
  ORIGINAL_FILE: 'YWJ',        // 原文件
  PROCESSED_FILE: 'FYWJ',      // 翻译文件（用于预览）
  REMARK: '68789e5fa849420e13f65dcf',           // 备注
  OWNER: 'ownerid'                             // 拥有者
} as const;

// 预览相关配置
export const PREVIEW_CONFIG = {
  // 原始预览地址基础URL（备用）
  baseUrl: 'https://dmit.duoningbio.com/rowfile',
  // 翻译后文件的控制项ID（用于预览）
  previewControlId: CONTROL_IDS.PROCESSED_FILE,

  // OnlyOffice 在线预览服务配置（暂时禁用）
  onlyoffice: {
    // OnlyOffice Document Server 地址
    serverUrl: 'http://10.250.200.231:8088/',
    // 是否启用 OnlyOffice 预览（暂时禁用）
    enabled: false,
    // 支持的文件类型
    supportedFormats: ['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'pdf', 'txt'],
    // JWT 配置
    jwt: {
      // JWT 密钥
      secret: 'ddFgc9cO9WkeIyRkm2hDGcg1LFIV6HBW',
      // JWT 头部名称
      header: 'Authorization',
      // 是否启用 JWT
      enabled: true
    },
    // 编辑器配置
    editor: {
      // 编辑模式：view（只读）、edit（编辑）
      mode: 'view',
      // 语言设置
      lang: 'zh-CN'
    }
  }
} as const;

// 文件类型映射
export const FILE_TYPE_MAP = {
  1: 'image',
  2: 'document',
  3: 'video',
  4: 'audio'
} as const;

// 数据类型枚举
export const DATA_TYPES = {
  TEXT: 2,
  NUMBER: 6,
  DATE: 15,
  ATTACHMENT: 14,
  USER: 26
} as const;

// 过滤器类型枚举
export const FILTER_TYPES = {
  EQUAL: 1,
  NOT_EQUAL: 2,
  CONTAINS: 3,
  NOT_CONTAINS: 4,
  STARTS_WITH: 5,
  ENDS_WITH: 6,
  GREATER_THAN: 7,
  LESS_THAN: 8,
  GREATER_EQUAL: 9,
  LESS_EQUAL: 10,
  BETWEEN: 11,
  NOT_BETWEEN: 12,
  IS_NULL: 13,
  IS_NOT_NULL: 14
} as const;

// 拼接类型枚举
export const SPLICE_TYPES = {
  AND: 1,
  OR: 2
} as const;

// 明道云用户认证相关配置
export const MINGDAO_AUTH_CONFIG = {
  // 明道云API基础配置 - 使用代理路径
  baseUrl: '/api/v2/open',
  appKey: 'd88c1d2329c42504',
  sign: 'YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==',

  // 工作表ID配置
  worksheets: {
    users: '6886e20ba849420e13f69b23',           // 用户表
    recharge_records: 'recharge_records',        // 充值记录表
    translations: '6886f053a849420e13f69b61',    // 翻译记录表
    consumption_records: 'consumption_records'   // 消费记录表
  },

  // 用户表字段ID映射
  userFields: {
    username: '6886e20ba849420e13f69b24',
    email: '6886e4c8a849420e13f69b30',
    password_hash: '6886e4c8a849420e13f69b31',
    full_name: '6886e4c8a849420e13f69b32',
    phone: '6886e4c8a849420e13f69b33',
    user_type: '6886e4c8a849420e13f69b34',
    status: '6886e4c8a849420e13f69b35',
    balance: '6886e4c8a849420e13f69b36',
    total_quota: '6886e4c8a849420e13f69b37',
    used_quota: '6886e4c8a849420e13f69b38',
    monthly_quota: '6886e4c8a849420e13f69b39',
    monthly_used: '6886e4c8a849420e13f69b3a',
    monthly_expire_date: '6886e4c8a849420e13f69b3b',
    created_at: '6886e4c8a849420e13f69b3c',
    updated_at: '6886e4c8a849420e13f69b3d',
    last_login: '6886e4c8a849420e13f69b3e'
  },

  // 充值记录表字段ID映射
  rechargeFields: {
    user: 'user',                               // 关联用户字段
    order_id: '6886f9e7a849420e13f69bad',       // 订单ID
    amount: 'amount',                           // 充值金额
    type: '6886f9e7a849420e13f69bb0',           // 充值类型
    method: '6886f9e7a849420e13f69bb1',         // 支付方式
    status: '6886f9e7a849420e13f69bb2',         // 状态
    description: '6886f9e7a849420e13f69bb3',    // 描述
    quota_granted: 'quota_granted',             // 分配的配额
    created_at: 'ctime',                        // 创建时间
    completed_at: '6886f9e7a849420e13f69bb4'    // 完成时间
  },

  // 翻译记录表字段ID映射
  translationFields: {
    user: '6886f887a849420e13f69b8f',           // 关联用户字段
    original_file: '6886f7a4a849420e13f69b6f',  // 原文件
    translated_file: '6886f7a4a849420e13f69b70', // 翻译后文件
    status: '6886f7a4a849420e13f69b71',         // 翻译状态
    total_chars: '6886f7a4a849420e13f69b72',    // 总字符数
    cost: '6886f7a4a849420e13f69b73',           // 翻译费用(元)
    payment_type: '6886f7a4a849420e13f69b74',   // 付费方式
    created_at: '6886f7a4a849420e13f69b75',     // 创建时间
    updated_at: '6886f7a4a849420e13f69b76',     // 更新时间
    completed_at: '6886f7a4a849420e13f69b77',   // 完成时间
    record_id: '6886f887a849420e13f69b8e'       // 记录ID
  }
} as const;

// 明道云数据类型
export const MINGDAO_DATA_TYPES = {
  TEXT: 2,        // 文本
  NUMBER: 6,      // 数字
  DATE: 15,       // 日期
  RELATION: 29,   // 关联记录
  USER: 26        // 成员
} as const;

// 明道云过滤器类型
export const MINGDAO_FILTER_TYPES = {
  EQUAL: 1,           // 等于
  NOT_EQUAL: 2,       // 不等于
  CONTAINS: 3,        // 包含
  NOT_CONTAINS: 4,    // 不包含
  RELATION_CONTAINS: 24  // 关联记录包含
} as const;
