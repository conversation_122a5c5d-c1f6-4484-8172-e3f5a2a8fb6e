#!/bin/bash

echo "🧪 测试 SMILE TRANS 服务"
echo "========================"

# 获取服务器 IP
SERVER_IP=$(hostname -I | awk '{print $1}')
PORT=3000
URL="http://${SERVER_IP}:${PORT}"

echo "测试地址: $URL"
echo ""

# 1. 测试端口连通性
echo "1. 🔌 测试端口连通性..."
if nc -z localhost $PORT 2>/dev/null; then
    echo "✅ 端口 $PORT 可访问"
else
    echo "❌ 端口 $PORT 不可访问"
    echo "请检查服务是否启动: pm2 status"
    exit 1
fi

# 2. 测试 HTTP 响应
echo ""
echo "2. 🌐 测试 HTTP 响应..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" $URL)
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ HTTP 响应正常 (状态码: $HTTP_CODE)"
else
    echo "❌ HTTP 响应异常 (状态码: $HTTP_CODE)"
fi

# 3. 测试页面内容
echo ""
echo "3. 📄 测试页面内容..."
CONTENT=$(curl -s $URL | head -c 100)
if [[ $CONTENT == *"SMILE TRANS"* ]]; then
    echo "✅ 页面内容正确"
else
    echo "❌ 页面内容异常"
    echo "返回内容: $CONTENT"
fi

# 4. 测试 API 接口
echo ""
echo "4. 🔗 测试 API 代理..."
API_CODE=$(curl -s -o /dev/null -w "%{http_code}" "${URL}/api/v2/open/worksheet/getFilterRows" -X POST -H "Content-Type: application/json" -d '{}')
echo "API 响应状态码: $API_CODE"

echo ""
echo "🎉 测试完成！"
echo ""
echo "如果所有测试都通过，你可以通过以下地址访问:"
echo "🌐 $URL"
echo ""
echo "如果外网无法访问，请检查:"
echo "1. 防火墙设置"
echo "2. 云服务器安全组配置"
echo "3. 网络连接"
