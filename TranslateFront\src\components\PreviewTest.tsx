import React from 'react';
import { ExternalLink, FileText } from 'lucide-react';
import { generatePreviewUrl, getDocumentFiles } from '../utils/preview';

// 使用新的数据结构进行测试
const testRowData = {
  "_id": "687a29a5a849420e13f6640c",
  "rowid": "7945faa6-5c35-4843-9461-3d2d75b61f50",
  "ctime": "2025-07-18 19:01:57",
  "utime": "2025-07-18 19:01:57",
  "autoid": 0,
  "caid": { accountId: "test", fullname: "测试", avatar: "", isPortal: false, status: 1 },
  "uaid": { accountId: "test", fullname: "测试", avatar: "", isPortal: false, status: 1 },
  "ownerid": { accountId: "test", fullname: "测试", avatar: "", isPortal: false, status: 1 },
  "wfstatus": "",
  "wfname": "",
  "wfcaid": "",
  "wfctime": "",
  "wfcuaids": "",
  "wfftime": "",
  "wfrtime": "",
  "YWJ": "[{\"file_id\":\"3ee49a83-5664-4026-9e2c-d76c393a64c7\",\"original_file_name\":\"SMP-FM-001-01 厂房设计、施工、验收标准管理规程2.docx\",\"file_size\":55287}]",
  "FYWJ": "[{\"file_id\":\"99fab634-46e6-4400-b893-e1766ded1606\",\"original_file_name\":\"SMP-FM-001-01 厂房设计、施工、验收标准管理规程.docx_eng-US.doc\",\"file_size\":95744}]"
};

const PreviewTest: React.FC = () => {
  const previewUrl = generatePreviewUrl(testRowData.rowid);
  const documentFiles = getDocumentFiles(testRowData);

  const openTestPreview = () => {
    window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
  };

  return (
    <div className="preview-test">
      <div className="preview-test-header">
        <h3>预览功能测试</h3>
        <p>测试预览地址生成和文件解析功能</p>
      </div>

      <div className="test-section">
        <h4>预览地址生成</h4>
        <div className="test-item">
          <strong>Row ID:</strong> {testRowData.rowid}
        </div>
        <div className="test-item">
          <strong>生成的预览地址:</strong> 
          <a href={previewUrl} target="_blank" rel="noopener noreferrer" className="preview-link">
            {previewUrl}
          </a>
        </div>
        <button className="btn btn-primary" onClick={openTestPreview}>
          <ExternalLink size={16} />
          打开预览窗口
        </button>
      </div>

      <div className="test-section">
        <h4>文件解析结果</h4>
        
        <div className="file-analysis">
          <div className="analysis-item">
            <strong>是否有原始文件:</strong> 
            <span className={documentFiles.hasOriginalFiles ? 'status-yes' : 'status-no'}>
              {documentFiles.hasOriginalFiles ? '是' : '否'}
            </span>
          </div>
          
          <div className="analysis-item">
            <strong>是否有翻译后文件:</strong> 
            <span className={documentFiles.hasProcessedFiles ? 'status-yes' : 'status-no'}>
              {documentFiles.hasProcessedFiles ? '是' : '否'}
            </span>
          </div>
          
          <div className="analysis-item">
            <strong>是否可以预览:</strong> 
            <span className={documentFiles.previewInfo.canPreview ? 'status-yes' : 'status-no'}>
              {documentFiles.previewInfo.canPreview ? '是' : '否'}
            </span>
          </div>
        </div>

        {documentFiles.originalFiles.length > 0 && (
          <div className="file-list">
            <h5>原始文件列表</h5>
            {documentFiles.originalFiles.map((file, index) => (
              <div key={index} className="file-item">
                <FileText size={16} />
                <span>{file.original_file_name}</span>
                <span className="file-size">({(file.file_size / 1024).toFixed(1)} KB)</span>
              </div>
            ))}
          </div>
        )}

        {documentFiles.processedFiles.length > 0 && (
          <div className="file-list">
            <h5>翻译后文件列表</h5>
            {documentFiles.processedFiles.map((file, index) => (
              <div key={index} className="file-item processed">
                <FileText size={16} />
                <span>{file.original_file_name}</span>
                <span className="file-size">({(file.file_size / 1024).toFixed(1)} KB)</span>
                <span className="file-status">已翻译</span>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="test-section">
        <h4>预览地址说明</h4>
        <ul className="info-list">
          <li>预览地址格式: <code>https://dmit.duoningbio.com/rowfile/{'{rowid}'}/ </code></li>
          <li>只有当 <code>FYWJ</code> 字段有翻译文件时才显示预览按钮</li>
          <li>预览的是翻译后的文件，不是原始文件</li>
          <li>点击预览按钮会在新窗口打开预览页面</li>
          <li><strong>数据结构更新:</strong> YWJ=原文件, FYWJ=翻译文件</li>
        </ul>
      </div>
    </div>
  );
};

export default PreviewTest;
