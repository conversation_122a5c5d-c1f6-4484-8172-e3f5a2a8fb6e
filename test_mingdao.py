"""
测试明道云服务
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.mingdao_full_service import mingdao_full_service

async def test_create_user():
    """测试创建用户"""
    print("🧪 测试创建用户...")
    try:
        result = await mingdao_full_service.create_user(
            username="testuser",
            email="<EMAIL>", 
            password="testpass123",
            full_name="测试用户"
        )
        print(f"✅ 创建用户成功: {result}")
        return result.get("user_id")
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return None

async def test_authenticate_user():
    """测试用户认证"""
    print("\n🧪 测试用户认证...")
    try:
        result = await mingdao_full_service.authenticate_user("testuser", "testpass123")
        print(f"✅ 用户认证成功: {result['success']}")
        if result["success"]:
            user = result["user"]
            print(f"   用户ID: {user['id']}")
            print(f"   用户名: {user['username']}")
            print(f"   邮箱: {user['email']}")
            print(f"   余额: {user['balance']} 元")
            print(f"   总配额: {user['total_quota']} 字符")
            print(f"   已使用: {user['used_quota']} 字符")
            return user
        return None
    except Exception as e:
        print(f"❌ 用户认证失败: {e}")
        return None

async def test_get_translation_history(user_id: str):
    """测试获取翻译历史"""
    print(f"\n🧪 测试获取翻译历史 (用户ID: {user_id})...")
    try:
        result = await mingdao_full_service.get_translation_history(user_id, page=1, page_size=10)
        print(f"✅ 获取翻译历史成功:")
        print(f"   总记录数: {result['total']}")
        print(f"   总页数: {result['total_pages']}")
        print(f"   当前页: {result['current_page']}")
        print(f"   当前页记录数: {len(result['translations'])}")
        
        for i, translation in enumerate(result['translations'][:3]):  # 只显示前3条
            print(f"   记录 {i+1}:")
            print(f"     ID: {translation['id']}")
            print(f"     状态: {translation['status']}")
            print(f"     字符数: {translation['total_characters']}")
            print(f"     费用: {translation['cost']} 元")
            print(f"     创建时间: {translation['created_at']}")
        
        return result
    except Exception as e:
        print(f"❌ 获取翻译历史失败: {e}")
        return None

async def test_pricing_calculation():
    """测试收费计算"""
    print("\n🧪 测试收费计算...")
    
    test_cases = [
        {"chars": 1000, "desc": "1000字符 (小文档)"},
        {"chars": 5000, "desc": "5000字符 (边界值)"},
        {"chars": 8000, "desc": "8000字符 (中等文档)"},
        {"chars": 15000, "desc": "15000字符 (大文档)"},
        {"chars": 300000, "desc": "300000字符 (批量优惠)"},
    ]
    
    for case in test_cases:
        cost_info = mingdao_full_service._calculate_translation_cost(case["chars"], "免费用户")
        print(f"   {case['desc']}: {cost_info['cost']} 元 ({cost_info['payment_type']})")

async def test_user_quota_check():
    """测试用户配额检查"""
    print("\n🧪 测试用户配额检查...")
    
    # 模拟用户数据
    mock_user = {
        "id": "test_user_id",
        "username": "testuser",
        "balance": 50.0,
        "total_quota": 10000,
        "used_quota": 2000,
        "monthly_quota": 100000,
        "monthly_used": 30000
    }
    
    test_cases = [
        {"chars": 1000, "cost": 5, "desc": "1000字符，5元"},
        {"chars": 8000, "cost": 10, "desc": "8000字符，10元"},
        {"chars": 80000, "cost": 80, "desc": "80000字符，80元 (超出余额)"},
    ]
    
    for case in test_cases:
        can_translate, message = await mingdao_full_service._check_user_quota(
            mock_user, case["chars"], case["cost"]
        )
        status = "✅ 可以翻译" if can_translate else "❌ 不能翻译"
        print(f"   {case['desc']}: {status} - {message}")

async def main():
    """主测试函数"""
    print("🚀 开始测试明道云服务...")
    
    # 测试收费计算
    await test_pricing_calculation()
    
    # 测试用户配额检查
    await test_user_quota_check()
    
    # 测试用户认证（使用现有用户）
    user = await test_authenticate_user()
    
    if user:
        # 测试获取翻译历史
        await test_get_translation_history(user["id"])
    
    # 注意：创建用户测试可能会因为用户名重复而失败，这是正常的
    # user_id = await test_create_user()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
