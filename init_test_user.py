"""
初始化测试用户脚本
确保测试用户有无限配额
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_async_db_session
from app.models.user import User, UserRole
from app.services.auth_service import get_password_hash
from sqlalchemy import select

async def init_test_user():
    """初始化测试用户"""
    print("🔧 初始化测试用户")
    print("=" * 50)
    
    async with get_async_db_session() as db:
        try:
            # 检查测试用户是否存在
            result = await db.execute(
                select(User).where(User.username == 'testuser')
            )
            test_user = result.scalar_one_or_none()
            
            if test_user:
                # 用户存在，更新配额
                print(f"✅ 找到测试用户 (ID: {test_user.id})")
                print(f"   当前配额: {test_user.translation_quota:,}")
                print(f"   已使用: {test_user.used_quota:,}")
                
                # 设置无限配额并重置使用量
                test_user.translation_quota = 1000000000  # 10亿字符
                test_user.used_quota = 0
                
                await db.commit()
                print("✅ 测试用户配额已更新为无限制")
                
            else:
                # 用户不存在，创建新用户
                print("🔍 测试用户不存在，正在创建...")
                
                hashed_password = get_password_hash("testpass123")
                test_user = User(
                    username="testuser",
                    email="<EMAIL>",
                    hashed_password=hashed_password,
                    full_name="Test User",
                    role=UserRole.USER,
                    is_active=True,
                    translation_quota=1000000000,  # 无限配额
                    used_quota=0
                )
                
                db.add(test_user)
                await db.commit()
                await db.refresh(test_user)
                
                print(f"✅ 测试用户创建成功 (ID: {test_user.id})")
                print("   用户名: testuser")
                print("   密码: testpass123")
                print("   配额: 无限制")
            
            # 验证最终状态
            print(f"\n📊 测试用户最终状态:")
            print(f"   用户名: {test_user.username}")
            print(f"   邮箱: {test_user.email}")
            print(f"   配额: {test_user.translation_quota:,} 字符")
            print(f"   已使用: {test_user.used_quota:,} 字符")
            print(f"   剩余: {test_user.translation_quota - test_user.used_quota:,} 字符")
            print(f"   状态: {'激活' if test_user.is_active else '未激活'}")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            import traceback
            traceback.print_exc()

async def check_all_users():
    """检查所有用户的配额状态"""
    print("\n👥 所有用户配额状态:")
    print("=" * 50)
    
    async with get_async_db_session() as db:
        try:
            result = await db.execute(
                select(User).order_by(User.id)
            )
            users = result.scalars().all()
            
            if not users:
                print("   没有用户")
                return
            
            for user in users:
                remaining = user.translation_quota - user.used_quota
                print(f"👤 {user.username}:")
                print(f"   配额: {user.translation_quota:,} 字符")
                print(f"   已用: {user.used_quota:,} 字符")
                print(f"   剩余: {remaining:,} 字符")
                print(f"   状态: {'激活' if user.is_active else '未激活'}")
                print()
                
        except Exception as e:
            print(f"❌ 检查用户失败: {e}")

async def main():
    """主函数"""
    print("🚀 测试用户初始化工具")
    print("=" * 60)
    
    # 初始化测试用户
    await init_test_user()
    
    # 检查所有用户
    await check_all_users()
    
    print("🎉 初始化完成！")
    print("\n💡 现在可以使用以下账号登录:")
    print("   用户名: testuser")
    print("   密码: testpass123")
    print("   配额: 无限制")

if __name__ == "__main__":
    asyncio.run(main())
