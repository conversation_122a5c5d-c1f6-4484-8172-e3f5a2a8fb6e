"""
简化版明道云API路由
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/simple-mingdao", tags=["简化明道云API"])

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "simple-mingdao-api",
        "message": "明道云API服务正常运行"
    }

@router.post("/test")
async def test_endpoint():
    """测试端点"""
    return {
        "success": True,
        "message": "明道云API测试成功",
        "data": {
            "timestamp": "2024-01-01 12:00:00",
            "version": "1.0.0"
        }
    }

@router.get("/pricing")
async def get_pricing_rules():
    """获取收费规则"""
    return {
        "success": True,
        "pricing_rules": {
            "monthly": {
                "name": "月付套餐",
                "price": 20,
                "quota": 100000,
                "duration_days": 30,
                "description": "20元/月，10万字额度"
            },
            "per_translation_small": {
                "name": "按次收费-小文档",
                "min_chars": 1,
                "max_chars": 5000,
                "price": 5,
                "description": "5000字以内5元一次"
            },
            "per_translation_large": {
                "name": "按次收费-大文档",
                "min_chars": 5001,
                "price_per_5k": 5,
                "description": "超过5000字，每5000字5元"
            },
            "bulk_discount": {
                "name": "批量优惠",
                "min_chars": 300000,
                "price": 100,
                "description": "30万字100元"
            }
        }
    }

@router.post("/calculate-cost")
async def calculate_cost(char_count: int):
    """计算翻译费用"""
    try:
        if char_count <= 0:
            raise HTTPException(status_code=400, detail="字符数必须大于0")
        
        if char_count <= 5000:
            cost = 5
            payment_type = "按次付费"
            description = f"{char_count}字符，按次收费"
        elif char_count >= 300000:
            cost = 100
            payment_type = "批量优惠"
            description = f"{char_count}字符，批量优惠"
        else:
            # 超过5000字，每5000字5元
            cost = ((char_count - 1) // 5000 + 1) * 5
            payment_type = "按次付费"
            description = f"{char_count}字符，分段收费"
        
        return {
            "success": True,
            "char_count": char_count,
            "cost": cost,
            "payment_type": payment_type,
            "description": description
        }
        
    except Exception as e:
        logger.error(f"计算费用失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
