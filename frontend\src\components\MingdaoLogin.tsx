import React, { useState } from 'react';
import { User, Lock, LogIn, Eye, EyeOff, CheckCircle, AlertCircle } from 'lucide-react';
import { authenticateMingdaoUser } from '../services/mingdaoApi';
import './MingdaoLogin.css';

interface LoginFormData {
  username: string;
  password: string;
}

interface MingdaoLoginProps {
  onLoginSuccess?: (userData: any) => void;
  onClose?: () => void;
  onSwitchToRegister?: () => void;
}

const MingdaoLogin: React.FC<MingdaoLoginProps> = ({ 
  onLoginSuccess, 
  onClose, 
  onSwitchToRegister 
}) => {
  const [formData, setFormData] = useState<LoginFormData>({
    username: '',
    password: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<LoginFormData>>({});
  const [loginResult, setLoginResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Partial<LoginFormData> = {};

    if (!formData.username.trim()) {
      newErrors.username = '请输入用户名';
    }

    if (!formData.password) {
      newErrors.password = '请输入密码';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理输入变化
  const handleInputChange = (field: keyof LoginFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // 明道云用户认证
  const authenticateUser = async (): Promise<{ success: boolean; message: string; user?: any }> => {
    try {
      console.log('🔐 开始真实的明道云用户认证:', formData.username);

      // 调用真实的明道云API进行用户认证
      const apiResult = await authenticateMingdaoUser({
        username: formData.username,
        password: formData.password
      });

      if (apiResult.success && apiResult.data) {
        console.log('✅ 明道云登录成功:', apiResult.data);

        return {
          success: true,
          message: '登录成功',
          user: apiResult.data
        };
      } else {
        console.error('❌ 明道云登录失败:', apiResult.error_msg);

        return {
          success: false,
          message: apiResult.error_msg || '登录失败'
        };
      }

    } catch (error) {
      console.error('❌ 用户认证过程发生错误:', error);

      // 如果是跨域错误，提供解决方案
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          success: false,
          message: '网络连接失败，可能是跨域问题。请检查网络连接或联系管理员。'
        };
      } else {
        return {
          success: false,
          message: `登录失败: ${error}`
        };
      }
    }
  };

  // 处理登录提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setLoginResult(null);

    try {
      const result = await authenticateUser();
      setLoginResult(result);

      if (result.success && result.user) {
        onLoginSuccess?.(result.user);
        
        // 1.5秒后自动关闭
        setTimeout(() => {
          onClose?.();
        }, 1500);
      }
    } catch (error) {
      setLoginResult({
        success: false,
        message: '登录过程中发生错误，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 快速登录（演示用）
  const handleQuickLogin = (username: string, password: string) => {
    setFormData({ username, password });
    setErrors({});
  };

  return (
    <div className="mingdao-login">
      <div className="login-header">
        <h2>
          <LogIn size={24} />
          明道云账号登录
        </h2>
        <p>登录您的明道云翻译账号</p>
      </div>

      {loginResult && (
        <div className={`login-result ${loginResult.success ? 'success' : 'error'}`}>
          {loginResult.success ? (
            <CheckCircle size={20} />
          ) : (
            <AlertCircle size={20} />
          )}
          <span>{loginResult.message}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="login-form">
        {/* 用户名 */}
        <div className="form-group">
          <label htmlFor="username">
            <User size={16} />
            用户名
          </label>
          <input
            id="username"
            type="text"
            value={formData.username}
            onChange={(e) => handleInputChange('username', e.target.value)}
            placeholder="请输入用户名"
            className={errors.username ? 'error' : ''}
            disabled={isLoading}
            autoComplete="username"
          />
          {errors.username && <span className="error-message">{errors.username}</span>}
        </div>

        {/* 密码 */}
        <div className="form-group">
          <label htmlFor="password">
            <Lock size={16} />
            密码
          </label>
          <div className="password-input">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="请输入密码"
              className={errors.password ? 'error' : ''}
              disabled={isLoading}
              autoComplete="current-password"
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {errors.password && <span className="error-message">{errors.password}</span>}
        </div>

        {/* 提交按钮 */}
        <div className="form-actions">
          <button
            type="submit"
            className="login-btn"
            disabled={isLoading || loginResult?.success}
          >
            {isLoading ? (
              <>
                <div className="loading-spinner"></div>
                登录中...
              </>
            ) : loginResult?.success ? (
              <>
                <CheckCircle size={16} />
                登录成功
              </>
            ) : (
              <>
                <LogIn size={16} />
                登录
              </>
            )}
          </button>

          {onClose && (
            <button
              type="button"
              className="cancel-btn"
              onClick={onClose}
              disabled={isLoading}
            >
              取消
            </button>
          )}
        </div>
      </form>

      {/* 演示账号 */}
      <div className="demo-accounts">
        <h4>演示账号</h4>
        <div className="demo-account-list">
          <div className="demo-account">
            <div className="account-info">
              <strong>testuser</strong>
              <span>密码: testpass123</span>
              <small>余额: ¥50, 配额: 8000字符</small>
            </div>
            <button
              type="button"
              className="quick-login-btn"
              onClick={() => handleQuickLogin('testuser', 'testpass123')}
              disabled={isLoading}
            >
              快速登录
            </button>
          </div>

          <div className="demo-account">
            <div className="account-info">
              <strong>demouser</strong>
              <span>密码: demo123</span>
              <small>余额: ¥100, 配额: 4000字符</small>
            </div>
            <button
              type="button"
              className="quick-login-btn"
              onClick={() => handleQuickLogin('demouser', 'demo123')}
              disabled={isLoading}
            >
              快速登录
            </button>
          </div>
        </div>
      </div>

      {/* 注册链接 */}
      {onSwitchToRegister && (
        <div className="register-link">
          <p>
            还没有账号？
            <button
              type="button"
              className="link-btn"
              onClick={onSwitchToRegister}
              disabled={isLoading}
            >
              立即注册
            </button>
          </p>
        </div>
      )}

      {/* 登录说明 */}
      <div className="login-info">
        <h4>登录后可享受</h4>
        <ul>
          <li>🔍 查看详细的翻译历史</li>
          <li>📊 实时配额和余额管理</li>
          <li>💳 灵活的付费方式选择</li>
          <li>📱 跨设备同步翻译记录</li>
        </ul>
      </div>
    </div>
  );
};

export default MingdaoLogin;
