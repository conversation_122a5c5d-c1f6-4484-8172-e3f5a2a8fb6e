import React, { useState } from 'react';
import { LogIn, UserPlus, FileText, Globe, Zap, Shield } from 'lucide-react';
import LoginModal from './LoginModal';
import RegisterModal from './RegisterModal';
import { UserAccount } from '../types/auth';

interface LoginPageProps {
  onLoginSuccess: (user: UserAccount) => void;
  onRegisterSuccess: (user: UserAccount) => void;
}

const LoginPage: React.FC<LoginPageProps> = ({
  onLoginSuccess,
  onRegisterSuccess
}) => {
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showRegisterModal, setShowRegisterModal] = useState(false);

  // 调试函数
  const handleLoginClick = () => {
    console.log('登录按钮被点击，当前状态:', { showLoginModal, showRegisterModal });
    setShowLoginModal(true);
    console.log('设置showLoginModal为true');
  };

  const handleRegisterClick = () => {
    console.log('注册按钮被点击，当前状态:', { showLoginModal, showRegisterModal });
    setShowRegisterModal(true);
    console.log('设置showRegisterModal为true');
  };

  // 监听状态变化
  React.useEffect(() => {
    console.log('LoginPage状态变化:', { showLoginModal, showRegisterModal });
  }, [showLoginModal, showRegisterModal]);

  return (
    <div className="login-page">
      {/* 测试按钮 - 用于验证React事件是否正常 */}
      <button
        onClick={() => alert('React事件正常工作！')}
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          zIndex: 9999,
          background: 'red',
          color: 'white',
          padding: '10px',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        测试React事件
      </button>
      {/* 头部导航 */}
      <header className="login-header">
        <div className="container">
          <div className="logo">
            <FileText size={32} />
            <h1>智能翻译系统</h1>
          </div>
          <div className="header-actions">
            <button
              className="btn btn-outline"
              onClick={handleLoginClick}
            >
              <LogIn size={18} />
              登录
            </button>
            <button
              className="btn btn-primary"
              onClick={handleRegisterClick}
            >
              <UserPlus size={18} />
              注册
            </button>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="login-main">
        {/* 英雄区域 */}
        <section className="hero-section">
          <div className="container">
            <div className="hero-content">
              <div className="hero-text">
                <h2>专业的文档翻译服务</h2>
                <p className="hero-description">
                  支持多种文档格式，提供高质量的AI翻译服务。
                  快速、准确、安全的文档翻译解决方案。
                </p>
                <div className="hero-actions">
                  <button
                    className="btn btn-primary btn-large"
                    onClick={handleRegisterClick}
                  >
                    <UserPlus size={20} />
                    立即开始
                  </button>
                  <button
                    className="btn btn-outline btn-large"
                    onClick={handleLoginClick}
                  >
                    <LogIn size={20} />
                    已有账号
                  </button>
                </div>
              </div>
              <div className="hero-image">
                <div className="feature-preview">
                  <div className="preview-card">
                    <Globe size={48} />
                    <h3>多语言支持</h3>
                    <p>支持中文、英语、日语、韩语等多种语言互译</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* 功能特色 */}
        <section className="features-section">
          <div className="container">
            <h3>为什么选择我们？</h3>
            <div className="features-grid">
              <div className="feature-card">
                <Zap size={32} />
                <h4>快速翻译</h4>
                <p>基于Azure AI的高速翻译引擎，几分钟内完成文档翻译</p>
              </div>
              <div className="feature-card">
                <FileText size={32} />
                <h4>多格式支持</h4>
                <p>支持Word、PDF、Excel、PowerPoint等多种文档格式</p>
              </div>
              <div className="feature-card">
                <Shield size={32} />
                <h4>安全可靠</h4>
                <p>企业级安全保障，文档传输和存储全程加密</p>
              </div>
            </div>
          </div>
        </section>

        {/* 使用流程 */}
        <section className="process-section">
          <div className="container">
            <h3>简单三步，完成翻译</h3>
            <div className="process-steps">
              <div className="step">
                <div className="step-number">1</div>
                <h4>上传文档</h4>
                <p>选择需要翻译的文档文件</p>
              </div>
              <div className="step">
                <div className="step-number">2</div>
                <h4>选择语言</h4>
                <p>设置源语言和目标语言</p>
              </div>
              <div className="step">
                <div className="step-number">3</div>
                <h4>获取结果</h4>
                <p>下载翻译完成的文档</p>
              </div>
            </div>
          </div>
        </section>

        {/* 测试账号提示 */}
        <section className="demo-section">
          <div className="container">
            <div className="demo-card">
              <h4>🎯 快速体验</h4>
              <p>使用测试账号立即体验翻译功能：</p>
              <div className="demo-credentials">
                <div className="credential">
                  <strong>用户名：</strong> testuser
                </div>
                <div className="credential">
                  <strong>密码：</strong> testpass123
                </div>
              </div>
              <button
                className="btn btn-success"
                onClick={handleLoginClick}
              >
                使用测试账号登录
              </button>
            </div>
          </div>
        </section>
      </main>

      {/* 页脚 */}
      <footer className="login-footer">
        <div className="container">
          <p>&copy; 2024 智能翻译系统. 保留所有权利.</p>
        </div>
      </footer>

      {/* 登录和注册模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLoginSuccess={(user) => {
          setShowLoginModal(false);
          onLoginSuccess(user);
        }}
        onSwitchToRegister={() => {
          setShowLoginModal(false);
          setShowRegisterModal(true);
        }}
      />

      <RegisterModal
        isOpen={showRegisterModal}
        onClose={() => setShowRegisterModal(false)}
        onRegisterSuccess={(user) => {
          setShowRegisterModal(false);
          onRegisterSuccess(user);
        }}
        onSwitchToLogin={() => {
          setShowRegisterModal(false);
          setShowLoginModal(true);
        }}
      />
    </div>
  );
};

export default LoginPage;
