"""
创建HTML预览功能
"""
import sqlite3
import html
from datetime import datetime

def create_html_preview(translation_id, output_path):
    """创建翻译结果的HTML预览"""
    try:
        # 从数据库获取翻译结果
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT th.translated_text, th.original_text, th.source_language, 
                   th.target_language, th.translated_characters, th.confidence_score,
                   uf.original_filename, th.created_at, th.completed_at
            FROM translation_histories th
            JOIN uploaded_files uf ON th.file_id = uf.id
            WHERE th.id = ? AND th.status = 'COMPLETED'
        ''', (translation_id,))
        
        result = cursor.fetchone()
        if not result:
            print(f"未找到翻译ID {translation_id} 的完成记录")
            return False
        
        (translated_text, original_text, source_lang, target_lang, 
         char_count, confidence, filename, created_at, completed_at) = result
        conn.close()
        
        # 语言名称映射
        lang_names = {
            'zh': '中文', 'en': '英语', 'ja': '日语', 'ko': '韩语',
            'fr': '法语', 'de': '德语', 'es': '西班牙语'
        }
        
        # 创建HTML内容
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译预览 - {html.escape(filename)}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }}
        
        .meta-info {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }}
        
        .meta-item {{
            text-align: center;
        }}
        
        .meta-label {{
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }}
        
        .meta-value {{
            font-size: 16px;
            font-weight: 600;
            color: #495057;
        }}
        
        .content {{
            padding: 30px;
        }}
        
        .section {{
            margin-bottom: 40px;
        }}
        
        .section-title {{
            font-size: 20px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }}
        
        .text-content {{
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            white-space: pre-wrap;
            font-size: 14px;
            line-height: 1.8;
            max-height: 400px;
            overflow-y: auto;
        }}
        
        .dual-view {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }}
        
        .original {{
            border-left-color: #28a745;
        }}
        
        .translated {{
            border-left-color: #007bff;
        }}
        
        .stats {{
            display: flex;
            justify-content: space-around;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }}
        
        .stat-item {{
            text-align: center;
        }}
        
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            display: block;
        }}
        
        .stat-label {{
            font-size: 12px;
            opacity: 0.8;
        }}
        
        @media (max-width: 768px) {{
            .dual-view {{
                grid-template-columns: 1fr;
            }}
            
            .meta-info {{
                grid-template-columns: 1fr;
            }}
        }}
        
        .print-btn {{
            position: fixed;
            top: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }}
        
        .print-btn:hover {{
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }}
        
        @media print {{
            .print-btn {{ display: none; }}
            body {{ background: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <button class="print-btn" onclick="window.print()">🖨️ 打印</button>
    
    <div class="container">
        <div class="header">
            <h1>📄 翻译文档预览</h1>
            <p>{html.escape(filename)}</p>
        </div>
        
        <div class="meta-info">
            <div class="meta-item">
                <div class="meta-label">翻译方向</div>
                <div class="meta-value">{lang_names.get(source_lang, source_lang)} → {lang_names.get(target_lang, target_lang)}</div>
            </div>
            <div class="meta-item">
                <div class="meta-label">字符数</div>
                <div class="meta-value">{char_count:,}</div>
            </div>
            <div class="meta-item">
                <div class="meta-label">置信度</div>
                <div class="meta-value">{int(confidence * 100) if confidence else 'N/A'}%</div>
            </div>
            <div class="meta-item">
                <div class="meta-label">完成时间</div>
                <div class="meta-value">{completed_at or 'N/A'}</div>
            </div>
        </div>
        
        <div class="content">
"""

        # 如果有原文，显示双栏对比
        if original_text and original_text.strip():
            html_content += f"""
            <div class="dual-view">
                <div class="section">
                    <div class="section-title">🔤 原文内容</div>
                    <div class="text-content original">{html.escape(original_text)}</div>
                </div>
                
                <div class="section">
                    <div class="section-title">🌍 翻译结果</div>
                    <div class="text-content translated">{html.escape(translated_text or '翻译内容为空')}</div>
                </div>
            </div>
"""
        else:
            # 只显示翻译结果
            html_content += f"""
            <div class="section">
                <div class="section-title">🌍 翻译结果</div>
                <div class="text-content">{html.escape(translated_text or '翻译内容为空')}</div>
            </div>
"""

        # 添加统计信息
        original_chars = len(original_text) if original_text else 0
        translated_chars = len(translated_text) if translated_text else 0
        
        html_content += f"""
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">{original_chars:,}</span>
                    <span class="stat-label">原文字符</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{translated_chars:,}</span>
                    <span class="stat-label">译文字符</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{int(confidence * 100) if confidence else 0}%</span>
                    <span class="stat-label">翻译置信度</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加一些交互功能
        document.addEventListener('DOMContentLoaded', function() {{
            // 自动调整文本区域高度
            const textAreas = document.querySelectorAll('.text-content');
            textAreas.forEach(area => {{
                if (area.scrollHeight > 400) {{
                    area.style.maxHeight = '400px';
                    area.style.overflowY = 'auto';
                }}
            }});
        }});
    </script>
</body>
</html>
"""
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML预览已生成：{output_path}")
        return True
        
    except Exception as e:
        print(f"生成HTML预览失败：{e}")
        import traceback
        traceback.print_exc()
        return False

def test_html_preview():
    """测试HTML预览功能"""
    print("🔍 测试HTML预览功能")
    print("=" * 50)
    
    # 获取最新的完成翻译
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, translated_characters
        FROM translation_histories 
        WHERE status = 'COMPLETED'
        ORDER BY id DESC 
        LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if not result:
        print("❌ 没有找到已完成的翻译记录")
        return
    
    translation_id, char_count = result
    print(f"📄 使用翻译ID: {translation_id} ({char_count} 字符)")
    
    # 生成HTML预览
    output_path = f"translation_preview_{translation_id}.html"
    success = create_html_preview(translation_id, output_path)
    
    if success:
        print(f"✅ HTML预览生成成功: {output_path}")
        print(f"🌐 在浏览器中打开: file://{os.path.abspath(output_path)}")
    else:
        print("❌ HTML预览生成失败")
    
    conn.close()

if __name__ == "__main__":
    import os
    test_html_preview()
