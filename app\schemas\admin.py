"""
管理员相关的 Pydantic 模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class SystemStats(BaseModel):
    """系统统计模型"""
    total_users: int
    active_users: int
    total_files: int
    total_translations: int
    completed_translations: int
    failed_translations: int
    total_terms: int
    disk_usage: Dict[str, Any]
    memory_usage: Dict[str, Any]
    cpu_usage: float


class UserStats(BaseModel):
    """用户统计模型"""
    id: int
    username: str
    email: str
    role: str
    is_active: bool
    translation_quota: int
    used_quota: int
    files_count: int
    translations_count: int
    last_login: Optional[str]
    created_at: str


class LogEntry(BaseModel):
    """日志条目模型"""
    timestamp: str
    level: str
    message: str
    module: str
    function: str
    line: int


class LogResponse(BaseModel):
    """日志响应模型"""
    logs: List[LogEntry]
    total: int
    page: int
    page_size: int


class SystemHealth(BaseModel):
    """系统健康状态模型"""
    status: str  # "healthy", "warning", "critical"
    database_status: str
    redis_status: Optional[str]
    disk_space: Dict[str, Any]
    memory_usage: Dict[str, Any]
    cpu_usage: float
    active_connections: int
    uptime: int  # 运行时间(秒)
    last_check: str


class ErrorReport(BaseModel):
    """错误报告模型"""
    error_type: str
    error_message: str
    stack_trace: Optional[str]
    user_id: Optional[int]
    endpoint: Optional[str]
    timestamp: str
    count: int = 1


class ErrorStats(BaseModel):
    """错误统计模型"""
    total_errors: int
    error_types: Dict[str, int]
    recent_errors: List[ErrorReport]
    error_rate: float  # 错误率 (每小时)


class BackupInfo(BaseModel):
    """备份信息模型"""
    backup_id: str
    filename: str
    size: int
    created_at: str
    status: str  # "completed", "failed", "in_progress"


class MaintenanceTask(BaseModel):
    """维护任务模型"""
    task_id: str
    task_name: str
    description: str
    status: str  # "pending", "running", "completed", "failed"
    progress: float = Field(ge=0.0, le=1.0)
    started_at: Optional[str]
    completed_at: Optional[str]
    error_message: Optional[str]
