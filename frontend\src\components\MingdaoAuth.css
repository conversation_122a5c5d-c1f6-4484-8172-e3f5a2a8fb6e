.mingdao-auth-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mingdao-auth-modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 模态框头部 */
.auth-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.auth-modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 模式切换标签 */
.auth-mode-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.mode-tab {
  flex: 1;
  padding: 15px 20px;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.mode-tab:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.mode-tab.active {
  background: white;
  color: #667eea;
}

.mode-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

/* 内容区域 */
.auth-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 重置子组件的样式 */
.auth-content .mingdao-login,
.auth-content .mingdao-register {
  box-shadow: none;
  border-radius: 0;
  margin: 0;
  max-width: none;
}

/* 底部信息 */
.auth-modal-footer {
  padding: 20px 25px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.footer-info p {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 0.9rem;
  font-weight: 600;
}

.feature-highlights {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.feature-highlights span {
  font-size: 0.8rem;
  color: #666;
  background: white;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mingdao-auth-overlay {
    padding: 10px;
  }
  
  .mingdao-auth-modal {
    max-height: 95vh;
    border-radius: 15px;
  }
  
  .auth-modal-header {
    padding: 15px 20px;
  }
  
  .auth-modal-title {
    font-size: 1.1rem;
  }
  
  .close-btn {
    width: 32px;
    height: 32px;
  }
  
  .mode-tab {
    padding: 12px 15px;
    font-size: 0.9rem;
  }
  
  .auth-modal-footer {
    padding: 15px 20px;
  }
  
  .feature-highlights {
    gap: 8px;
  }
  
  .feature-highlights span {
    font-size: 0.75rem;
    padding: 3px 6px;
  }
}

@media (max-width: 480px) {
  .mingdao-auth-overlay {
    padding: 5px;
  }
  
  .mingdao-auth-modal {
    border-radius: 10px;
  }
  
  .auth-modal-header {
    padding: 12px 15px;
  }
  
  .auth-modal-title {
    font-size: 1rem;
  }
  
  .mode-tab {
    padding: 10px 12px;
    font-size: 0.85rem;
  }
  
  .auth-modal-footer {
    padding: 12px 15px;
  }
  
  .footer-info p {
    font-size: 0.8rem;
  }
  
  .feature-highlights {
    flex-direction: column;
    gap: 5px;
  }
  
  .feature-highlights span {
    text-align: center;
  }
}

/* 滚动条样式 */
.auth-content::-webkit-scrollbar {
  width: 6px;
}

.auth-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.auth-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.auth-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 焦点状态 */
.close-btn:focus,
.mode-tab:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* 动画增强 */
.mode-tab {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mode-tab.active {
  transform: translateY(-1px);
}

/* 深色模式支持（可选） */
@media (prefers-color-scheme: dark) {
  .mingdao-auth-modal {
    background: #1a1a1a;
    color: #e0e0e0;
  }
  
  .auth-mode-tabs {
    background: #2a2a2a;
    border-bottom-color: #404040;
  }
  
  .mode-tab {
    color: #b0b0b0;
  }
  
  .mode-tab:hover {
    background: rgba(102, 126, 234, 0.2);
    color: #8fa4f3;
  }
  
  .mode-tab.active {
    background: #1a1a1a;
    color: #8fa4f3;
  }
  
  .auth-modal-footer {
    background: #2a2a2a;
    border-top-color: #404040;
  }
  
  .footer-info p {
    color: #e0e0e0;
  }
  
  .feature-highlights span {
    background: #333;
    border-color: #555;
    color: #b0b0b0;
  }
}
