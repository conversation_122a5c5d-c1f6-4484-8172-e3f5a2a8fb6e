#!/usr/bin/env python3
"""
测试翻译设置API
"""

import requests
import json

def test_translation_api():
    """测试翻译设置API"""
    
    print("🧪 测试翻译设置API")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    test_user_rowid = "e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa"
    
    test_settings = {
        "paragraph": {
            "font_family": "微软雅黑",
            "font_size": 12,
            "bold": False,
            "italic": False,
            "underline": False,
            "text_align": "inherit",
            "color": "#000000"
        },
        "table": {
            "font_family": "宋体",
            "font_size": 8,
            "bold": True,
            "italic": False,
            "underline": False,
            "text_align": "center",
            "color": "#333333"
        },
        "header": {
            "font_family": "黑体",
            "font_size": 14,
            "bold": True,
            "italic": False,
            "underline": True,
            "text_align": "center",
            "color": "#000080"
        },
        "enable_paragraph": True,
        "enable_table": True,
        "enable_header": True
    }
    
    # 1. 测试服务器是否运行
    print("\n1️⃣ 测试服务器连接")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器已启动")
        print("   启动命令: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return
    
    # 2. 测试保存翻译设置
    print("\n2️⃣ 测试保存翻译设置")
    try:
        response = requests.post(
            f"{base_url}/api/translation-settings/{test_user_rowid}",
            json=test_settings,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 保存成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 保存失败:")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，服务器可能未启动")
    except Exception as e:
        print(f"❌ 保存测试失败: {e}")
    
    # 3. 测试读取翻译设置
    print("\n3️⃣ 测试读取翻译设置")
    try:
        response = requests.get(
            f"{base_url}/api/translation-settings/{test_user_rowid}",
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 读取成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 读取失败:")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，服务器可能未启动")
    except Exception as e:
        print(f"❌ 读取测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎊 测试完成！")
    print("\n💡 如果测试成功，请检查明道云表单 yhfygssz 是否有新记录")

if __name__ == "__main__":
    test_translation_api()
