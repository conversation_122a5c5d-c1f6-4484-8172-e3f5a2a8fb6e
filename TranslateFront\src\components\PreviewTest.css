.preview-test {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 20px;
  margin-bottom: 20px;
}

.preview-test-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.preview-test-header p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #6c757d;
}

.test-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #dee2e6;
}

.test-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.test-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.test-section h5 {
  margin: 12px 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.test-item {
  margin-bottom: 8px;
  font-size: 14px;
}

.test-item strong {
  color: #495057;
  margin-right: 8px;
}

.preview-link {
  color: #007bff;
  text-decoration: none;
  word-break: break-all;
}

.preview-link:hover {
  text-decoration: underline;
}

.file-analysis {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.analysis-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-item strong {
  min-width: 120px;
  color: #495057;
}

.status-yes {
  color: #28a745;
  font-weight: 500;
}

.status-no {
  color: #dc3545;
  font-weight: 500;
}

.file-list {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 14px;
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-item.processed {
  border-left: 4px solid #28a745;
  background-color: #f8fff9;
}

.file-size {
  color: #6c757d;
  font-size: 12px;
}

.file-status {
  color: #28a745;
  font-size: 11px;
  font-weight: 500;
  background-color: #d4edda;
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: auto;
}

.info-list {
  margin: 0;
  padding-left: 20px;
}

.info-list li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #6c757d;
}

.info-list code {
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #495057;
}
