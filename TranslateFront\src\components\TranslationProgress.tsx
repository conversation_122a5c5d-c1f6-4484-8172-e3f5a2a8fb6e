import React, { useState, useEffect } from 'react';
import { Progress, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import './TranslationProgress.css';

interface TranslationProgressProps {
  translationId: number;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

interface ProgressData {
  current: number;
  total: number;
  percentage: number;
  message: string;
  completed?: boolean;
  timestamp: string;
}

const TranslationProgress: React.FC<TranslationProgressProps> = ({
  translationId,
  onComplete,
  onError
}) => {
  const [progress, setProgress] = useState<ProgressData>({
    current: 0,
    total: 100,
    percentage: 0,
    message: '准备开始翻译...',
    timestamp: new Date().toISOString()
  });
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 创建WebSocket连接
    const wsUrl = `ws://localhost:8000/api/translate/progress/${translationId}`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      setIsConnected(true);
      setError(null);
      console.log('WebSocket连接已建立');
    };

    ws.onmessage = (event) => {
      try {
        const data: ProgressData = JSON.parse(event.data);
        setProgress(data);
        
        if (data.completed && onComplete) {
          onComplete();
        }
      } catch (err) {
        console.error('解析进度数据失败:', err);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      setError('连接错误');
      setIsConnected(false);
      if (onError) {
        onError('WebSocket连接失败');
      }
    };

    ws.onclose = () => {
      setIsConnected(false);
      console.log('WebSocket连接已关闭');
    };

    // 清理函数
    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [translationId, onComplete, onError]);

  const getStatusIcon = () => {
    if (error) {
      return <AlertCircle className="status-icon error" size={20} />;
    }
    if (progress.completed) {
      return <CheckCircle className="status-icon success" size={20} />;
    }
    return <Clock className="status-icon processing" size={20} />;
  };

  const getStatusText = () => {
    if (error) return '翻译失败';
    if (progress.completed) return '翻译完成';
    if (!isConnected) return '连接中...';
    return '翻译中';
  };

  return (
    <div className="translation-progress">
      <div className="progress-header">
        <div className="status-info">
          {getStatusIcon()}
          <span className="status-text">{getStatusText()}</span>
        </div>
        <div className="connection-status">
          <div className={`connection-indicator ${isConnected ? 'connected' : 'disconnected'}`} />
          <span className="connection-text">
            {isConnected ? '已连接' : '未连接'}
          </span>
        </div>
      </div>

      <div className="progress-content">
        <div className="progress-bar-container">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${progress.percentage}%` }}
            />
          </div>
          <div className="progress-text">
            {progress.percentage.toFixed(1)}%
          </div>
        </div>

        <div className="progress-details">
          <div className="progress-message">{progress.message}</div>
          <div className="progress-stats">
            {progress.total > 0 && (
              <span className="progress-count">
                {progress.current} / {progress.total}
              </span>
            )}
            <span className="progress-time">
              {new Date(progress.timestamp).toLocaleTimeString()}
            </span>
          </div>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <AlertCircle size={16} />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
};

export default TranslationProgress;
