#!/bin/bash

# 翻译文档管理系统 - 自动部署脚本
# 使用方法: ./deploy.sh

echo "🚀 开始部署翻译文档管理系统..."

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+"
    exit 1
fi

# 检查 npm 是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"

# 安装依赖
echo "📦 安装依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

# 构建项目
echo "🔨 构建项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 项目构建失败"
    exit 1
fi

# 检查是否安装了 PM2
if command -v pm2 &> /dev/null; then
    echo "🔄 使用 PM2 重启服务..."
    pm2 stop translate-front 2>/dev/null || true
    pm2 delete translate-front 2>/dev/null || true
    pm2 start server.cjs --name "translate-front"
    pm2 save
    echo "✅ PM2 服务已启动"
else
    echo "⚠️  PM2 未安装，使用普通方式启动..."
    echo "💡 建议安装 PM2: npm install -g pm2"
    
    # 杀死可能存在的进程
    pkill -f "node server.cjs" 2>/dev/null || true
    
    # 后台启动服务
    nohup npm run start > app.log 2>&1 &
    echo "✅ 服务已在后台启动"
fi

echo ""
echo "🎉 部署完成！"
echo "📍 访问地址: http://localhost:3000"
echo "📋 查看日志: tail -f app.log (或 pm2 logs translate-front)"
echo ""
echo "🔧 常用命令:"
echo "  查看状态: pm2 status"
echo "  查看日志: pm2 logs translate-front"
echo "  重启服务: pm2 restart translate-front"
echo "  停止服务: pm2 stop translate-front"
