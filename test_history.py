"""
测试翻译历史记录功能
"""
import asyncio
import httpx
import json

async def test_translation_history():
    """测试翻译历史记录功能"""
    print("🔍 测试翻译历史记录功能")
    print("=" * 50)
    
    # 测试用户登录
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    async with httpx.AsyncClient() as client:
        # 1. 登录获取令牌
        print("1. 用户登录...")
        try:
            response = await client.post("http://localhost:8000/api/v1/auth/login", json=login_data)
            if response.status_code == 200:
                token = response.json()["access_token"]
                print("✅ 登录成功")
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return
        
        # 2. 获取翻译历史
        print("\n2. 获取翻译历史...")
        headers = {"Authorization": f"Bearer {token}"}
        
        try:
            response = await client.get("http://localhost:8000/api/v1/translate/", headers=headers)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                translations = data.get("translations", [])
                print(f"✅ 找到 {len(translations)} 个翻译记录")
                
                print("\n📋 翻译历史详情:")
                for i, translation in enumerate(translations, 1):
                    print(f"\n   {i}. 翻译 ID: {translation['id']}")
                    print(f"      文件名: {translation.get('filename', 'Unknown')}")
                    print(f"      语言对: {translation['source_language']} → {translation['target_language']}")
                    print(f"      状态: {translation['status']}")
                    print(f"      创建时间: {translation['created_at']}")
                    if translation['status'] == 'completed':
                        print(f"      翻译字符数: {translation.get('translated_characters', 0)}")
                        print(f"      置信度: {translation.get('confidence_score', 'N/A')}")
                
                # 3. 测试查看特定翻译结果
                if translations:
                    completed_translations = [t for t in translations if t['status'] == 'completed']
                    if completed_translations:
                        translation_id = completed_translations[0]['id']
                        print(f"\n3. 测试查看翻译结果 (ID: {translation_id})...")
                        
                        try:
                            response = await client.get(
                                f"http://localhost:8000/api/v1/translate/{translation_id}",
                                headers=headers
                            )
                            
                            if response.status_code == 200:
                                result = response.json()
                                translated_text = result.get('translated_text', '')
                                print(f"✅ 获取翻译结果成功")
                                print(f"   结果长度: {len(translated_text)} 字符")
                                print(f"   结果预览: {translated_text[:100]}...")
                            else:
                                print(f"❌ 获取翻译结果失败: {response.status_code}")
                                
                        except Exception as e:
                            print(f"❌ 获取翻译结果错误: {e}")
                        
                        # 4. 测试下载功能
                        print(f"\n4. 测试下载翻译文档 (ID: {translation_id})...")
                        
                        try:
                            response = await client.get(
                                f"http://localhost:8000/api/v1/translate/{translation_id}/download",
                                headers=headers
                            )
                            
                            if response.status_code == 200:
                                content = response.content.decode('utf-8')
                                filename = f"history_download_{translation_id}.txt"
                                
                                with open(filename, 'w', encoding='utf-8') as f:
                                    f.write(content)
                                
                                print(f"✅ 下载成功！文件保存为: {filename}")
                                print(f"   文件大小: {len(content)} 字符")
                            else:
                                print(f"❌ 下载失败: {response.status_code}")
                                
                        except Exception as e:
                            print(f"❌ 下载错误: {e}")
                    else:
                        print("\n⚠️  没有已完成的翻译记录可供测试")
                else:
                    print("\n⚠️  没有翻译历史记录")
                    
            else:
                print(f"❌ 获取翻译历史失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 获取翻译历史错误: {e}")
    
    print("\n🎯 测试总结:")
    print("   - 翻译历史记录 API 测试")
    print("   - 查看特定翻译结果测试")
    print("   - 下载历史翻译文档测试")
    print("   - 前端界面集成测试")

if __name__ == "__main__":
    asyncio.run(test_translation_history())
