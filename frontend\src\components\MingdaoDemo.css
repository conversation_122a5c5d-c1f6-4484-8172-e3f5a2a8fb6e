.mingdao-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 头部介绍 */
.demo-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.header-text h1 {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 0 0 15px 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.header-text p {
  font-size: 1.2rem;
  margin: 0 0 20px 0;
  opacity: 0.9;
  line-height: 1.6;
}

.header-badges {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.badge-success {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.badge-info {
  background: rgba(33, 150, 243, 0.2);
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.badge-warning {
  background: rgba(255, 152, 0, 0.2);
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.header-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* 功能特性 */
.features-section {
  margin-bottom: 40px;
}

.features-section h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.feature-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  border: 2px solid #f0f0f0;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 20px;
}

.feature-card h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.feature-card p {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.6;
}

.feature-benefits {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-benefits li {
  padding: 5px 0;
  color: #555;
  font-size: 0.9rem;
}

.feature-benefits li:before {
  content: "✓";
  color: #4caf50;
  font-weight: bold;
  margin-right: 8px;
}

/* 选项卡 */
.demo-tabs {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  overflow: hidden;
}

.tab-nav {
  display: flex;
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 15px 20px;
  border: none;
  background: transparent;
  color: #666;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab-btn.active {
  background: white;
  color: #667eea;
  border-bottom-color: #667eea;
}

.tab-content {
  padding: 30px;
}

/* 概览内容 */
.overview-content h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.tables-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.table-card {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border: 2px solid #e9ecef;
}

.table-card h4 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #333;
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.table-card p {
  color: #666;
  margin-bottom: 15px;
  font-size: 0.9rem;
  font-family: monospace;
}

.table-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.table-fields span {
  background: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  color: #555;
  border: 1px solid #dee2e6;
}

/* 架构流程 */
.architecture-flow {
  margin-top: 40px;
}

.flow-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  margin-top: 30px;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 150px;
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  margin-bottom: 10px;
}

.step-content h4 {
  color: #333;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.step-content p {
  color: #666;
  font-size: 0.8rem;
  line-height: 1.4;
  margin: 0;
}

.flow-arrow {
  color: #667eea;
  font-size: 1.5rem;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mingdao-demo {
    padding: 15px;
  }
  
  .demo-header {
    padding: 25px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 25px;
    text-align: center;
  }
  
  .header-text h1 {
    font-size: 2rem;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-nav {
    flex-direction: column;
  }
  
  .tab-btn {
    justify-content: flex-start;
    padding: 12px 20px;
  }
  
  .tab-content {
    padding: 20px;
  }
  
  .tables-overview {
    grid-template-columns: 1fr;
  }
  
  .flow-steps {
    flex-direction: column;
    gap: 20px;
  }
  
  .flow-arrow {
    transform: rotate(90deg);
  }
  
  .flow-step {
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .header-badges {
    justify-content: center;
  }
  
  .badge {
    font-size: 0.75rem;
    padding: 6px 10px;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .feature-card {
    padding: 20px;
  }
}
