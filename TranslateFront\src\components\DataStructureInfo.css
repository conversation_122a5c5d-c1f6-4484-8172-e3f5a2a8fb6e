.data-structure-info {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 20px;
  margin-bottom: 20px;
}

.info-header h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.structure-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #dee2e6;
}

.structure-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.structure-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.mapping-table {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  overflow: hidden;
}

.mapping-row {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  padding: 12px;
  border-bottom: 1px solid #dee2e6;
}

.mapping-row:last-child {
  border-bottom: none;
}

.mapping-row.header {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.field-name {
  font-family: 'Courier New', monospace;
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 600;
  color: #495057;
}

.file-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.file-type.original {
  color: #007bff;
}

.file-type.translated {
  color: #28a745;
}

.preview-status {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px;
}

.status-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.warning-icon {
  color: #856404;
  flex-shrink: 0;
  margin-top: 2px;
}

.status-content strong {
  color: #856404;
  display: block;
  margin-bottom: 4px;
}

.status-content p {
  margin: 0;
  color: #856404;
  font-size: 14px;
}

.feature-list {
  margin: 0;
  padding-left: 20px;
}

.feature-list li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #6c757d;
}

.feature-list strong {
  color: #495057;
}

.example-data {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
}

.example-data summary {
  cursor: pointer;
  font-weight: 500;
  color: #495057;
  padding: 4px;
}

.example-data summary:hover {
  color: #007bff;
}

.json-example {
  margin: 12px 0 0 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  color: #495057;
  border: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mapping-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .mapping-row.header {
    display: none;
  }
  
  .mapping-row > span:before {
    font-weight: 600;
    color: #495057;
    display: block;
    margin-bottom: 4px;
  }
  
  .mapping-row > span:nth-child(1):before {
    content: "字段名: ";
  }
  
  .mapping-row > span:nth-child(2):before {
    content: "说明: ";
  }
  
  .mapping-row > span:nth-child(3):before {
    content: "文件类型: ";
  }
}
