"""
管理员相关路由
"""
import time
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_db
from app.schemas.admin import (
    SystemStats, UserStats, LogResponse, LogEntry,
    SystemHealth, ErrorStats
)
from app.schemas.auth import UserCreate, UserAdminUpdate, UserProfile
from app.services.admin_service import AdminService
from app.services.auth_service import AuthService
from app.utils.dependencies import get_admin_user
from app.utils.logger import logger


router = APIRouter()


@router.get("/stats", response_model=SystemStats)
async def get_system_stats(
    current_user = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取系统统计信息"""
    try:
        stats = await AdminService.get_system_stats(db)
        return stats
    except Exception as e:
        logger.error(f"Get system stats error: {e}")
        raise


@router.get("/users", response_model=dict)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=100, description="每页数量"),
    current_user = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取用户列表和统计"""
    try:
        skip = (page - 1) * page_size
        user_stats, total = await AdminService.get_user_stats(db, skip, page_size)
        
        return {
            "users": user_stats,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"Get users error: {e}")
        raise


@router.post("/users", response_model=UserProfile, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    current_user = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """创建用户"""
    try:
        user = await AuthService.create_user(db, user_data)
        
        return UserProfile(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            translation_quota=user.translation_quota,
            used_quota=user.used_quota,
            remaining_quota=user.remaining_quota,
            preferred_source_lang=user.preferred_source_lang,
            preferred_target_lang=user.preferred_target_lang,
            created_at=user.created_at.isoformat() if user.created_at else None,
            last_login=user.last_login.isoformat() if user.last_login else None
        )
    except Exception as e:
        logger.error(f"Create user error: {e}")
        raise


@router.put("/users/{user_id}", response_model=UserProfile)
async def update_user(
    user_id: int,
    user_update: UserAdminUpdate,
    current_user = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """更新用户信息"""
    try:
        user = await AuthService.get_user_by_id(db, user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # 更新用户信息
        if user_update.full_name is not None:
            user.full_name = user_update.full_name
        if user_update.role is not None:
            user.role = user_update.role
        if user_update.translation_quota is not None:
            user.translation_quota = user_update.translation_quota
        if user_update.is_active is not None:
            user.is_active = user_update.is_active
        if user_update.is_verified is not None:
            user.is_verified = user_update.is_verified
        
        await db.commit()
        await db.refresh(user)
        
        logger.info(f"User updated: {user.username} by admin {current_user.username}")
        
        return UserProfile(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            translation_quota=user.translation_quota,
            used_quota=user.used_quota,
            remaining_quota=user.remaining_quota,
            preferred_source_lang=user.preferred_source_lang,
            preferred_target_lang=user.preferred_target_lang,
            created_at=user.created_at.isoformat() if user.created_at else None,
            last_login=user.last_login.isoformat() if user.last_login else None
        )
    except Exception as e:
        logger.error(f"Update user error: {e}")
        raise


@router.get("/health", response_model=SystemHealth)
async def get_system_health(
    current_user = Depends(get_admin_user)
):
    """获取系统健康状态"""
    try:
        health = await AdminService.get_system_health()
        return health
    except Exception as e:
        logger.error(f"Get system health error: {e}")
        raise


@router.get("/logs", response_model=LogResponse)
async def get_logs(
    lines: int = Query(100, ge=1, le=1000, description="日志行数"),
    level: Optional[str] = Query(None, description="日志级别过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    current_user = Depends(get_admin_user)
):
    """获取系统日志"""
    try:
        logs_data = AdminService.read_log_file(lines=lines * 2, level=level)  # 读取更多行以支持分页
        
        # 分页处理
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_logs = logs_data[start_idx:end_idx]
        
        log_entries = [
            LogEntry(
                timestamp=log["timestamp"],
                level=log["level"],
                message=log["message"],
                module=log["module"],
                function=log["function"],
                line=log["line"]
            )
            for log in paginated_logs
        ]
        
        return LogResponse(
            logs=log_entries,
            total=len(logs_data),
            page=page,
            page_size=page_size
        )
    except Exception as e:
        logger.error(f"Get logs error: {e}")
        raise


@router.post("/cleanup")
async def cleanup_old_data(
    days: int = Query(30, ge=1, le=365, description="清理多少天前的数据"),
    current_user = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """清理旧数据"""
    try:
        result = await AdminService.cleanup_old_data(db, days)
        
        logger.info(f"Data cleanup completed by admin {current_user.username}: {result}")
        
        return {
            "message": "Data cleanup completed successfully",
            "deleted_translations": result["deleted_translations"],
            "deleted_files": result["deleted_files"]
        }
    except Exception as e:
        logger.error(f"Cleanup error: {e}")
        raise


@router.post("/backup")
async def create_backup(
    current_user = Depends(get_admin_user)
):
    """创建系统备份"""
    try:
        # 这里应该实现实际的备份逻辑
        # 例如：数据库备份、文件备份等
        
        logger.info(f"Backup initiated by admin {current_user.username}")
        
        return {
            "message": "Backup initiated successfully",
            "backup_id": f"backup_{int(time.time())}",
            "status": "in_progress"
        }
    except Exception as e:
        logger.error(f"Backup error: {e}")
        raise


@router.get("/maintenance")
async def get_maintenance_status(
    current_user = Depends(get_admin_user)
):
    """获取维护状态"""
    try:
        # 这里应该返回当前的维护任务状态
        return {
            "maintenance_mode": False,
            "active_tasks": [],
            "last_maintenance": None
        }
    except Exception as e:
        logger.error(f"Get maintenance status error: {e}")
        raise


@router.post("/maintenance/enable")
async def enable_maintenance_mode(
    current_user = Depends(get_admin_user)
):
    """启用维护模式"""
    try:
        logger.warning(f"Maintenance mode enabled by admin {current_user.username}")
        
        return {
            "message": "Maintenance mode enabled",
            "enabled_at": datetime.utcnow().isoformat(),
            "enabled_by": current_user.username
        }
    except Exception as e:
        logger.error(f"Enable maintenance mode error: {e}")
        raise


@router.post("/maintenance/disable")
async def disable_maintenance_mode(
    current_user = Depends(get_admin_user)
):
    """禁用维护模式"""
    try:
        logger.info(f"Maintenance mode disabled by admin {current_user.username}")
        
        return {
            "message": "Maintenance mode disabled",
            "disabled_at": datetime.utcnow().isoformat(),
            "disabled_by": current_user.username
        }
    except Exception as e:
        logger.error(f"Disable maintenance mode error: {e}")
        raise
