import { useState, useEffect } from 'react';
import { Download, RefreshCw, Search, LogIn, LogOut, User, Languages, DollarSign, Activity } from 'lucide-react';
import FileUpload from './components/FileUpload';
import DocumentList from './components/DocumentList';
import StatusMessage from './components/StatusMessage';
import BatchDownloadModal from './components/BatchDownloadModal';
import Pagination from './components/Pagination';
import LoginModal from './components/LoginModal';
import TranslationModal from './components/TranslationModal';
import MingdaoPricing from './components/MingdaoPricing';
import MingdaoUserInfo from './components/MingdaoUserInfo';
import MingdaoSystemStatus from './components/MingdaoSystemStatus';
import MingdaoDemo from './components/MingdaoDemo';
import MingdaoAuth from './components/MingdaoAuth';
import BasicRegisterTest from './components/BasicRegisterTest';
import {
  TranslationHistory,
  UploadFile,
  UploadedFile,
  UserInfo,
  LoginRequest,
  TranslationRequest
} from './types/document';
import { downloadFile } from './utils/download';
import {
  getTranslationHistory,
  uploadFile,
  login,
  logout,
  getUserProfile,
  TokenManager,
  createTranslation,
  getTranslationProgress,
  downloadTranslationText,
  downloadTranslationDocx,
  getHtmlPreview,
  getUploadedFiles,
  getMingdaoTranslationHistory
} from './services/api';
import { API_CONFIG } from './config/api.config';
import { useMessages } from './hooks/useMessages';
import { openPreviewWindow, getDocumentFiles } from './utils/preview';
import { getFilePreviewUrl } from './utils/officePreview';
import './WelcomePage.css';
import './App.css';
import './components/FileUpload.css';
import './components/DocumentList.css';
import './components/DocumentPreview.css';
import './components/BatchDownloadModal.css';
// import './components/OnlyOfficePreview.css';
import './components/StatusMessage.css';
// import './components/ApiTest.css';
// import './components/PreviewTest.css';
// import './components/DataStructureInfo.css';
// import './components/SystemStatus.css';

// 模拟数据已移除，现在使用真实API数据

function App() {
  // 认证状态
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [showLoginModal, setShowLoginModal] = useState<boolean>(false);

  // 翻译历史状态
  const [translations, setTranslations] = useState<TranslationHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // 上传和翻译状态
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({});
  const [translationProgress, setTranslationProgress] = useState<{[key: number]: number}>({});

  // 预览状态
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewFileName, setPreviewFileName] = useState<string>('');

  // 批量下载模态框状态
  const [batchDownloadModal, setBatchDownloadModal] = useState<{
    isOpen: boolean;
    selectedTranslations: TranslationHistory[];
  }>({
    isOpen: false,
    selectedTranslations: []
  });

  // 当前选中的翻译
  const [selectedTranslations, setSelectedTranslations] = useState<TranslationHistory[]>([]);

  // 翻译创建状态
  const [showTranslationModal, setShowTranslationModal] = useState<boolean>(false);
  const [selectedFileForTranslation, setSelectedFileForTranslation] = useState<number | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  // 数据源切换
  const [dataSource, setDataSource] = useState<'backend' | 'mingdao'>('backend');

  // 明道云组件显示控制
  const [showMingdaoComponent, setShowMingdaoComponent] = useState<'none' | 'pricing' | 'userinfo' | 'status' | 'demo' | 'basictest'>('none');

  // 明道云认证模态框
  const [showMingdaoAuth, setShowMingdaoAuth] = useState(false);
  const [mingdaoAuthMode, setMingdaoAuthMode] = useState<'login' | 'register'>('login');

  // 消息系统
  const { messages, removeMessage, showSuccess, showError, showInfo } = useMessages();

  // 检查认证状态
  const checkAuthStatus = async () => {
    const token = TokenManager.getToken();
    if (token) {
      try {
        const userInfo = await getUserProfile();
        setUser(userInfo);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('获取用户信息失败:', error);
        TokenManager.removeToken();
        setIsAuthenticated(false);
        setUser(null);
      }
    }
  };

  // 登录处理
  const handleLogin = async (credentials: LoginRequest) => {
    try {
      const response = await login(credentials);
      TokenManager.setToken(response.access_token);
      setUser(response.user);
      setIsAuthenticated(true);
      setShowLoginModal(false);
      showSuccess('登录成功');
      await loadTranslations(); // 登录后加载翻译历史
    } catch (error) {
      console.error('登录失败:', error);
      showError('登录失败，请检查用户名和密码');
    }
  };

  // 登出处理
  const handleLogout = () => {
    logout();
    setUser(null);
    setIsAuthenticated(false);
    setTranslations([]);
    showInfo('已退出登录');
  };

  // 处理明道云登录
  const handleMingdaoLogin = () => {
    setMingdaoAuthMode('login');
    setShowMingdaoAuth(true);
  };

  // 处理明道云注册
  const handleMingdaoRegister = () => {
    setMingdaoAuthMode('register');
    setShowMingdaoAuth(true);
  };

  // 处理明道云认证成功
  const handleMingdaoAuthSuccess = (userData: any) => {
    console.log('明道云认证成功:', userData);
    setUser(userData);
    setIsAuthenticated(true);
    setDataSource('mingdao');
    setShowMingdaoAuth(false);
    showInfo(`欢迎，${userData.full_name || userData.username}！`);
  };

  // 加载翻译历史
  const loadTranslations = async (page: number = currentPage, size: number = pageSize) => {
    if (!isAuthenticated) return;

    setLoading(true);
    try {
      let response;

      if (dataSource === 'mingdao') {
        // 从明道云加载数据
        response = await getMingdaoTranslationHistory({
          page,
          page_size: size
        });
        showInfo(`成功从明道云加载第 ${page} 页，共 ${response.translations.length} 条记录`);
      } else {
        // 从后端加载数据
        response = await getTranslationHistory({
          page,
          page_size: size,
          status: searchTerm ? undefined : undefined // 可以根据搜索词过滤
        });
        showInfo(`成功加载第 ${page} 页，共 ${response.translations.length} 条记录`);
      }

      setTranslations(response.translations);
      setTotalItems(response.total);
      setTotalPages(response.total_pages);
    } catch (error) {
      console.error('加载翻译历史失败:', error);
      showError(`加载翻译历史失败: ${dataSource === 'mingdao' ? '明道云' : '后端'}数据源`);
      setTranslations([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载上传文件列表
  const loadUploadedFiles = async () => {
    if (!isAuthenticated) return;

    try {
      const response = await getUploadedFiles({
        page: 1,
        page_size: 100 // 获取最近100个文件
      });
      setUploadedFiles(response.files);
    } catch (error) {
      console.error('加载上传文件失败:', error);
    }
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadTranslations(page, pageSize);
  };

  // 处理每页大小变化
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
    setTotalPages(Math.ceil(totalItems / newPageSize));
    loadTranslations(1, newPageSize);
  };

  // 初始化应用
  useEffect(() => {
    const initApp = async () => {
      console.log('🚀 初始化应用...');
      await checkAuthStatus();
    };
    initApp();
  }, []);

  // 当认证状态改变时加载数据
  useEffect(() => {
    if (isAuthenticated) {
      loadTranslations(1, pageSize);
      loadUploadedFiles();
    }
  }, [isAuthenticated]); // eslint-disable-line react-hooks/exhaustive-deps

  // 当数据源改变时重新加载数据
  useEffect(() => {
    if (isAuthenticated) {
      loadTranslations(1, pageSize);
    }
  }, [dataSource]); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理文件上传
  const handleFilesSelected = (files: UploadFile[]) => {
    console.log('选择的文件:', files);
  };

  const handleUpload = async (files: UploadFile[]) => {
    if (!isAuthenticated) {
      showError('请先登录');
      return;
    }

    console.log('开始上传文件:', files);

    try {
      // 过滤出状态为 pending 的文件
      const filesToUpload = files.filter(f => f.status === 'pending');

      if (filesToUpload.length === 0) {
        showError('没有可上传的文件');
        return;
      }

      // 逐个上传文件
      for (const uploadFile of filesToUpload) {
        try {
          setUploadProgress(prev => ({
            ...prev,
            [uploadFile.name]: 0
          }));

          const response = await uploadFile(uploadFile.file);

          setUploadProgress(prev => ({
            ...prev,
            [uploadFile.name]: 100
          }));

          // 上传成功后，可以选择直接创建翻译任务
          if (response.file) {
            uploadFile.uploadedFile = response.file;
            uploadFile.status = 'success';
            showSuccess(`文件 ${uploadFile.name} 上传成功`);
            // 刷新上传文件列表
            await loadUploadedFiles();
          }
        } catch (error) {
          console.error(`上传文件 ${uploadFile.name} 失败:`, error);
          uploadFile.status = 'error';
          uploadFile.error = error instanceof Error ? error.message : '上传失败';
          showError(`文件 ${uploadFile.name} 上传失败`);
        }
      }

      // 清空上传进度
      setUploadProgress({});
    } catch (error) {
      console.error('上传失败:', error);
      showError('上传失败，请稍后重试');
      setUploadProgress({});
    }
  };

  // 创建翻译任务
  const handleCreateTranslation = async (request: TranslationRequest) => {
    if (!isAuthenticated) {
      showError('请先登录');
      return;
    }

    try {
      const response = await createTranslation(request);
      showSuccess('翻译任务创建成功');

      // 开始监控翻译进度
      monitorTranslationProgress(response.translation.id);

      // 刷新翻译列表
      await loadTranslations();
    } catch (error) {
      console.error('创建翻译任务失败:', error);
      showError('创建翻译任务失败');
    }
  };

  // 监控翻译进度
  const monitorTranslationProgress = (translationId: number) => {
    const interval = setInterval(async () => {
      try {
        const progress = await getTranslationProgress(translationId);

        setTranslationProgress(prev => ({
          ...prev,
          [translationId]: progress.progress
        }));

        if (progress.status === 'COMPLETED' || progress.status === 'FAILED') {
          clearInterval(interval);
          await loadTranslations(); // 刷新列表

          if (progress.status === 'COMPLETED') {
            showSuccess('翻译完成');
          } else {
            showError('翻译失败');
          }
        }
      } catch (error) {
        console.error('获取翻译进度失败:', error);
        clearInterval(interval);
      }
    }, 2000); // 每2秒检查一次
  };

  // 处理HTML预览
  const handleHtmlPreview = async (translationId: number) => {
    try {
      const htmlContent = await getHtmlPreview(translationId);
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(htmlContent);
        newWindow.document.close();
      }
      showSuccess('HTML预览已打开');
    } catch (error) {
      console.error('HTML预览失败:', error);
      showError('HTML预览失败');
    }
  };

  // 处理文本下载
  const handleDownloadText = async (translationId: number, filename: string) => {
    try {
      const blob = await downloadTranslationText(translationId);
      downloadFile(blob, `${filename}_翻译.txt`);
      showSuccess('文本下载成功');
    } catch (error) {
      console.error('文本下载失败:', error);
      showError('文本下载失败');
    }
  };

  // 处理Word下载
  const handleDownloadDocx = async (translationId: number, filename: string) => {
    try {
      const blob = await downloadTranslationDocx(translationId);
      downloadFile(blob, `${filename}_双语翻译.docx`);
      showSuccess('Word文档下载成功');
    } catch (error) {
      console.error('Word下载失败:', error);
      showError('Word下载失败');
    }
  };

  // 批量下载 - 打开选择模态框
  const handleBatchDownload = async () => {
    if (selectedTranslations.length === 0) {
      showError('请先选择要下载的翻译');
      return;
    }

    setBatchDownloadModal({
      isOpen: true,
      selectedTranslations: selectedTranslations
    });
  };

  // 确认批量下载
  const confirmBatchDownload = async (downloadType: 'text' | 'docx') => {
    const { selectedTranslations } = batchDownloadModal;

    showInfo(`开始批量下载 ${selectedTranslations.length} 个翻译文档...`);

    try {
      for (const translation of selectedTranslations) {
        if (translation.status === 'COMPLETED') {
          const filename = translation.file?.original_filename || `translation_${translation.id}`;

          if (downloadType === 'text') {
            await handleDownloadText(translation.id, filename);
          } else {
            await handleDownloadDocx(translation.id, filename);
          }

          // 添加延迟避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      showSuccess('批量下载完成');
    } catch (error) {
      console.error('批量下载失败:', error);
      showError('批量下载失败');
    }

    closeBatchDownloadModal();
  };

  // 关闭批量下载模态框
  const closeBatchDownloadModal = () => {
    setBatchDownloadModal({
      isOpen: false,
      selectedTranslations: []
    });
  };

  // 处理翻译选择变化
  const handleSelectionChange = (selected: TranslationHistory[]) => {
    setSelectedTranslations(selected);
  };



  // 过滤翻译历史
  const filteredTranslations = translations.filter(translation => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();

    // 搜索文件名
    if (translation.file?.original_filename?.toLowerCase().includes(searchLower)) {
      return true;
    }

    // 搜索语言对
    const sourceLang = API_CONFIG.translation.supportedLanguages[translation.source_language as keyof typeof API_CONFIG.translation.supportedLanguages];
    const targetLang = API_CONFIG.translation.supportedLanguages[translation.target_language as keyof typeof API_CONFIG.translation.supportedLanguages];

    if (sourceLang?.toLowerCase().includes(searchLower) ||
        targetLang?.toLowerCase().includes(searchLower)) {
      return true;
    }

    return false;
  });

  // 如果未认证，显示登录界面
  if (!isAuthenticated) {
    return (
      <div className="welcome-page">
        {/* 消息提示 */}
        <div className="status-messages-container">
          {messages.map((message) => (
            <StatusMessage
              key={message.id}
              type={message.type}
              message={message.message}
              onClose={() => removeMessage(message.id)}
            />
          ))}
        </div>

        {/* 顶部导航 */}
        <nav className="welcome-nav">
          <div className="nav-content">
            <div className="nav-brand">
              <div className="brand-icon">
                <Languages size={24} />
              </div>
              <div className="brand-text">
                <h1>SMILE TRANS</h1>
                <p>智能翻译系统</p>
              </div>
            </div>
            <button
              onClick={() => setShowLoginModal(true)}
              className="nav-login-btn"
            >
              <LogIn size={16} />
              登录 / 注册
            </button>
          </div>
        </nav>

        {/* 主要内容区域 */}
        <div className="welcome-main">
          <div className="welcome-content">
            {/* 主标题区域 */}
            <div className="welcome-hero">
              <div className="hero-icon">
                <Languages size={40} />
              </div>
              <h1 className="hero-title">
                欢迎使用 <span className="title-highlight">SMILE TRANS</span>
              </h1>
              <p className="hero-subtitle">
                专业的智能翻译系统，支持多种语言互译，保持文档格式完整
              </p>
              <button
                onClick={() => setShowLoginModal(true)}
                className="hero-cta-btn"
              >
                <LogIn size={20} />
                开始使用
              </button>
            </div>

            {/* 功能特色 */}
            <div className="features-grid">
              <div className="feature-card">
                <div className="feature-card-icon blue">
                  <Languages size={32} />
                </div>
                <h3>多语言支持</h3>
                <p>支持中英日韩法德西等多种语言互译</p>
              </div>

              <div className="feature-card">
                <div className="feature-card-icon purple">
                  <Download size={32} />
                </div>
                <h3>格式保持</h3>
                <p>完美保持Word文档原有格式和排版</p>
              </div>

              <div className="feature-card">
                <div className="feature-card-icon indigo">
                  <User size={32} />
                </div>
                <h3>用户友好</h3>
                <p>简单易用的界面，实时翻译进度显示</p>
              </div>
            </div>

            {/* 底部提示 */}
            <div className="welcome-footer">
              <p>已有账号？</p>
              <button
                onClick={() => setShowLoginModal(true)}
                className="footer-login-btn"
              >
                立即登录开始翻译
              </button>
            </div>
          </div>
        </div>

        {/* 登录模态框 */}
        <LoginModal
          isOpen={showLoginModal}
          onClose={() => setShowLoginModal(false)}
          onLogin={handleLogin}
        />
      </div>
    );
  }

  return (
    <div className="app">
      {/* 消息提示 */}
      <div className="status-messages-container">
        {messages.map((message) => (
          <StatusMessage
            key={message.id}
            type={message.type}
            message={message.message}
            onClose={() => removeMessage(message.id)}
          />
        ))}
      </div>

      <div className="app-header">
        <div className="flex justify-between items-center">
          <h1>SMILE TRANS - 智能翻译系统</h1>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              欢迎, {user?.full_name || user?.username}
            </span>
            <button
              onClick={handleLogout}
              className="text-red-600 hover:text-red-800 flex items-center gap-1"
            >
              <LogOut size={16} />
              退出
            </button>
          </div>
        </div>
      </div>

      <div className="app-content">
        {/* 左侧操作区域 */}
        <div className="sidebar">
          {/* 用户信息区域 */}
          <div className="user-section">
            <h3>用户信息</h3>
            <div className="user-info">
              <p><User size={16} /> {user?.username}</p>
              <p>剩余配额: {user?.remaining_quota || 0}</p>
            </div>
          </div>

          {/* 文件上传区域 */}
          <div className="upload-section">
            <h3>文件上传</h3>
            <FileUpload
              onFilesSelected={handleFilesSelected}
              onUpload={handleUpload}
              maxFileSize={50}
              multiple={true}
            />
          </div>

          {/* 翻译操作区域 */}
          <div className="translation-section">
            <h3>翻译操作</h3>
            <div className="action-buttons">
              <button
                className="btn btn-primary"
                onClick={() => setShowTranslationModal(true)}
                disabled={loading}
              >
                <Languages size={16} />
                创建翻译
              </button>
            </div>
          </div>

          {/* 明道云功能区域 */}
          <div className="mingdao-section">
            <h3>明道云功能</h3>

            {/* 明道云认证按钮 */}
            {!isAuthenticated && (
              <div className="auth-buttons" style={{ marginBottom: '15px' }}>
                <button
                  className="btn btn-primary"
                  onClick={handleMingdaoLogin}
                >
                  <LogIn size={16} />
                  明道云登录
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={handleMingdaoRegister}
                >
                  <User size={16} />
                  创建账号
                </button>
              </div>
            )}

            <div className="action-buttons">
              <button
                className={`btn ${showMingdaoComponent === 'basictest' ? 'btn-success' : 'btn-warning'}`}
                onClick={() => setShowMingdaoComponent(showMingdaoComponent === 'basictest' ? 'none' : 'basictest')}
                style={{ backgroundColor: showMingdaoComponent === 'basictest' ? '#28a745' : '#ff6b35', borderColor: showMingdaoComponent === 'basictest' ? '#28a745' : '#ff6b35' }}
              >
                🧪 基础测试
              </button>
              <button
                className={`btn ${showMingdaoComponent === 'demo' ? 'btn-success' : 'btn-primary'}`}
                onClick={() => setShowMingdaoComponent(showMingdaoComponent === 'demo' ? 'none' : 'demo')}
              >
                <Activity size={16} />
                完整演示
              </button>
              <button
                className={`btn ${showMingdaoComponent === 'pricing' ? 'btn-success' : 'btn-secondary'}`}
                onClick={() => setShowMingdaoComponent(showMingdaoComponent === 'pricing' ? 'none' : 'pricing')}
              >
                <DollarSign size={16} />
                收费规则
              </button>
              <button
                className={`btn ${showMingdaoComponent === 'userinfo' ? 'btn-success' : 'btn-secondary'}`}
                onClick={() => setShowMingdaoComponent(showMingdaoComponent === 'userinfo' ? 'none' : 'userinfo')}
                disabled={!isAuthenticated}
              >
                <User size={16} />
                用户信息
              </button>
              <button
                className={`btn ${showMingdaoComponent === 'status' ? 'btn-success' : 'btn-secondary'}`}
                onClick={() => setShowMingdaoComponent(showMingdaoComponent === 'status' ? 'none' : 'status')}
              >
                <Activity size={16} />
                系统状态
              </button>
            </div>
          </div>

          {/* 操作按钮区域 */}
          <div className="actions-section">
            <h3>操作</h3>

            {/* 数据源切换 */}
            <div className="data-source-section">
              <label>数据源:</label>
              <select
                value={dataSource}
                onChange={(e) => setDataSource(e.target.value as 'backend' | 'mingdao')}
                className="form-select"
              >
                <option value="backend">后端数据库</option>
                <option value="mingdao">明道云 (预览)</option>
              </select>
              <small className="help-text">
                {dataSource === 'mingdao' ? '使用明道云数据，支持OnlyOffice预览' : '使用后端数据库'}
              </small>
            </div>

            <div className="action-buttons">
              <button
                className="btn btn-primary"
                onClick={() => loadTranslations(currentPage, pageSize)}
                disabled={loading}
              >
                <RefreshCw size={16} />
                刷新列表
              </button>

              <button
                className="btn btn-success"
                onClick={handleBatchDownload}
                disabled={selectedTranslations.length === 0}
              >
                <Download size={16} />
                批量下载 {selectedTranslations.length > 0 && `(${selectedTranslations.length})`}
              </button>
            </div>
          </div>

          {/* 搜索过滤区域 */}
          <div className="filter-section">
            <h3>搜索过滤</h3>
            <div className="search-box">
              <Search size={16} className="search-icon" />
              <input
                type="text"
                placeholder="搜索文件名或语言..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>

          {/* 调试组件已移除，准备部署 */}
        </div>

        {/* 右侧主内容区域 */}
        <div className="main-content">
          {/* 明道云组件显示区域 */}
          {showMingdaoComponent !== 'none' && (
            <div className="mingdao-component-container" style={{ marginBottom: '20px' }}>
              {showMingdaoComponent === 'basictest' && (
                <BasicRegisterTest />
              )}
              {showMingdaoComponent === 'demo' && (
                <MingdaoDemo />
              )}
              {showMingdaoComponent === 'pricing' && (
                <MingdaoPricing
                  onCalculate={(charCount, cost, paymentType) => {
                    console.log('费用计算:', { charCount, cost, paymentType });
                  }}
                />
              )}
              {showMingdaoComponent === 'userinfo' && (
                <MingdaoUserInfo
                  user={user}
                  onRefresh={checkAuthStatus}
                />
              )}
              {showMingdaoComponent === 'status' && (
                <MingdaoSystemStatus
                  onStatusChange={(isHealthy) => {
                    console.log('系统状态变化:', isHealthy);
                  }}
                />
              )}
            </div>
          )}

          {/* 翻译历史列表 */}
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            height: showMingdaoComponent !== 'none' ? 'auto' : '100%',
            overflow: 'hidden'
          }}>
            {/* 翻译历史列表区域 - 可滚动 */}
            <div style={{
              flex: '1',
              overflow: 'auto',
              minHeight: showMingdaoComponent !== 'none' ? '400px' : '0'
            }}>
              <DocumentList
                documents={filteredTranslations}
                  onPreview={handleHtmlPreview}
                  onDownload={handleDownloadText}
                  onDownloadDocx={handleDownloadDocx}
                  onSelectionChange={handleSelectionChange}
                  loading={loading}
                  translationProgress={translationProgress}
                />
              </div>

              {/* 分页组件区域 - 固定在底部 */}
              <div style={{ flexShrink: 0 }}>
                {(totalItems > 0 || translations.length > 0) ? (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages || Math.ceil(translations.length / pageSize) || 1}
                    totalItems={totalItems || translations.length}
                    pageSize={pageSize}
                    pageSizeOptions={[10, 20, 50, 100]}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    loading={loading}
                  />
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    {loading ? '正在加载分页信息...' : `暂无翻译记录 (总记录数: ${totalItems})`}
                  </div>
                )}
              </div>
            </div>
        </div>
      </div>

      {/* 登录模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLogin={handleLogin}
      />

      {/* 翻译创建模态框 */}
      <TranslationModal
        isOpen={showTranslationModal}
        onClose={() => setShowTranslationModal(false)}
        onCreateTranslation={handleCreateTranslation}
        uploadedFiles={uploadedFiles}
      />

      {/* 批量下载选择模态框 */}
      <BatchDownloadModal
        isOpen={batchDownloadModal.isOpen}
        onClose={closeBatchDownloadModal}
        onConfirm={confirmBatchDownload}
        selectedCount={batchDownloadModal.selectedTranslations.length}
      />

      {/* 明道云认证模态框 */}
      <MingdaoAuth
        isOpen={showMingdaoAuth}
        onClose={() => setShowMingdaoAuth(false)}
        onAuthSuccess={handleMingdaoAuthSuccess}
        initialMode={mingdaoAuthMode}
      />

    </div>
  );
}

export default App;
