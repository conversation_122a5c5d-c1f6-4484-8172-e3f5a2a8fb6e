#!/bin/bash

echo "🚀 启动生产环境服务..."

# 创建日志目录
mkdir -p logs

# 构建项目
echo "📦 构建项目..."
npm run build

# 使用 PM2 启动服务
echo "🔄 启动 PM2 服务..."
pm2 start ecosystem.config.cjs

# 保存 PM2 配置（开机自启）
pm2 save

# 设置开机自启动
pm2 startup

echo "✅ 服务启动完成！"
echo ""
echo "📊 查看服务状态: pm2 status"
echo "📝 查看日志: pm2 logs translate-front"
echo "🔄 重启服务: pm2 restart translate-front"
echo "⏹️  停止服务: pm2 stop translate-front"
echo ""
echo "🌐 访问地址: http://your-server-ip:3000"
