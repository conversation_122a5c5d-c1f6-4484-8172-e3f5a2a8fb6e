"""
简单的明道云API代理服务器
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
import json

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 明道云配置
MINGDAO_CONFIG = {
    "base_url": "https://dmit.duoningbio.com/api/v2/open",
    "app_key": "d88c1d2329c42504",
    "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA=="
}

@app.route('/')
def home():
    """首页"""
    return {
        "message": "明道云API代理服务器",
        "version": "1.0.0",
        "status": "running"
    }

@app.route('/health')
def health():
    """健康检查"""
    return {"status": "healthy", "service": "mingdao-proxy"}

@app.route('/api/mingdao/pricing')
def get_pricing():
    """获取收费规则"""
    return {
        "success": True,
        "pricing_rules": {
            "monthly": {
                "name": "月付套餐",
                "price": 20,
                "quota": 100000,
                "duration_days": 30,
                "description": "20元/月，10万字额度"
            },
            "per_translation_small": {
                "name": "按次收费-小文档",
                "min_chars": 1,
                "max_chars": 5000,
                "price": 5,
                "description": "5000字以内5元一次"
            },
            "per_translation_large": {
                "name": "按次收费-大文档",
                "min_chars": 5001,
                "price_per_5k": 5,
                "description": "超过5000字，每5000字5元"
            },
            "bulk_discount": {
                "name": "批量优惠",
                "min_chars": 300000,
                "price": 100,
                "description": "30万字100元"
            }
        }
    }

@app.route('/api/mingdao/calculate-cost', methods=['POST'])
def calculate_cost():
    """计算翻译费用"""
    try:
        data = request.get_json()
        char_count = data.get('charCount', 0)
        
        if char_count <= 0:
            return {"error": "字符数必须大于0"}, 400
        
        if char_count <= 5000:
            cost = 5
            payment_type = "按次付费"
            description = f"{char_count}字符，按次收费"
        elif char_count >= 300000:
            cost = 100
            payment_type = "批量优惠"
            description = f"{char_count}字符，批量优惠"
        else:
            # 超过5000字，每5000字5元
            cost = ((char_count - 1) // 5000 + 1) * 5
            payment_type = "按次付费"
            description = f"{char_count}字符，分段收费"
        
        return {
            "success": True,
            "char_count": char_count,
            "cost": cost,
            "payment_type": payment_type,
            "description": description
        }
        
    except Exception as e:
        return {"error": str(e)}, 500

@app.route('/api/mingdao/worksheet/info', methods=['POST'])
def get_worksheet_info():
    """获取工作表信息"""
    try:
        data = request.get_json()
        worksheet_id = data.get('worksheetId')
        
        if not worksheet_id:
            return {"error": "worksheetId is required"}, 400
        
        payload = {
            "appKey": MINGDAO_CONFIG["app_key"],
            "sign": MINGDAO_CONFIG["sign"],
            "worksheetId": worksheet_id
        }
        
        response = requests.post(
            f"{MINGDAO_CONFIG['base_url']}/worksheet/getWorksheetInfo",
            json=payload,
            timeout=30
        )
        
        return response.json()
        
    except Exception as e:
        return {"error": str(e)}, 500

@app.route('/api/mingdao/worksheet/rows', methods=['POST'])
def get_worksheet_rows():
    """获取工作表数据"""
    try:
        data = request.get_json()
        worksheet_id = data.get('worksheetId')
        page_size = data.get('pageSize', 20)
        page_index = data.get('pageIndex', 1)
        filters = data.get('filters', [])
        
        if not worksheet_id:
            return {"error": "worksheetId is required"}, 400
        
        payload = {
            "appKey": MINGDAO_CONFIG["app_key"],
            "sign": MINGDAO_CONFIG["sign"],
            "worksheetId": worksheet_id,
            "pageSize": page_size,
            "pageIndex": page_index,
            "listType": 0,
            "controls": [],
            "filters": filters
        }
        
        response = requests.post(
            f"{MINGDAO_CONFIG['base_url']}/worksheet/getFilterRows",
            json=payload,
            timeout=30
        )
        
        return response.json()
        
    except Exception as e:
        return {"error": str(e)}, 500

@app.route('/api/mingdao/test')
def test_connection():
    """测试连接"""
    try:
        # 测试获取用户表信息
        payload = {
            "appKey": MINGDAO_CONFIG["app_key"],
            "sign": MINGDAO_CONFIG["sign"],
            "worksheetId": "6886e20ba849420e13f69b23"  # 用户表ID
        }
        
        response = requests.post(
            f"{MINGDAO_CONFIG['base_url']}/worksheet/getWorksheetInfo",
            json=payload,
            timeout=10
        )
        
        result = response.json()
        
        return {
            "success": True,
            "message": "连接测试成功",
            "mingdao_response": result,
            "status_code": response.status_code
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": "连接测试失败",
            "error": str(e)
        }

if __name__ == '__main__':
    print("🚀 启动简单代理服务器...")
    print("📖 访问: http://localhost:9000")
    print("🔗 API: http://localhost:9000/api/mingdao/")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=9000, debug=True)
