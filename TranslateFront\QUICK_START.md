# 🚀 SMILE TRANS 快速部署指南

## 📋 问题解决

**问题**: 使用 `npm run serve` 启动程序后，断开服务器连接时服务就停止了。

**解决方案**: 使用进程管理工具让服务在后台持续运行。

## 🎯 三种解决方案

### 方案1: PM2 (推荐) ⭐⭐⭐⭐⭐

**优势**: 专业进程管理、自动重启、日志管理、监控

```bash
# 1. 安装 PM2
npm install -g pm2

# 2. 一键启动（推荐）
chmod +x start-production.sh
./start-production.sh

# 3. 或手动启动
npm run production

# 管理命令
pm2 status              # 查看状态
pm2 logs translate-front # 查看日志
pm2 restart translate-front # 重启
pm2 stop translate-front    # 停止
```

### 方案2: nohup (简单) ⭐⭐⭐

**优势**: 无需安装额外工具，简单快速

```bash
# 启动
chmod +x start-nohup.sh
./start-nohup.sh

# 停止
chmod +x stop-nohup.sh
./stop-nohup.sh

# 查看日志
tail -f app.log
```

### 方案3: systemd (最稳定) ⭐⭐⭐⭐⭐

**优势**: 系统级服务，开机自启，最高稳定性

```bash
# 设置服务
chmod +x setup-systemd.sh
./setup-systemd.sh

# 管理服务
sudo systemctl start translate-front
sudo systemctl status translate-front
sudo systemctl stop translate-front
```

## 📁 已创建的文件

### 配置文件
- `ecosystem.config.js` - PM2 配置文件
- `translate-front.service` - systemd 服务配置

### 脚本文件
- `install-pm2.sh` - PM2 安装脚本
- `start-production.sh` - PM2 启动脚本
- `stop-production.sh` - PM2 停止脚本
- `start-nohup.sh` - nohup 启动脚本
- `stop-nohup.sh` - nohup 停止脚本
- `setup-systemd.sh` - systemd 设置脚本

### 文档文件
- `README.md` - 完整项目文档
- `DEPLOYMENT.md` - 详细部署指南
- `USAGE.md` - 使用说明
- `QUICK_START.md` - 本快速指南

## 🔧 package.json 新增脚本

```json
{
  "scripts": {
    "pm2:start": "pm2 start ecosystem.config.js",
    "pm2:stop": "pm2 stop translate-front",
    "pm2:restart": "pm2 restart translate-front",
    "pm2:logs": "pm2 logs translate-front",
    "pm2:status": "pm2 status",
    "production": "npm run build && npm run pm2:start"
  }
}
```

## ⚡ 立即开始

### Linux/Mac 服务器

```bash
# 1. 上传项目到服务器
scp -r ./TranslateFront user@your-server:/var/www/

# 2. 进入项目目录
cd /var/www/TranslateFront

# 3. 安装依赖
npm install

# 4. 给脚本执行权限
chmod +x *.sh

# 5. 选择一种方案启动
./start-production.sh  # PM2 方案
# 或
./start-nohup.sh      # nohup 方案
# 或
./setup-systemd.sh    # systemd 方案
```

### Windows 服务器

```bash
# 1. 安装 PM2
npm install -g pm2

# 2. 启动服务
npm run production

# 3. 查看状态
pm2 status
```

## ✅ 验证部署

访问 `http://your-server-ip:3000` 检查：

- ✅ 页面正常加载
- ✅ 文档列表正常显示
- ✅ 文件上传功能正常
- ✅ **断开SSH连接后服务仍在运行** 🎉

## 🔍 常用命令

```bash
# 查看服务状态
pm2 status
sudo systemctl status translate-front

# 查看日志
pm2 logs translate-front
tail -f app.log
sudo journalctl -u translate-front -f

# 重启服务
pm2 restart translate-front
sudo systemctl restart translate-front

# 停止服务
pm2 stop translate-front
sudo systemctl stop translate-front
```

## 🚨 故障排除

### 端口被占用
```bash
sudo lsof -i :3000
sudo kill -9 <PID>
```

### 权限问题
```bash
chmod +x *.sh
sudo chown -R $USER:$USER ./
```

### 查看错误日志
```bash
pm2 logs translate-front --err
tail -f app.log
```

## 🎉 完成！

现在你的 **SMILE TRANS** 系统可以：

- ✅ 在后台持续运行
- ✅ 断开SSH连接后继续工作
- ✅ 自动重启（如果崩溃）
- ✅ 完整的日志记录
- ✅ 开机自动启动（systemd方案）

**问题解决！** 🚀

---

需要更多帮助？查看：
- [README.md](./README.md) - 完整文档
- [DEPLOYMENT.md](./DEPLOYMENT.md) - 详细部署指南
- [USAGE.md](./USAGE.md) - 使用说明
