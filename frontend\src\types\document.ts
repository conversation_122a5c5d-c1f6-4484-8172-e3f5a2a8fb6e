// 翻译系统类型定义

// 用户信息接口
export interface UserInfo {
  id: number | string;
  username: string;
  email: string;
  full_name?: string;
  phone?: string;
  role?: 'USER' | 'ADMIN';
  is_active?: boolean;

  // 原有字段（兼容性）
  translation_quota?: number;
  used_quota?: number;
  remaining_quota?: number;

  // 明道云字段
  user_type?: string;
  status?: string;
  balance?: number;
  total_quota?: number;
  monthly_quota?: number;
  monthly_used?: number;
  monthly_expire_date?: string;
  last_login?: string;

  created_at: string;
  updated_at?: string;
}

// 上传文件信息接口
export interface UploadedFile {
  id: number;
  user_id: number;
  filename: string;
  original_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  status: 'UPLOADED' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  error_message?: string;
  total_paragraphs?: number;
  total_characters?: number;
  extracted_text?: string;
  created_at: string;
  updated_at: string;
}

// 翻译历史记录接口
export interface TranslationHistory {
  id: number;
  user_id: number;
  file_id: number;
  source_language: string;
  target_language: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  progress: number;
  original_text?: string;
  translated_text?: string;
  total_characters: number;
  translated_characters?: number;
  confidence_score?: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  file?: UploadedFile;
}

// 语言信息接口
export interface Language {
  code: string;
  name: string;
  native_name?: string;
}

// 翻译请求接口
export interface TranslationRequest {
  file_id: number;
  source_language: string;
  target_language: string;
  include_original?: boolean;
}

// 翻译进度接口
export interface TranslationProgress {
  translation_id: number;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  progress: number;
  error_message?: string;
  estimated_time?: number;
}

// 文件上传状态接口
export interface UploadFile {
  file: File;
  id: string;
  name: string;
  size: number;
  type: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  uploadedFile?: UploadedFile;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// 认证响应接口
export interface AuthResponse {
  access_token: string;
  token_type: string;
  user: UserInfo;
}

// 登录请求接口
export interface LoginRequest {
  username: string;
  password: string;
}

// 注册请求接口
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

// 预览模式枚举
export enum PreviewMode {
  NONE = 'none',
  MODAL = 'modal',
  INLINE = 'inline',
  HTML = 'html',
  ONLYOFFICE = 'onlyoffice'
}

// 文件类型枚举
export enum FileType {
  UNKNOWN = 'unknown',
  DOC = 'doc',
  DOCX = 'docx',
  PDF = 'pdf',
  TXT = 'txt'
}

// 翻译状态枚举
export enum TranslationStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

// 文件状态枚举
export enum FileStatus {
  UPLOADED = 'UPLOADED',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

// 下载类型枚举
export enum DownloadType {
  TEXT = 'text',
  DOCX = 'docx',
  HTML = 'html'
}

// 语言代码枚举
export enum LanguageCode {
  ZH = 'zh',
  EN = 'en',
  JA = 'ja',
  KO = 'ko',
  FR = 'fr',
  DE = 'de',
  ES = 'es'
}

// 用户角色枚举
export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN'
}

// 错误信息接口
export interface ErrorInfo {
  code: string;
  message: string;
  details?: any;
}

// 批量操作接口
export interface BatchOperation {
  ids: number[];
  operation: 'download' | 'delete' | 'export';
  format?: DownloadType;
}

// 统计信息接口
export interface Statistics {
  total_files: number;
  total_translations: number;
  total_characters: number;
  success_rate: number;
  average_time: number;
}
