// 明道云API服务
import { MINGDAO_CONFIG } from '../config/api.config';

// 明道云API基础配置
// 在开发环境中使用代理，生产环境中使用实际的API地址
const MINGDAO_API_BASE = import.meta.env.DEV
  ? '/mingdao-api'  // 开发环境使用代理
  : 'https://api.mingdao.com/api';  // 生产环境使用实际地址

// 用户注册数据接口
export interface RegisterUserData {
  username: string;
  email: string;
  password: string;
  fullName?: string;
  phone?: string;
}

// 用户登录数据接口
export interface LoginUserData {
  username: string;
  password: string;
}

// API响应接口
export interface MingdaoApiResponse<T = any> {
  success: boolean;
  data?: T;
  error_msg?: string;
  error_code?: string;
}

// 生成明道云API签名（简化版本）
const generateSign = (data: any): string => {
  // 实际项目中应该使用更安全的签名算法
  // 这里使用简化版本进行演示
  const timestamp = Date.now();
  const dataStr = JSON.stringify(data);
  return btoa(`${MINGDAO_CONFIG.appKey}_${timestamp}_${dataStr}`);
};

// 创建明道云用户
export const createMingdaoUser = async (userData: RegisterUserData): Promise<MingdaoApiResponse> => {
  try {
    console.log('🚀 开始创建明道云用户:', userData.username);

    // 密码哈希处理
    const passwordHash = btoa(userData.password); // 简单编码，实际应使用更安全的哈希
    const currentTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

    // 构建明道云API请求数据
    const controls = [
      { controlId: MINGDAO_CONFIG.userFields.username, value: userData.username },
      { controlId: MINGDAO_CONFIG.userFields.email, value: userData.email },
      { controlId: MINGDAO_CONFIG.userFields.password_hash, value: passwordHash },
      { controlId: MINGDAO_CONFIG.userFields.full_name, value: userData.fullName || userData.username },
      { controlId: MINGDAO_CONFIG.userFields.phone, value: userData.phone || '' },
      { controlId: MINGDAO_CONFIG.userFields.user_type, value: 'b4e9a50e-e83a-4e1e-ac29-619572a67265' }, // 免费用户
      { controlId: MINGDAO_CONFIG.userFields.status, value: '0f7a48f5-9dc2-496d-96ca-c4a3b49e6d00' }, // 正常状态
      { controlId: MINGDAO_CONFIG.userFields.balance, value: 0 },
      { controlId: MINGDAO_CONFIG.userFields.total_quota, value: 1000 }, // 免费1000字符
      { controlId: MINGDAO_CONFIG.userFields.used_quota, value: 0 },
      { controlId: MINGDAO_CONFIG.userFields.monthly_quota, value: 0 },
      { controlId: MINGDAO_CONFIG.userFields.monthly_used, value: 0 },
      { controlId: MINGDAO_CONFIG.userFields.created_at, value: currentTime },
      { controlId: MINGDAO_CONFIG.userFields.updated_at, value: currentTime }
    ];

    const requestData = {
      appKey: MINGDAO_CONFIG.appKey,
      sign: generateSign(controls),
      worksheetId: MINGDAO_CONFIG.worksheets.users,
      triggerWorkflow: true,
      controls: controls
    };

    console.log('📤 发送明道云API请求:', requestData);

    // 发送请求到明道云API
    const response = await fetch(`${MINGDAO_API_BASE}/worksheet/addRow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('📥 明道云API响应:', result);

    if (result.success) {
      return {
        success: true,
        data: {
          userId: result.data,
          username: userData.username,
          email: userData.email,
          full_name: userData.fullName || userData.username,
          phone: userData.phone || '',
          user_type: 'b4e9a50e-e83a-4e1e-ac29-619572a67265',
          status: '0f7a48f5-9dc2-496d-96ca-c4a3b49e6d00',
          balance: 0,
          total_quota: 1000,
          used_quota: 0,
          monthly_quota: 0,
          monthly_used: 0,
          created_at: currentTime
        }
      };
    } else {
      return {
        success: false,
        error_msg: result.error_msg || '创建用户失败',
        error_code: result.error_code
      };
    }

  } catch (error) {
    console.error('❌ 创建明道云用户失败:', error);
    return {
      success: false,
      error_msg: `网络错误: ${error}`,
      error_code: 'NETWORK_ERROR'
    };
  }
};

// 验证明道云用户登录
export const authenticateMingdaoUser = async (loginData: LoginUserData): Promise<MingdaoApiResponse> => {
  try {
    console.log('🔐 开始验证明道云用户:', loginData.username);

    // 构建查询条件
    const filters = [
      {
        controlId: MINGDAO_CONFIG.userFields.username,
        dataType: 2, // 文本类型
        spliceType: 1, // AND
        filterType: 1, // 等于
        value: loginData.username
      }
    ];

    const requestData = {
      appKey: MINGDAO_CONFIG.appKey,
      sign: generateSign(filters),
      worksheetId: MINGDAO_CONFIG.worksheets.users,
      pageSize: 1,
      pageIndex: 1,
      listType: 0,
      controls: [], // 返回所有字段
      filters: filters
    };

    console.log('📤 发送明道云查询请求:', requestData);

    // 查询用户
    const response = await fetch(`${MINGDAO_API_BASE}/worksheet/getRows`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('📥 明道云查询响应:', result);

    if (result.success && result.data && result.data.length > 0) {
      const userData = result.data[0];
      
      // 获取密码哈希进行验证
      const storedPasswordHash = userData[MINGDAO_CONFIG.userFields.password_hash];
      const inputPasswordHash = btoa(loginData.password);

      if (storedPasswordHash === inputPasswordHash) {
        // 密码正确，更新最后登录时间
        await updateLastLoginTime(userData.rowid);

        return {
          success: true,
          data: {
            id: userData.rowid,
            username: userData[MINGDAO_CONFIG.userFields.username],
            email: userData[MINGDAO_CONFIG.userFields.email],
            full_name: userData[MINGDAO_CONFIG.userFields.full_name],
            phone: userData[MINGDAO_CONFIG.userFields.phone],
            user_type: userData[MINGDAO_CONFIG.userFields.user_type],
            status: userData[MINGDAO_CONFIG.userFields.status],
            balance: parseFloat(userData[MINGDAO_CONFIG.userFields.balance] || '0'),
            total_quota: parseInt(userData[MINGDAO_CONFIG.userFields.total_quota] || '0'),
            used_quota: parseInt(userData[MINGDAO_CONFIG.userFields.used_quota] || '0'),
            monthly_quota: parseInt(userData[MINGDAO_CONFIG.userFields.monthly_quota] || '0'),
            monthly_used: parseInt(userData[MINGDAO_CONFIG.userFields.monthly_used] || '0'),
            monthly_expire_date: userData[MINGDAO_CONFIG.userFields.monthly_expire_date],
            last_login: new Date().toISOString().replace('T', ' ').substring(0, 19),
            created_at: userData[MINGDAO_CONFIG.userFields.created_at],
            updated_at: userData[MINGDAO_CONFIG.userFields.updated_at]
          }
        };
      } else {
        return {
          success: false,
          error_msg: '密码错误',
          error_code: 'INVALID_PASSWORD'
        };
      }
    } else {
      return {
        success: false,
        error_msg: '用户名不存在',
        error_code: 'USER_NOT_FOUND'
      };
    }

  } catch (error) {
    console.error('❌ 验证明道云用户失败:', error);
    return {
      success: false,
      error_msg: `网络错误: ${error}`,
      error_code: 'NETWORK_ERROR'
    };
  }
};

// 更新用户最后登录时间
const updateLastLoginTime = async (rowId: string): Promise<void> => {
  try {
    const currentTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
    
    const controls = [
      { controlId: MINGDAO_CONFIG.userFields.last_login, value: currentTime },
      { controlId: MINGDAO_CONFIG.userFields.updated_at, value: currentTime }
    ];

    const requestData = {
      appKey: MINGDAO_CONFIG.appKey,
      sign: generateSign(controls),
      worksheetId: MINGDAO_CONFIG.worksheets.users,
      rowId: rowId,
      controls: controls
    };

    await fetch(`${MINGDAO_API_BASE}/worksheet/editRow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    console.log('✅ 已更新最后登录时间');
  } catch (error) {
    console.error('⚠️ 更新最后登录时间失败:', error);
  }
};

// 检查用户名是否已存在
export const checkUsernameExists = async (username: string): Promise<boolean> => {
  try {
    const filters = [
      {
        controlId: MINGDAO_CONFIG.userFields.username,
        dataType: 2,
        spliceType: 1,
        filterType: 1,
        value: username
      }
    ];

    const requestData = {
      appKey: MINGDAO_CONFIG.appKey,
      sign: generateSign(filters),
      worksheetId: MINGDAO_CONFIG.worksheets.users,
      pageSize: 1,
      pageIndex: 1,
      listType: 0,
      controls: [MINGDAO_CONFIG.userFields.username],
      filters: filters
    };

    const response = await fetch(`${MINGDAO_API_BASE}/worksheet/getRows`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    const result = await response.json();
    return result.success && result.data && result.data.length > 0;
  } catch (error) {
    console.error('检查用户名失败:', error);
    return false;
  }
};

// 检查邮箱是否已存在
export const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    const filters = [
      {
        controlId: MINGDAO_CONFIG.userFields.email,
        dataType: 2,
        spliceType: 1,
        filterType: 1,
        value: email
      }
    ];

    const requestData = {
      appKey: MINGDAO_CONFIG.appKey,
      sign: generateSign(filters),
      worksheetId: MINGDAO_CONFIG.worksheets.users,
      pageSize: 1,
      pageIndex: 1,
      listType: 0,
      controls: [MINGDAO_CONFIG.userFields.email],
      filters: filters
    };

    const response = await fetch(`${MINGDAO_API_BASE}/worksheet/getRows`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    const result = await response.json();
    return result.success && result.data && result.data.length > 0;
  } catch (error) {
    console.error('检查邮箱失败:', error);
    return false;
  }
};
