#!/usr/bin/env python3
"""
测试明道云翻译设置功能
"""

import asyncio
import requests
import json

async def test_translation_settings():
    """测试翻译设置API"""
    
    base_url = "http://localhost:8000"
    test_user_rowid = "e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa"  # 从明道云数据中获取的用户rowid
    
    print("🧪 测试明道云翻译设置功能")
    print("=" * 60)
    
    # 1. 测试获取用户翻译设置（应该返回默认设置）
    print("\n1️⃣ 测试获取用户翻译设置")
    try:
        response = requests.get(f"{base_url}/api/translation-settings/{test_user_rowid}")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 获取失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 2. 测试保存用户翻译设置
    print("\n2️⃣ 测试保存用户翻译设置")
    test_settings = {
        "paragraph": {
            "font_family": "微软雅黑",
            "font_size": 12,
            "bold": False,
            "italic": False,
            "underline": False,
            "text_align": "inherit",
            "color": "#000000"
        },
        "table": {
            "font_family": "宋体",
            "font_size": 8,
            "bold": True,
            "italic": False,
            "underline": False,
            "text_align": "center",
            "color": "#333333"
        },
        "header": {
            "font_family": "黑体",
            "font_size": 14,
            "bold": True,
            "italic": False,
            "underline": True,
            "text_align": "center",
            "color": "#000080"
        },
        "enable_paragraph": True,
        "enable_table": True,
        "enable_header": True
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/translation-settings/{test_user_rowid}",
            json=test_settings,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 保存成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 保存失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 3. 再次获取用户翻译设置（应该返回刚才保存的设置）
    print("\n3️⃣ 验证保存的设置")
    try:
        response = requests.get(f"{base_url}/api/translation-settings/{test_user_rowid}")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 验证设置是否正确
            saved_settings = result.get("data", {})
            if (saved_settings.get("paragraph", {}).get("font_family") == "微软雅黑" and
                saved_settings.get("table", {}).get("font_size") == 8):
                print("🎉 设置验证成功！")
            else:
                print("⚠️ 设置验证失败，数据不匹配")
        else:
            print(f"❌ 获取失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 4. 测试重置设置
    print("\n4️⃣ 测试重置翻译设置")
    try:
        response = requests.delete(f"{base_url}/api/translation-settings/{test_user_rowid}")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ 重置成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 重置失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎊 测试完成！")

def test_sync():
    """同步测试函数"""
    asyncio.run(test_translation_settings())

if __name__ == "__main__":
    test_sync()
