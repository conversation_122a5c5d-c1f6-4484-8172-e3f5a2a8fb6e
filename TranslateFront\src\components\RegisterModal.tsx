import React, { useState } from 'react';
import { X, User, Mail, Lock, Phone, UserPlus, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { authService } from '../services/auth';
import { RegisterRequest } from '../types/document';
import './RegisterModal.css';

interface RegisterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRegisterSuccess: (user: any) => void;
  onSwitchToLogin?: () => void;
}

const RegisterModal: React.FC<RegisterModalProps> = ({
  isOpen,
  onClose,
  onRegisterSuccess,
  onSwitchToLogin
}) => {
  const [formData, setFormData] = useState<RegisterRequest>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<RegisterRequest>>({});
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // 处理输入变化
  const handleInputChange = (field: keyof RegisterRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // 清除结果消息
    if (result) {
      setResult(null);
    }
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Partial<RegisterRequest> = {};

    // 用户名验证
    if (!formData.username.trim()) {
      newErrors.username = '用户名不能为空';
    } else if (formData.username.length < 3) {
      newErrors.username = '用户名至少3个字符';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = '用户名只能包含字母、数字和下划线';
    }

    // 邮箱验证
    if (!formData.email.trim()) {
      newErrors.email = '邮箱不能为空';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    // 密码验证
    if (!formData.password) {
      newErrors.password = '密码不能为空';
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少6个字符';
    }

    // 确认密码验证
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    // 手机号验证（可选）
    if (formData.phone && !/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入有效的手机号';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理注册提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await authService.register(formData);

      if (response.success && response.data) {
        setResult({
          success: true,
          message: '注册成功！已分配1000字符免费额度'
        });

        // 通知父组件注册成功
        onRegisterSuccess(response.data);

        // 延迟关闭模态框
        setTimeout(() => {
          onClose();
          resetForm();
        }, 2000);
      } else {
        setResult({
          success: false,
          message: response.message || '注册失败'
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: '注册过程中发生错误，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '',
      phone: ''
    });
    setErrors({});
    setResult(null);
    setShowPassword(false);
    setShowConfirmPassword(false);
  };

  // 处理模态框关闭
  const handleClose = () => {
    if (!isLoading) {
      onClose();
      resetForm();
    }
  };

  // 处理背景点击
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="register-modal-overlay" onClick={handleBackdropClick}>
      <div className="register-modal">
        {/* 模态框头部 */}
        <div className="register-modal-header">
          <h2>
            <UserPlus size={24} />
            用户注册
          </h2>
          <button 
            className="close-btn" 
            onClick={handleClose}
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* 结果消息 */}
        {result && (
          <div className={`result-message ${result.success ? 'success' : 'error'}`}>
            {result.success ? (
              <CheckCircle size={20} />
            ) : (
              <AlertCircle size={20} />
            )}
            <span>{result.message}</span>
          </div>
        )}

        {/* 注册表单 */}
        <form onSubmit={handleSubmit} className="register-form">
          {/* 用户名 */}
          <div className="form-group">
            <label htmlFor="username">
              <User size={16} />
              用户名 *
            </label>
            <input
              id="username"
              type="text"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              placeholder="请输入用户名"
              className={errors.username ? 'error' : ''}
              disabled={isLoading}
            />
            {errors.username && (
              <span className="error-message">{errors.username}</span>
            )}
          </div>

          {/* 邮箱 */}
          <div className="form-group">
            <label htmlFor="email">
              <Mail size={16} />
              邮箱 *
            </label>
            <input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="请输入邮箱地址"
              className={errors.email ? 'error' : ''}
              disabled={isLoading}
            />
            {errors.email && (
              <span className="error-message">{errors.email}</span>
            )}
          </div>

          {/* 密码 */}
          <div className="form-group">
            <label htmlFor="password">
              <Lock size={16} />
              密码 *
            </label>
            <div className="password-input">
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                placeholder="请输入密码（至少6位）"
                className={errors.password ? 'error' : ''}
                disabled={isLoading}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {errors.password && (
              <span className="error-message">{errors.password}</span>
            )}
          </div>

          {/* 确认密码 */}
          <div className="form-group">
            <label htmlFor="confirmPassword">
              <Lock size={16} />
              确认密码 *
            </label>
            <div className="password-input">
              <input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                placeholder="请再次输入密码"
                className={errors.confirmPassword ? 'error' : ''}
                disabled={isLoading}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading}
              >
                {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {errors.confirmPassword && (
              <span className="error-message">{errors.confirmPassword}</span>
            )}
          </div>

          {/* 姓名 */}
          <div className="form-group">
            <label htmlFor="fullName">
              <User size={16} />
              姓名
            </label>
            <input
              id="fullName"
              type="text"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              placeholder="请输入真实姓名（可选）"
              disabled={isLoading}
            />
          </div>

          {/* 手机号 */}
          <div className="form-group">
            <label htmlFor="phone">
              <Phone size={16} />
              手机号
            </label>
            <input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="请输入手机号（可选）"
              className={errors.phone ? 'error' : ''}
              disabled={isLoading}
            />
            {errors.phone && (
              <span className="error-message">{errors.phone}</span>
            )}
          </div>

          {/* 提交按钮 */}
          <div className="form-actions">
            <button
              type="submit"
              className="register-btn"
              disabled={isLoading || result?.success}
            >
              {isLoading ? (
                <>
                  <div className="loading-spinner"></div>
                  注册中...
                </>
              ) : result?.success ? (
                <>
                  <CheckCircle size={16} />
                  注册成功
                </>
              ) : (
                <>
                  <UserPlus size={16} />
                  注册
                </>
              )}
            </button>

            <button
              type="button"
              className="cancel-btn"
              onClick={handleClose}
              disabled={isLoading}
            >
              取消
            </button>
          </div>
        </form>

        {/* 登录链接 */}
        {onSwitchToLogin && (
          <div className="login-link">
            <p>
              已有账号？
              <button
                type="button"
                className="link-btn"
                onClick={onSwitchToLogin}
                disabled={isLoading}
              >
                立即登录
              </button>
            </p>
          </div>
        )}

        {/* 注册说明 */}
        <div className="register-info">
          <h4>注册福利</h4>
          <ul>
            <li>🎁 免费获得1000字符翻译额度</li>
            <li>📊 详细的使用统计和配额管理</li>
            <li>💳 灵活的充值方式选择</li>
            <li>🔒 企业级数据安全保障</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default RegisterModal;
