#!/usr/bin/env python3
"""
分析翻译后的Word文档
"""

try:
    from docx import Document
    import sys
    import os
    
    # 文档路径
    doc_path = r"d:\mywork\Translate\SMP-FM-003-01 厂房档案标准管理规程_translated_zh_to_en.docx"
    
    if not os.path.exists(doc_path):
        print(f"❌ 文档不存在: {doc_path}")
        sys.exit(1)
    
    print(f"📄 分析文档: {doc_path}")
    print("=" * 80)
    
    # 打开文档
    doc = Document(doc_path)
    
    # 分析页眉
    print("\n🔍 **页眉分析**")
    print("-" * 40)
    
    for section_idx, section in enumerate(doc.sections):
        header = section.header
        if header:
            print(f"\n📋 Section {section_idx + 1} 页眉:")
            
            # 页眉段落
            for para_idx, para in enumerate(header.paragraphs):
                if para.text.strip():
                    print(f"  段落 {para_idx + 1}: {para.text[:100]}...")
            
            # 页眉表格
            for table_idx, table in enumerate(header.tables):
                print(f"\n  📊 页眉表格 {table_idx + 1}:")
                print(f"    行数: {len(table.rows)}, 列数: {len(table.rows[0].cells) if len(table.rows) > 0 else 0}")
                
                for row_idx, row in enumerate(table.rows):
                    print(f"    行 {row_idx + 1}:")
                    for col_idx, cell in enumerate(row.cells):
                        cell_text = ""
                        for para in cell.paragraphs:
                            cell_text += para.text + " "
                        cell_text = cell_text.strip()
                        if cell_text:
                            print(f"      列 {col_idx + 1}: {cell_text[:50]}...")
    
    # 分析正文段落（前10个）
    print("\n🔍 **正文段落分析（前10个）**")
    print("-" * 40)
    
    for para_idx, para in enumerate(doc.paragraphs[:10]):
        if para.text.strip():
            # 检查段落格式
            indent = "无"
            if para.paragraph_format.first_line_indent:
                indent = f"{para.paragraph_format.first_line_indent.pt:.1f}pt"
            
            alignment = "默认"
            if para.alignment:
                alignment = str(para.alignment)
            
            print(f"  段落 {para_idx + 1}:")
            print(f"    内容: {para.text[:80]}...")
            print(f"    首行缩进: {indent}")
            print(f"    对齐方式: {alignment}")
            
            # 检查字体
            for run in para.runs:
                if run.text.strip():
                    font_name = run.font.name or "默认"
                    font_size = f"{run.font.size.pt:.1f}pt" if run.font.size else "默认"
                    print(f"    字体: {font_name}, 大小: {font_size}")
                    break
            print()
    
    # 分析表格（第一个）
    print("\n🔍 **表格分析（第一个）**")
    print("-" * 40)
    
    if len(doc.tables) > 0:
        table = doc.tables[0]
        print(f"📊 第一个表格:")
        print(f"  行数: {len(table.rows)}, 列数: {len(table.rows[0].cells) if len(table.rows) > 0 else 0}")
        
        for row_idx, row in enumerate(table.rows[:3]):  # 只显示前3行
            print(f"  行 {row_idx + 1}:")
            for col_idx, cell in enumerate(row.cells):
                cell_text = ""
                for para in cell.paragraphs:
                    cell_text += para.text + " "
                cell_text = cell_text.strip()
                if cell_text:
                    print(f"    列 {col_idx + 1}: {cell_text[:50]}...")
    else:
        print("❌ 文档中没有找到表格")
    
    print("\n" + "=" * 80)
    print("✅ 分析完成")

except ImportError:
    print("❌ 需要安装 python-docx: pip install python-docx")
except Exception as e:
    print(f"❌ 分析失败: {e}")
    import traceback
    traceback.print_exc()
