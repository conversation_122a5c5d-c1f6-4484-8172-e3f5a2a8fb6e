#!/usr/bin/env python3
"""
测试导入是否有问题
"""

try:
    print("Testing imports...")
    
    print("1. Testing schemas.translation...")
    from app.schemas.translation import TranslationFormatSettings, TranslationSettings
    print("   ✅ schemas.translation imported successfully")
    
    print("2. Testing services.translation_service...")
    from app.services.translation_service import TranslationService
    print("   ✅ services.translation_service imported successfully")
    
    print("3. Testing routes.translate...")
    from app.routes.translate import router
    print("   ✅ routes.translate imported successfully")
    
    print("4. Testing main app...")
    from app.main import app
    print("   ✅ main app imported successfully")
    
    print("\n🎉 All imports successful! The server should start normally.")
    
except Exception as e:
    print(f"\n❌ Import error: {e}")
    import traceback
    traceback.print_exc()
