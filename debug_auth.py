"""
调试认证问题
"""
import asyncio
import httpx
import json

BASE_URL = "http://localhost:8000"

async def debug_auth():
    """调试认证流程"""
    async with httpx.AsyncClient() as client:
        print("🔍 调试认证问题")
        print("=" * 50)
        
        # 1. 测试登录
        print("1. 测试用户登录...")
        login_data = {
            "username": "testuser",
            "password": "testpass123"
        }
        
        try:
            response = await client.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
            print(f"   登录响应状态: {response.status_code}")
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data["access_token"]
                print(f"✅ 登录成功")
                print(f"   令牌类型: {token_data.get('token_type', 'unknown')}")
                print(f"   过期时间: {token_data.get('expires_in', 'unknown')} 秒")
                print(f"   令牌前20字符: {access_token[:20]}...")
                print(f"   令牌长度: {len(access_token)} 字符")
                
                # 2. 测试令牌验证
                print("\n2. 测试令牌验证...")
                headers = {"Authorization": f"Bearer {access_token}"}
                
                # 测试获取用户资料
                profile_response = await client.get(f"{BASE_URL}/api/v1/auth/profile", headers=headers)
                print(f"   用户资料响应状态: {profile_response.status_code}")
                
                if profile_response.status_code == 200:
                    profile = profile_response.json()
                    print(f"✅ 令牌验证成功")
                    print(f"   用户名: {profile['username']}")
                    print(f"   用户ID: {profile['id']}")
                else:
                    print(f"❌ 令牌验证失败")
                    print(f"   错误: {profile_response.text}")
                
                # 3. 测试文件上传接口（不上传文件，只测试认证）
                print("\n3. 测试文件上传接口认证...")
                
                # 创建一个简单的测试文件
                test_content = b"Hello, this is a test file."
                files = {"file": ("test.txt", test_content, "text/plain")}
                
                upload_response = await client.post(
                    f"{BASE_URL}/api/v1/upload/",
                    headers=headers,
                    files=files
                )
                
                print(f"   文件上传响应状态: {upload_response.status_code}")
                
                if upload_response.status_code == 401:
                    print(f"❌ 认证失败")
                    print(f"   错误: {upload_response.text}")
                elif upload_response.status_code == 400:
                    print(f"⚠️  认证通过，但文件格式不支持（这是正常的）")
                    print(f"   错误: {upload_response.text}")
                elif upload_response.status_code == 201:
                    print(f"✅ 文件上传成功")
                    print(f"   响应: {upload_response.json()}")
                else:
                    print(f"❓ 其他状态码: {upload_response.status_code}")
                    print(f"   响应: {upload_response.text}")
                
                # 4. 测试不同的认证头格式
                print("\n4. 测试不同的认证头格式...")
                
                # 测试1: 正确格式
                headers1 = {"Authorization": f"Bearer {access_token}"}
                test1 = await client.get(f"{BASE_URL}/api/v1/auth/profile", headers=headers1)
                print(f"   Bearer {access_token[:10]}... : {test1.status_code}")
                
                # 测试2: 缺少 Bearer
                headers2 = {"Authorization": access_token}
                test2 = await client.get(f"{BASE_URL}/api/v1/auth/profile", headers=headers2)
                print(f"   {access_token[:10]}... : {test2.status_code}")
                
                # 测试3: 错误的令牌
                headers3 = {"Authorization": "Bearer invalid_token"}
                test3 = await client.get(f"{BASE_URL}/api/v1/auth/profile", headers=headers3)
                print(f"   Bearer invalid_token : {test3.status_code}")
                
                # 5. 检查令牌内容（解码但不验证）
                print("\n5. 检查令牌内容...")
                try:
                    import base64
                    import json
                    
                    # JWT 令牌格式: header.payload.signature
                    parts = access_token.split('.')
                    if len(parts) == 3:
                        # 解码 payload（添加必要的填充）
                        payload = parts[1]
                        # 添加填充
                        payload += '=' * (4 - len(payload) % 4)
                        decoded_payload = base64.urlsafe_b64decode(payload)
                        payload_data = json.loads(decoded_payload)
                        
                        print(f"   令牌载荷:")
                        for key, value in payload_data.items():
                            if key == 'exp':
                                import datetime
                                exp_time = datetime.datetime.fromtimestamp(value)
                                print(f"     {key}: {value} ({exp_time})")
                            else:
                                print(f"     {key}: {value}")
                    else:
                        print(f"   ❌ 令牌格式不正确，部分数量: {len(parts)}")
                        
                except Exception as e:
                    print(f"   ❌ 解码令牌失败: {e}")
                
            else:
                print(f"❌ 登录失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return
                
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return
        
        print("\n" + "=" * 50)
        print("🎯 调试总结:")
        print("   - 检查令牌是否正确生成")
        print("   - 检查令牌是否过期")
        print("   - 检查认证头格式是否正确")
        print("   - 检查服务器时间是否同步")

if __name__ == "__main__":
    asyncio.run(debug_auth())
