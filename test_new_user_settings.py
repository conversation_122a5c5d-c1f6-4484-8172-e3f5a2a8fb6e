#!/usr/bin/env python3
"""
测试新用户的翻译设置保存功能
"""

import requests
import json

def test_new_user_settings():
    """测试新用户的翻译设置"""
    
    print("🧪 测试新用户翻译设置保存功能")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 使用一个新的测试用户rowid（您可以替换为实际的新用户rowid）
    new_user_rowid = "test-new-user-12345"  # 请替换为您的新测试账号的rowid
    
    test_settings = {
        "paragraph": {
            "font_family": "微软雅黑",
            "font_size": 12,
            "bold": False,
            "italic": False,
            "underline": False,
            "text_align": "inherit",
            "color": "#000000"
        },
        "table": {
            "font_family": "宋体",
            "font_size": 8,
            "bold": True,
            "italic": False,
            "underline": False,
            "text_align": "center",
            "color": "#333333"
        },
        "header": {
            "font_family": "黑体",
            "font_size": 14,
            "bold": True,
            "italic": False,
            "underline": True,
            "text_align": "center",
            "color": "#000080"
        },
        "enable_paragraph": True,
        "enable_table": True,
        "enable_header": True
    }
    
    print(f"🆔 测试用户rowid: {new_user_rowid}")
    print("⚠️  请确保将上面的rowid替换为您的新测试账号的实际rowid")
    
    # 1. 测试服务器连接
    print("\n1️⃣ 测试服务器连接")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
            return
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器已启动")
        return
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return
    
    # 2. 测试获取用户设置（应该返回默认设置，因为是新用户）
    print("\n2️⃣ 测试获取新用户设置（应该返回默认设置）")
    try:
        response = requests.get(f"{base_url}/api/translation-settings/{new_user_rowid}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取成功（默认设置）:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取测试失败: {e}")
    
    # 3. 测试保存新用户设置
    print("\n3️⃣ 测试保存新用户翻译设置")
    try:
        response = requests.post(
            f"{base_url}/api/translation-settings/{new_user_rowid}",
            json=test_settings,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 保存成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 保存失败:")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 保存测试失败: {e}")
    
    # 4. 再次获取用户设置（应该返回刚才保存的设置）
    print("\n4️⃣ 验证保存的设置")
    try:
        response = requests.get(f"{base_url}/api/translation-settings/{new_user_rowid}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 验证设置是否正确
            saved_settings = result.get("data", {})
            if (saved_settings.get("paragraph", {}).get("font_family") == "微软雅黑" and
                saved_settings.get("table", {}).get("font_size") == 8):
                print("🎉 设置验证成功！数据已正确保存到明道云")
            else:
                print("⚠️ 设置验证失败，数据不匹配")
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
    
    # 5. 测试更新设置
    print("\n5️⃣ 测试更新已有设置")
    updated_settings = test_settings.copy()
    updated_settings["paragraph"]["font_family"] = "宋体"
    updated_settings["paragraph"]["font_size"] = 14
    
    try:
        response = requests.post(
            f"{base_url}/api/translation-settings/{new_user_rowid}",
            json=updated_settings,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 更新成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 更新失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 更新测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎊 测试完成！")
    print("\n💡 请到明道云后台查看 yhfygssz 表单是否有新记录")
    print(f"   用户rowid: {new_user_rowid}")
    print("   应该能看到翻译设置JSON字段中保存了您的设置")

if __name__ == "__main__":
    print("⚠️  重要提醒：")
    print("   请在运行此脚本前，将 new_user_rowid 替换为您的新测试账号的实际rowid")
    print("   您可以在明道云用户表中找到这个rowid")
    print()
    
    confirm = input("确认已替换用户rowid？(y/n): ")
    if confirm.lower() == 'y':
        test_new_user_settings()
    else:
        print("请先替换用户rowid后再运行测试")
