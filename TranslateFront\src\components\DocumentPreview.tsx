import React, { useState, useEffect } from 'react';
import { X, Download, AlertCircle, Loader } from 'lucide-react';
import { DocumentFile } from '../types/document';
import { getFileExtension } from '../utils/download';

interface DocumentPreviewProps {
  file: DocumentFile | null;
  isOpen: boolean;
  onClose: () => void;
  onDownload: (file: DocumentFile) => void;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  file,
  isOpen,
  onClose,
  onDownload
}) => {
  const [previewContent, setPreviewContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [previewMode, setPreviewMode] = useState<'iframe' | 'html' | 'unsupported'>('unsupported');

  useEffect(() => {
    if (!file || !isOpen) {
      setPreviewContent('');
      setError('');
      setPreviewMode('unsupported');
      return;
    }

    loadPreview(file);
  }, [file, isOpen]);

  const loadPreview = async (file: DocumentFile) => {
    setLoading(true);
    setError('');
    
    try {
      const extension = getFileExtension(file.original_file_name);
      
      switch (extension) {
        case 'pdf':
          await loadPdfPreview(file);
          break;
        case 'doc':
        case 'docx':
          await loadWordPreview(file);
          break;
        case 'txt':
          await loadTextPreview(file);
          break;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
          await loadImagePreview(file);
          break;
        default:
          setPreviewMode('unsupported');
          setError(`不支持预览 .${extension} 格式的文件`);
      }
    } catch (err) {
      console.error('预览加载失败:', err);
      setError('预览加载失败，请尝试下载文件查看');
      setPreviewMode('unsupported');
    } finally {
      setLoading(false);
    }
  };

  const loadPdfPreview = async (file: DocumentFile) => {
    // PDF 可以直接在 iframe 中预览，使用 DownloadUrl
    setPreviewContent(file.DownloadUrl);
    setPreviewMode('iframe');
  };

  const loadImagePreview = async (file: DocumentFile) => {
    // 图片可以直接显示，使用 DownloadUrl
    setPreviewContent(file.DownloadUrl);
    setPreviewMode('iframe');
  };

  const loadTextPreview = async (file: DocumentFile) => {
    try {
      const response = await fetch(file.DownloadUrl);
      if (!response.ok) {
        throw new Error('无法获取文件内容');
      }
      const text = await response.text();
      setPreviewContent(text);
      setPreviewMode('html');
    } catch (error) {
      throw new Error('文本文件加载失败');
    }
  };

  const loadWordPreview = async (file: DocumentFile) => {
    try {
      // 尝试使用 mammoth.js 预览 Word 文档
      const mammoth = await import('mammoth');
      
      // 获取文件数据，使用 DownloadUrl
      const response = await fetch(file.DownloadUrl);
      if (!response.ok) {
        throw new Error('无法获取文件数据');
      }
      
      const arrayBuffer = await response.arrayBuffer();
      
      // 转换为 HTML
      const result = await mammoth.convertToHtml({ arrayBuffer });
      setPreviewContent(result.value);
      setPreviewMode('html');
      
      // 显示警告信息（如果有）
      if (result.messages.length > 0) {
        console.warn('Word 转换警告:', result.messages);
      }
    } catch (error) {
      console.error('Word 预览失败:', error);
      // 回退到 iframe 预览，使用 DownloadUrl
      setPreviewContent(file.DownloadUrl);
      setPreviewMode('iframe');
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen || !file) {
    return null;
  }

  return (
    <div className="preview-modal" onClick={handleBackdropClick}>
      <div className="preview-container">
        <div className="preview-header">
          <div className="preview-title">
            <h3>{file.original_file_name}</h3>
            <p className="file-info">
              大小: {(file.file_size / 1024 / 1024).toFixed(2)} MB
            </p>
          </div>
          <div className="preview-actions">
            {file.allow_down && (
              <button
                className="btn btn-primary"
                onClick={() => onDownload(file)}
              >
                <Download size={16} />
                下载
              </button>
            )}
            <button className="btn btn-outline" onClick={onClose}>
              <X size={16} />
              关闭
            </button>
          </div>
        </div>

        <div className="preview-content">
          {loading && (
            <div className="preview-loading">
              <Loader className="spinner" size={32} />
              <p>正在加载预览...</p>
            </div>
          )}

          {error && (
            <div className="preview-error">
              <AlertCircle size={48} />
              <h4>预览失败</h4>
              <p>{error}</p>
              {file.allow_down && (
                <button
                  className="btn btn-primary"
                  onClick={() => onDownload(file)}
                >
                  <Download size={16} />
                  下载文件
                </button>
              )}
            </div>
          )}

          {!loading && !error && previewMode === 'iframe' && (
            <iframe
              src={previewContent}
              className="preview-iframe"
              title={file.original_file_name}
              sandbox="allow-same-origin allow-scripts"
            />
          )}

          {!loading && !error && previewMode === 'html' && (
            <div 
              className="preview-html"
              dangerouslySetInnerHTML={{ __html: previewContent }}
            />
          )}

          {!loading && !error && previewMode === 'unsupported' && (
            <div className="preview-unsupported">
              <AlertCircle size={48} />
              <h4>不支持预览</h4>
              <p>此文件格式暂不支持在线预览</p>
              {file.allow_down && (
                <button
                  className="btn btn-primary"
                  onClick={() => onDownload(file)}
                >
                  <Download size={16} />
                  下载文件查看
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentPreview;
