.app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.app-header {
  background-color: white;
  border-bottom: 1px solid #dee2e6;
  padding: 16px 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #495057;
}

.app-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

/* 左侧边栏 */
.sidebar {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex-shrink: 0;
  height: calc(100vh - 80px); /* 减去头部高度 */
  overflow-y: auto; /* 添加垂直滚动条 */
  padding-right: 8px; /* 为滚动条留出空间 */
  box-sizing: border-box;
}

/* 美化滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 8px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.upload-section,
.actions-section,
.filter-section {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 20px;
}

.upload-section h3,
.actions-section h3,
.filter-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-buttons .btn {
  width: 100%;
  justify-content: center;
  padding: 12px 16px;
  font-size: 14px;
}

/* 搜索过滤区域 */
.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #6c757d;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search-input::placeholder {
  color: #6c757d;
}

/* 右侧主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  position: relative;
  min-height: 0; /* 确保可以滚动 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .app-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .sidebar {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
    gap: 16px;
  }
  
  .upload-section,
  .actions-section,
  .filter-section {
    min-width: 280px;
    flex-shrink: 0;
  }
}

@media (max-width: 768px) {
  .app-content {
    padding: 16px;
  }
  
  .app-header {
    padding: 12px 16px;
  }
  
  .app-header h1 {
    font-size: 20px;
  }
  
  .sidebar {
    flex-direction: column;
    gap: 12px;
  }
  
  .upload-section,
  .actions-section,
  .filter-section {
    min-width: auto;
    padding: 16px;
  }
  
  .action-buttons {
    gap: 8px;
  }
  
  .action-buttons .btn {
    padding: 10px 14px;
    font-size: 13px;
  }

  /* 预览响应式样式 */
  .preview-header {
    padding: 12px 16px;
    flex-wrap: wrap;
    gap: 12px;
  }

  .preview-title {
    font-size: 14px;
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
  }

  .back-btn {
    padding: 6px 12px;
    font-size: 13px;
  }
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 滚动条优化 */
.sidebar::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.sidebar::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.upload-section,
.actions-section,
.filter-section,
.main-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 分页组件样式 */
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.pagination-stats {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.pagination-stats .font-medium {
  color: #007bff;
  font-weight: 600;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 14px;
  color: #6c757d;
}

.page-size-select {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: all 0.2s ease;
}

.page-size-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.page-size-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-numbers {
  display: flex;
  align-items: center;
  gap: 2px;
  margin: 0 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 8px;
  border: 1px solid #dee2e6;
  background-color: white;
  color: #495057;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.pagination-nav-btn {
  color: #6c757d;
}

.pagination-nav-btn:hover:not(:disabled) {
  color: #007bff;
  background-color: #e7f3ff;
  border-color: #007bff;
}

.pagination-number-btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  font-weight: 600;
}

.pagination-number-btn.active:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.pagination-ellipsis {
  padding: 8px 4px;
  color: #6c757d;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 焦点状态优化 */
.btn:focus,
.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 禁用状态 */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* 预览容器样式 */
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  flex: 1;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #6c757d;
  background-color: white;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background-color: #6c757d;
  color: white;
  border-color: #6c757d;
}

.preview-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-frame-container {
  flex: 1;
  position: relative;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-frame {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

/* 明道云功能区域样式 */
.mingdao-section {
  margin-bottom: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #e3f2fd, #ffffff);
  border-radius: 8px;
  border: 2px solid #2196f3;
}

.mingdao-section h3 {
  margin: 0 0 15px 0;
  color: #1976d2;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mingdao-section h3:before {
  content: "☁️";
  font-size: 1.2rem;
}

.mingdao-component-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
  border: 2px solid #e3f2fd;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 认证按钮区域 */
.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

.auth-buttons .btn {
  font-size: 0.85rem;
  padding: 8px 12px;
}

/* 按钮样式增强 */
.btn-secondary {
  background: #6c757d;
  color: white;
  border: 1px solid #6c757d;
}

.btn-secondary:hover {
  background: #5a6268;
  border-color: #545b62;
}

.btn-success {
  background: #28a745;
  color: white;
  border: 1px solid #28a745;
}

.btn-success:hover {
  background: #218838;
  border-color: #1e7e34;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    min-height: auto;
  }

  .main-content {
    width: 100%;
    height: auto;
    min-height: 60vh;
  }

  .mingdao-section {
    padding: 12px;
  }

  .mingdao-section h3 {
    font-size: 0.9rem;
  }

  .mingdao-component-container {
    border-radius: 8px;
  }
}
