"""
用户设置相关API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
import logging

from app.db.database import get_async_db
from app.models.user import User
from app.api.auth import get_current_user
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/user", tags=["用户设置"])


class TranslationFormatSettings(BaseModel):
    """翻译格式设置模型"""
    paragraph: Dict[str, Any] = {
        "font_family": "Arial",
        "font_size": 10.5,
        "bold": False,
        "italic": False,
        "underline": False,
        "text_align": "inherit",
        "color": "#000000"
    }
    table: Dict[str, Any] = {
        "font_family": "Arial", 
        "font_size": 6,
        "bold": False,
        "italic": False,
        "underline": False,
        "text_align": "inherit",
        "color": "#000000"
    }
    header: Dict[str, Any] = {
        "font_family": "Arial",
        "font_size": 10.5,
        "bold": False,
        "italic": False,
        "underline": False,
        "text_align": "inherit",
        "color": "#000000"
    }
    enable_paragraph: bool = True
    enable_table: bool = True
    enable_header: bool = True


@router.get("/translation-format-settings")
async def get_translation_format_settings(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取用户的翻译格式设置"""
    try:
        settings = current_user.get_translation_format_settings()
        logger.info(f"用户 {current_user.username} 获取翻译格式设置")
        
        return {
            "success": True,
            "data": settings,
            "message": "获取翻译格式设置成功"
        }
    except Exception as e:
        logger.error(f"获取翻译格式设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取翻译格式设置失败"
        )


@router.post("/translation-format-settings")
async def update_translation_format_settings(
    settings: TranslationFormatSettings,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """更新用户的翻译格式设置"""
    try:
        # 转换为字典
        settings_dict = settings.dict()
        
        # 保存到数据库
        current_user.set_translation_format_settings(settings_dict)
        await db.commit()
        
        logger.info(f"用户 {current_user.username} 更新翻译格式设置: {settings_dict}")
        
        return {
            "success": True,
            "data": settings_dict,
            "message": "翻译格式设置更新成功"
        }
    except Exception as e:
        logger.error(f"更新翻译格式设置失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新翻译格式设置失败"
        )


@router.post("/translation-format-settings/reset")
async def reset_translation_format_settings(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """重置用户的翻译格式设置为默认值"""
    try:
        # 清空设置，这样会使用默认值
        current_user.translation_format_settings = None
        await db.commit()
        
        # 获取默认设置
        default_settings = current_user.get_translation_format_settings()
        
        logger.info(f"用户 {current_user.username} 重置翻译格式设置为默认值")
        
        return {
            "success": True,
            "data": default_settings,
            "message": "翻译格式设置已重置为默认值"
        }
    except Exception as e:
        logger.error(f"重置翻译格式设置失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置翻译格式设置失败"
        )
