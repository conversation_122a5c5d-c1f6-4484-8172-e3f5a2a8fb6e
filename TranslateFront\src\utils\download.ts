import { DocumentFile } from '../types/document';

/**
 * 下载单个文件
 */
export const downloadFile = async (file: DocumentFile): Promise<void> => {
  try {
    // 使用 original_file_full_path 作为下载链接
    const downloadUrl = file.original_file_full_path || file.DownloadUrl;

    // 创建一个隐藏的 a 标签来触发下载
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = file.original_file_name;
    link.target = '_blank';

    // 添加到 DOM 并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);

    console.log(`开始下载文件: ${file.original_file_name}`);
    console.log(`下载链接: ${downloadUrl}`);
  } catch (error) {
    console.error('下载文件失败:', error);
    throw new Error(`下载文件 ${file.original_file_name} 失败`);
  }
};

/**
 * 批量下载文件
 */
export const downloadMultipleFiles = async (files: DocumentFile[]): Promise<void> => {
  if (files.length === 0) {
    throw new Error('没有可下载的文件');
  }

  try {
    // 逐个下载文件，避免浏览器阻止多个下载
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (!file.allow_down) {
        console.warn(`文件 ${file.original_file_name} 不允许下载，跳过`);
        continue;
      }

      // 添加延迟避免浏览器限制
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      await downloadFile(file);
    }
    
    console.log(`批量下载完成，共 ${files.length} 个文件`);
  } catch (error) {
    console.error('批量下载失败:', error);
    throw error;
  }
};

/**
 * 获取文件的 Blob 数据（用于预览）
 */
export const fetchFileBlob = async (url: string): Promise<Blob> => {
  try {
    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors',
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.blob();
  } catch (error) {
    console.error('获取文件数据失败:', error);
    throw new Error('无法获取文件数据');
  }
};

/**
 * 检查文件是否可以下载
 */
export const canDownloadFile = (file: DocumentFile): boolean => {
  return file.allow_down && !!file.DownloadUrl;
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 获取文件扩展名
 */
export const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || '';
};

/**
 * 检查是否为支持的文档格式
 */
export const isSupportedDocument = (filename: string): boolean => {
  const supportedExtensions = ['doc', 'docx', 'pdf', 'txt', 'xlsx', 'xls', 'pptx', 'ppt'];
  const extension = getFileExtension(filename);
  return supportedExtensions.includes(extension);
};

/**
 * 生成下载进度回调
 */
export const createDownloadProgress = (
  onProgress?: (progress: number) => void
) => {
  return async (url: string, filename: string): Promise<void> => {
    try {
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const contentLength = response.headers.get('content-length');
      const total = contentLength ? parseInt(contentLength, 10) : 0;
      
      if (!response.body) {
        throw new Error('Response body is null');
      }
      
      const reader = response.body.getReader();
      const chunks: Uint8Array[] = [];
      let loaded = 0;
      
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        chunks.push(value);
        loaded += value.length;
        
        if (onProgress && total > 0) {
          onProgress((loaded / total) * 100);
        }
      }
      
      // 创建 Blob 并下载
      const blob = new Blob(chunks);
      const blobUrl = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理
      URL.revokeObjectURL(blobUrl);
      
    } catch (error) {
      console.error('下载失败:', error);
      throw error;
    }
  };
};
