"""
使用与 VBA 宏完全相同的方式测试 Azure API
"""
import asyncio
import httpx
import json
import uuid

# 您的 Azure 配置
API_KEY = "FpyApWXGNX8MvH59fvH57g89cNNFT0lMble8NkiEGnhoRNNHd1t5JQQJ99BGAC3pKaRXJ3w3AAAbACOGWVpY"
REGION = "eastasia"

def escape_json(text):
    """模拟 VBA 的 EscapeJSON 函数"""
    text = text.replace("\\", "\\\\")
    text = text.replace('"', '\\"')
    text = text.replace('\r', '\\r')
    text = text.replace('\n', '\\n')
    return text

async def test_vba_style_translation():
    """使用与 VBA 宏完全相同的方式测试翻译"""
    print("🔍 使用 VBA 风格测试 Azure 翻译 API")
    print("=" * 50)
    
    # 测试文本
    test_text = "这是一个测试文档。人工智能技术正在快速发展。"
    
    # 1. 使用 VBA 宏中的确切 URL 和参数
    print("1. 使用 VBA 宏的确切 URL...")
    try:
        url = "https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&from=zh&to=en"
        
        # 构建请求体（与 VBA 宏相同）
        escaped_text = escape_json(test_text)
        body = f'[{{"Text":"{escaped_text}"}}]'
        
        headers = {
            'Ocp-Apim-Subscription-Key': API_KEY,
            'Ocp-Apim-Subscription-Region': REGION,
            'Content-Type': 'application/json'
        }
        
        print(f"   URL: {url}")
        print(f"   请求体: {body}")
        print(f"   请求头: {headers}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, content=body)
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 翻译成功!")
                print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 提取翻译文本（模拟 VBA 解析）
                if result and len(result) > 0 and 'translations' in result[0]:
                    translated_text = result[0]['translations'][0]['text']
                    print(f"   原文: {test_text}")
                    print(f"   译文: {translated_text}")
                
            else:
                print(f"❌ 翻译失败")
                print(f"   响应内容: {response.text}")
                
    except Exception as e:
        print(f"❌ 请求错误: {e}")
    
    print()
    
    # 2. 测试不同的 API 版本
    print("2. 测试不同的 API 版本...")
    
    api_versions = ["3.0", "2.0", "1.0"]
    
    for version in api_versions:
        try:
            url = f"https://api.cognitive.microsofttranslator.com/translate?api-version={version}&from=zh&to=en"
            
            headers = {
                'Ocp-Apim-Subscription-Key': API_KEY,
                'Ocp-Apim-Subscription-Region': REGION,
                'Content-Type': 'application/json'
            }
            
            body = f'[{{"Text":"{escape_json("测试")}"}}]'
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, content=body)
                
                print(f"   API 版本 {version}: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"   ✅ 版本 {version} 可用")
                    break
                elif response.status_code == 404:
                    print(f"   ❌ 版本 {version} 不存在")
                else:
                    print(f"   ⚠️  版本 {version} 状态: {response.status_code}")
                    
        except Exception as e:
            print(f"   ❌ 版本 {version} 错误: {e}")
    
    print()
    
    # 3. 测试密钥验证
    print("3. 测试密钥验证...")
    
    # 使用简单的请求测试密钥
    try:
        url = "https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&from=en&to=zh"
        
        headers = {
            'Ocp-Apim-Subscription-Key': API_KEY,
            'Ocp-Apim-Subscription-Region': REGION,
            'Content-Type': 'application/json'
        }
        
        body = '[{"Text":"hello"}]'
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, content=body)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ API 密钥有效")
            elif response.status_code == 401:
                print("   ❌ API 密钥无效")
            elif response.status_code == 403:
                print("   ❌ API 密钥无权限")
            elif response.status_code == 404:
                print("   ❌ API 端点不存在")
            else:
                print(f"   ⚠️  其他错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
    except Exception as e:
        print(f"   ❌ 密钥验证错误: {e}")
    
    print()
    
    # 4. 测试网络连接
    print("4. 测试网络连接...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("https://api.cognitive.microsofttranslator.com")
            print(f"   基础连接状态: {response.status_code}")
            
            if response.status_code in [200, 404, 405]:
                print("   ✅ 网络连接正常")
            else:
                print(f"   ⚠️  连接状态: {response.status_code}")
                
    except Exception as e:
        print(f"   ❌ 网络连接错误: {e}")
    
    print()
    print("🎯 诊断总结:")
    print("   - 如果所有测试都返回 404，可能是 API 端点已更改")
    print("   - 如果返回 401/403，可能是密钥问题")
    print("   - 如果返回 200，说明 API 工作正常")
    print("   - 建议检查 Azure 门户中的服务状态和密钥")

if __name__ == "__main__":
    asyncio.run(test_vba_style_translation())
