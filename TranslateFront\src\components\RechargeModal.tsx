import React, { useState, useEffect } from 'react';
import { X, CreditCard, Smartphone, Building, DollarSign, Zap, AlertCircle, CheckCircle } from 'lucide-react';
import { rechargeService } from '../services/recharge';
import { authService } from '../services/auth';
import { RechargeRequest, RechargeRecord } from '../types/document';
import './RechargeModal.css';

interface RechargeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRechargeSuccess?: () => void;
}

const RechargeModal: React.FC<RechargeModalProps> = ({
  isOpen,
  onClose,
  onRechargeSuccess
}) => {
  const [formData, setFormData] = useState<RechargeRequest>({
    amount: 50,
    type: 'balance',
    method: 'alipay'
  });

  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    orderId?: string;
  } | null>(null);

  const [rechargeRecords, setRechargeRecords] = useState<RechargeRecord[]>([]);
  const [showRecords, setShowRecords] = useState(false);

  // 充值金额选项
  const amountOptions = [
    { value: 20, label: '¥20', popular: false },
    { value: 50, label: '¥50', popular: true },
    { value: 100, label: '¥100', popular: true },
    { value: 200, label: '¥200', popular: false },
    { value: 500, label: '¥500', popular: false }
  ];

  // 支付方式选项
  const paymentMethods = [
    { value: 'alipay', label: '支付宝', icon: Smartphone, color: '#1677ff' },
    { value: 'wechat', label: '微信支付', icon: Smartphone, color: '#07c160' },
    { value: 'bank', label: '银行卡', icon: CreditCard, color: '#722ed1' }
  ];

  // 充值类型选项
  const rechargeTypes = [
    { 
      value: 'balance', 
      label: '余额充值', 
      icon: DollarSign, 
      description: '充值到账户余额，可用于各种消费',
      color: '#52c41a'
    },
    { 
      value: 'quota', 
      label: '配额充值', 
      icon: Zap, 
      description: '直接增加翻译字符配额',
      color: '#1890ff'
    }
  ];

  // 加载充值记录
  const loadRechargeRecords = async () => {
    try {
      const response = await rechargeService.getRechargeRecords(1, 5);
      if (response.success && response.data) {
        setRechargeRecords(response.data);
      }
    } catch (error) {
      console.error('加载充值记录失败:', error);
    }
  };

  useEffect(() => {
    if (isOpen && showRecords) {
      loadRechargeRecords();
    }
  }, [isOpen, showRecords]);

  // 处理输入变化
  const handleInputChange = (field: keyof RechargeRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除结果消息
    if (result) {
      setResult(null);
    }
  };

  // 处理自定义金额输入
  const handleCustomAmountChange = (value: string) => {
    const amount = parseFloat(value) || 0;
    if (amount >= 1 && amount <= 10000) {
      handleInputChange('amount', amount);
    }
  };

  // 处理充值提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.amount < 1) {
      setResult({
        success: false,
        message: '充值金额不能少于1元'
      });
      return;
    }

    if (formData.amount > 10000) {
      setResult({
        success: false,
        message: '单次充值金额不能超过10000元'
      });
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      // 创建充值订单
      const orderResponse = await rechargeService.createRechargeOrder(formData);

      if (orderResponse.success && orderResponse.data) {
        setResult({
          success: true,
          message: '充值订单创建成功，正在跳转支付...',
          orderId: orderResponse.data.orderId
        });

        // 模拟支付处理
        setTimeout(async () => {
          try {
            const paymentResponse = await rechargeService.processPayment(orderResponse.data!.orderId);
            
            if (paymentResponse.success) {
              setResult({
                success: true,
                message: '充值成功！余额已更新'
              });

              // 刷新用户信息
              await authService.refreshUserInfo();
              
              // 通知父组件
              onRechargeSuccess?.();

              // 延迟关闭模态框
              setTimeout(() => {
                onClose();
                resetForm();
              }, 2000);
            } else {
              setResult({
                success: false,
                message: paymentResponse.message || '支付失败'
              });
            }
          } catch (error) {
            setResult({
              success: false,
              message: '支付处理失败，请稍后重试'
            });
          }
        }, 2000);

      } else {
        setResult({
          success: false,
          message: orderResponse.message || '创建订单失败'
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: '充值过程中发生错误，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      amount: 50,
      type: 'balance',
      method: 'alipay'
    });
    setResult(null);
    setShowRecords(false);
  };

  // 处理模态框关闭
  const handleClose = () => {
    if (!isLoading) {
      onClose();
      resetForm();
    }
  };

  // 处理背景点击
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // 获取当前用户
  const currentUser = authService.getCurrentUser();

  if (!isOpen) return null;

  return (
    <div className="recharge-modal-overlay" onClick={handleBackdropClick}>
      <div className="recharge-modal">
        {/* 模态框头部 */}
        <div className="recharge-modal-header">
          <h2>
            <CreditCard size={24} />
            账户充值
          </h2>
          <button 
            className="close-btn" 
            onClick={handleClose}
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* 用户信息 */}
        {currentUser && (
          <div className="user-info-section">
            <div className="balance-info">
              <div className="balance-item">
                <span className="label">当前余额</span>
                <span className="value">¥{currentUser.balance.toFixed(2)}</span>
              </div>
              <div className="balance-item">
                <span className="label">可用配额</span>
                <span className="value">{(currentUser.totalQuota - currentUser.usedQuota).toLocaleString()}字符</span>
              </div>
            </div>
          </div>
        )}

        {/* 结果消息 */}
        {result && (
          <div className={`result-message ${result.success ? 'success' : 'error'}`}>
            {result.success ? (
              <CheckCircle size={20} />
            ) : (
              <AlertCircle size={20} />
            )}
            <span>{result.message}</span>
          </div>
        )}

        {/* 充值表单 */}
        <form onSubmit={handleSubmit} className="recharge-form">
          {/* 充值类型选择 */}
          <div className="form-section">
            <h3>充值类型</h3>
            <div className="type-options">
              {rechargeTypes.map((type) => (
                <label key={type.value} className="type-option">
                  <input
                    type="radio"
                    name="type"
                    value={type.value}
                    checked={formData.type === type.value}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    disabled={isLoading}
                  />
                  <div className="type-card" style={{ borderColor: type.color }}>
                    <type.icon size={24} style={{ color: type.color }} />
                    <div className="type-info">
                      <div className="type-label">{type.label}</div>
                      <div className="type-description">{type.description}</div>
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 充值金额选择 */}
          <div className="form-section">
            <h3>充值金额</h3>
            <div className="amount-options">
              {amountOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  className={`amount-btn ${formData.amount === option.value ? 'active' : ''} ${option.popular ? 'popular' : ''}`}
                  onClick={() => handleInputChange('amount', option.value)}
                  disabled={isLoading}
                >
                  {option.label}
                  {option.popular && <span className="popular-tag">推荐</span>}
                </button>
              ))}
            </div>
            
            {/* 自定义金额 */}
            <div className="custom-amount">
              <label>自定义金额（¥1-¥10000）</label>
              <input
                type="number"
                min="1"
                max="10000"
                step="1"
                value={formData.amount}
                onChange={(e) => handleCustomAmountChange(e.target.value)}
                placeholder="请输入金额"
                disabled={isLoading}
              />
            </div>
          </div>

          {/* 支付方式选择 */}
          <div className="form-section">
            <h3>支付方式</h3>
            <div className="payment-methods">
              {paymentMethods.map((method) => (
                <label key={method.value} className="payment-method">
                  <input
                    type="radio"
                    name="method"
                    value={method.value}
                    checked={formData.method === method.value}
                    onChange={(e) => handleInputChange('method', e.target.value)}
                    disabled={isLoading}
                  />
                  <div className="method-card" style={{ borderColor: method.color }}>
                    <method.icon size={24} style={{ color: method.color }} />
                    <span>{method.label}</span>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="form-actions">
            <button
              type="submit"
              className="recharge-btn"
              disabled={isLoading || result?.success}
            >
              {isLoading ? (
                <>
                  <div className="loading-spinner"></div>
                  处理中...
                </>
              ) : result?.success ? (
                <>
                  <CheckCircle size={16} />
                  充值成功
                </>
              ) : (
                <>
                  <CreditCard size={16} />
                  立即充值 ¥{formData.amount}
                </>
              )}
            </button>

            <button
              type="button"
              className="records-btn"
              onClick={() => setShowRecords(!showRecords)}
              disabled={isLoading}
            >
              {showRecords ? '隐藏记录' : '查看记录'}
            </button>
          </div>
        </form>

        {/* 充值记录 */}
        {showRecords && (
          <div className="records-section">
            <h3>最近充值记录</h3>
            {rechargeRecords.length > 0 ? (
              <div className="records-list">
                {rechargeRecords.map((record) => (
                  <div key={record.id} className="record-item">
                    <div className="record-info">
                      <div className="record-amount">¥{record.amount}</div>
                      <div className="record-details">
                        <div className="record-type">{record.type === 'balance' ? '余额充值' : '配额充值'}</div>
                        <div className="record-time">{new Date(record.createdAt).toLocaleString()}</div>
                      </div>
                    </div>
                    <div className={`record-status ${record.status}`}>
                      {record.status === 'success' ? '成功' : record.status === 'pending' ? '处理中' : '失败'}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-records">暂无充值记录</div>
            )}
          </div>
        )}

        {/* 充值说明 */}
        <div className="recharge-info">
          <h4>充值说明</h4>
          <ul>
            <li>💳 支持支付宝、微信支付、银行卡多种支付方式</li>
            <li>⚡ 充值成功后立即到账，无需等待</li>
            <li>🔒 采用银行级安全加密，保障资金安全</li>
            <li>📞 如有问题请联系客服：400-123-4567</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default RechargeModal;
