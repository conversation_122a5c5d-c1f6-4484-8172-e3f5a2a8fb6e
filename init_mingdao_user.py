"""
初始化明道云测试用户
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.mingdao_full_service import mingdao_full_service

async def create_test_user():
    """创建测试用户"""
    print("🚀 正在创建测试用户...")
    
    try:
        result = await mingdao_full_service.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            full_name="测试用户"
        )
        
        if result["success"]:
            print(f"✅ 测试用户创建成功!")
            print(f"   用户ID: {result['user_id']}")
            print(f"   用户名: testuser")
            print(f"   密码: testpass123")
            print(f"   邮箱: <EMAIL>")
            print(f"   初始配额: 1000 字符")
            
            # 验证用户创建
            auth_result = await mingdao_full_service.authenticate_user("testuser", "testpass123")
            if auth_result["success"]:
                user = auth_result["user"]
                print(f"\n✅ 用户验证成功:")
                print(f"   用户类型: {user['user_type']}")
                print(f"   账户状态: {user['status']}")
                print(f"   余额: {user['balance']} 元")
                print(f"   总配额: {user['total_quota']} 字符")
                print(f"   已使用: {user['used_quota']} 字符")
                print(f"   月度配额: {user['monthly_quota']} 字符")
                print(f"   创建时间: {user['created_at']}")
            else:
                print(f"❌ 用户验证失败: {auth_result['message']}")
                
        else:
            print(f"❌ 创建用户失败: {result['message']}")
            
    except Exception as e:
        print(f"❌ 创建用户异常: {e}")
        if "用户名已存在" in str(e) or "username" in str(e).lower():
            print("💡 用户可能已存在，尝试验证现有用户...")
            try:
                auth_result = await mingdao_full_service.authenticate_user("testuser", "testpass123")
                if auth_result["success"]:
                    user = auth_result["user"]
                    print(f"✅ 现有用户验证成功:")
                    print(f"   用户ID: {user['id']}")
                    print(f"   用户名: {user['username']}")
                    print(f"   邮箱: {user['email']}")
                    print(f"   余额: {user['balance']} 元")
                    print(f"   总配额: {user['total_quota']} 字符")
                    print(f"   已使用: {user['used_quota']} 字符")
                else:
                    print(f"❌ 现有用户验证失败: {auth_result['message']}")
            except Exception as verify_e:
                print(f"❌ 验证现有用户异常: {verify_e}")

async def main():
    """主函数"""
    print("🔧 明道云测试用户初始化工具")
    print("=" * 50)
    
    await create_test_user()
    
    print("\n" + "=" * 50)
    print("🎉 初始化完成!")
    print("\n📋 测试账号信息:")
    print("   用户名: testuser")
    print("   密码: testpass123")
    print("   邮箱: <EMAIL>")
    print("\n💡 您现在可以使用这个账号测试翻译系统了!")

if __name__ == "__main__":
    asyncio.run(main())
