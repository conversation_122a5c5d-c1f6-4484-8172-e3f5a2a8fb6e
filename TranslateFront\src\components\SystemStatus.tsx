import React from 'react';
import { CheckCircle, AlertTriangle, Clock, Upload, Download, Eye } from 'lucide-react';

const SystemStatus: React.FC = () => {
  const features = [
    {
      name: '文档上传',
      status: 'working',
      description: '支持拖拽上传，文件保存到 YWJ 字段',
      icon: Upload
    },
    {
      name: '文档列表',
      status: 'working',
      description: '显示原文件和翻译文件，支持搜索',
      icon: CheckCircle
    },
    {
      name: '文档下载',
      status: 'working',
      description: '可以下载原文件和翻译文件',
      icon: Download
    },
    {
      name: 'API 连接',
      status: 'working',
      description: '获取文档列表和上传文件正常',
      icon: CheckCircle
    },
    {
      name: '翻译预览',
      status: 'pending',
      description: '等待确认新的预览方案',
      icon: Eye
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'working':
        return <CheckCircle size={16} className="status-working" />;
      case 'pending':
        return <Clock size={16} className="status-pending" />;
      case 'warning':
        return <AlertTriangle size={16} className="status-warning" />;
      default:
        return <AlertTriangle size={16} className="status-warning" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'working':
        return '正常';
      case 'pending':
        return '待定';
      case 'warning':
        return '警告';
      default:
        return '未知';
    }
  };

  return (
    <div className="system-status">
      <div className="status-header">
        <h3>系统状态</h3>
        <p>当前功能状态概览</p>
      </div>

      <div className="status-grid">
        {features.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <div key={index} className={`status-card ${feature.status}`}>
              <div className="status-card-header">
                <div className="feature-icon">
                  <IconComponent size={20} />
                </div>
                <div className="feature-info">
                  <h4>{feature.name}</h4>
                  <div className="status-badge">
                    {getStatusIcon(feature.status)}
                    <span>{getStatusText(feature.status)}</span>
                  </div>
                </div>
              </div>
              <p className="feature-description">{feature.description}</p>
            </div>
          );
        })}
      </div>

      <div className="status-summary">
        <div className="summary-item">
          <CheckCircle size={16} className="status-working" />
          <span>4 个功能正常工作</span>
        </div>
        <div className="summary-item">
          <Clock size={16} className="status-pending" />
          <span>1 个功能等待确认</span>
        </div>
      </div>
    </div>
  );
};

export default SystemStatus;
