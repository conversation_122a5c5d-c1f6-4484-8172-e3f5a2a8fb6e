"""
Azure 翻译服务
"""
import json
import uuid
from typing import List, Dict, Optional, Tuple
import httpx
from fastapi import HTTPException, status

from app.config import settings
from app.utils.logger import logger


class AzureTranslatorService:
    """Azure 翻译服务类"""
    
    def __init__(self):
        self.endpoint = settings.azure_translator_endpoint
        self.key = settings.azure_translator_key
        self.region = settings.azure_translator_region
        # 使用与 VBA 宏相同的 API 端点格式
        self.base_url = self.endpoint
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头（模拟VBA中的请求头设置）"""
        return {
            'Ocp-Apim-Subscription-Key': self.key,
            'Ocp-Apim-Subscription-Region': self.region,
            'Content-type': 'application/json',
            'X-ClientTraceId': str(uuid.uuid4())
        }

    def _escape_json_text(self, text: str) -> str:
        """转义JSON文本（模拟VBA EscapeJSON函数）"""
        text = text.replace('\\', '\\\\')
        text = text.replace('"', '\\"')
        text = text.replace('\r', '\\r')
        text = text.replace('\n', '\\n')
        return text
    
    async def get_supported_languages(self) -> Dict[str, any]:
        """获取支持的语言列表"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/languages",
                    params={'api-version': '3.0', 'scope': 'translation'}
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Error getting supported languages: {e}")
            # 返回默认支持的语言列表而不是抛出异常
            return {
                "translation": {
                    "zh": {"name": "Chinese", "nativeName": "中文"},
                    "en": {"name": "English", "nativeName": "English"},
                    "ja": {"name": "Japanese", "nativeName": "日本語"},
                    "ko": {"name": "Korean", "nativeName": "한국어"},
                    "fr": {"name": "French", "nativeName": "Français"},
                    "de": {"name": "German", "nativeName": "Deutsch"},
                    "es": {"name": "Spanish", "nativeName": "Español"}
                }
            }
    
    async def detect_language(self, text: str) -> Dict[str, any]:
        """检测文本语言"""
        try:
            body = [{'text': text[:1000]}]  # 只取前1000个字符进行检测
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/detect",
                    headers=self._get_headers(),
                    json=body,
                    params={'api-version': '3.0'}
                )
                response.raise_for_status()
                result = response.json()
                
                if result and len(result) > 0:
                    return result[0]
                else:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Could not detect language"
                    )
        except Exception as e:
            logger.error(f"Error detecting language: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to detect language"
            )
    
    async def translate_text(
        self,
        texts: List[str],
        target_language: str,
        source_language: Optional[str] = None
    ) -> List[Dict[str, any]]:
        """翻译文本（使用与VBA宏完全相同的API调用方式）"""
        try:
            # 使用与VBA宏完全相同的URL格式
            url = f"{self.base_url}/translate"

            # 准备参数（与VBA中的URL参数一致）
            params = {
                'api-version': '3.0',
                'to': target_language
            }

            if source_language:
                params['from'] = source_language

            # 准备请求体（与VBA中的JSON构建方式一致）
            body = []
            for text in texts:
                escaped_text = self._escape_json_text(text)
                body.append({'Text': escaped_text})

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    url,
                    headers=self._get_headers(),
                    json=body,
                    params=params
                )
                response.raise_for_status()

                # 解析响应（模拟VBA中的响应解析）
                result = response.json()

                # 验证响应格式并提取翻译文本
                processed_results = []
                for item in result:
                    if 'translations' in item and len(item['translations']) > 0:
                        translation_text = item['translations'][0].get('text', '[Translation failed]')
                        processed_results.append({
                            'translations': [{'text': translation_text}]
                        })
                    else:
                        processed_results.append({
                            'translations': [{'text': '[Translation failed]'}]
                        })

                return processed_results

        except httpx.HTTPStatusError as e:
            logger.error(f"Azure Translator API error: {e.response.status_code} - {e.response.text}")
            # 返回错误信息而不是抛出异常（模拟VBA错误处理）
            return [{'translations': [{'text': f'[Error: HTTP {e.response.status_code}]'}]} for _ in texts]
        except Exception as e:
            logger.error(f"Error translating text: {e}")
            # 返回错误信息而不是抛出异常（模拟VBA错误处理）
            return [{'translations': [{'text': f'[Error: {str(e)}]'}]} for _ in texts]
    
    async def translate_batch(
        self,
        texts: List[str],
        target_language: str,
        source_language: Optional[str] = None,
        batch_size: int = 100
    ) -> List[Dict[str, any]]:
        """批量翻译文本"""
        results = []
        
        # 分批处理
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_results = await self.translate_text(batch, target_language, source_language)
            results.extend(batch_results)
        
        return results
    
    def calculate_cost(self, character_count: int) -> float:
        """计算翻译成本 (基于字符数)"""
        # Azure 翻译服务的定价 (示例价格，实际价格请参考官方文档)
        # 标准版: $10 per 1,000,000 characters
        cost_per_million_chars = 10.0
        return (character_count / 1_000_000) * cost_per_million_chars
    
    def estimate_translation_time(self, character_count: int) -> int:
        """估算翻译时间 (秒)"""
        # 基于经验值：每秒大约可以翻译1000个字符
        chars_per_second = 1000
        return max(1, character_count // chars_per_second)
    
    async def validate_language_pair(self, source_lang: str, target_lang: str) -> bool:
        """验证语言对是否支持"""
        # 简化验证，使用预定义的支持语言列表
        supported_languages = ['zh', 'en', 'ja', 'ko', 'fr', 'de', 'es', 'ru', 'it', 'pt']

        # 检查目标语言是否支持
        if target_lang not in supported_languages:
            return False

        # 检查源语言是否支持 (如果指定了源语言)
        if source_lang and source_lang not in supported_languages:
            return False

        return True


# 创建全局翻译服务实例
azure_translator = AzureTranslatorService()
