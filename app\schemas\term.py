"""
术语管理相关的 Pydantic 模型
"""
from typing import Optional, List
from pydantic import BaseModel, Field


class TermCategoryCreate(BaseModel):
    """术语分类创建模型"""
    name: str = Field(..., max_length=100, description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")


class TermCategoryResponse(BaseModel):
    """术语分类响应模型"""
    id: int
    name: str
    description: Optional[str]
    created_at: str
    updated_at: Optional[str]
    
    class Config:
        from_attributes = True


class TermCreate(BaseModel):
    """术语创建模型"""
    category_id: Optional[int] = Field(None, description="分类ID")
    source_term: str = Field(..., max_length=200, description="源术语")
    target_term: str = Field(..., max_length=200, description="目标术语")
    source_language: str = Field(..., max_length=10, description="源语言代码")
    target_language: str = Field(..., max_length=10, description="目标语言代码")
    definition: Optional[str] = Field(None, description="术语定义")
    context: Optional[str] = Field(None, description="使用上下文")
    notes: Optional[str] = Field(None, description="备注")
    confidence_level: int = Field(default=5, ge=1, le=10, description="置信度等级 (1-10)")


class TermUpdate(BaseModel):
    """术语更新模型"""
    category_id: Optional[int] = None
    target_term: Optional[str] = Field(None, max_length=200)
    definition: Optional[str] = None
    context: Optional[str] = None
    notes: Optional[str] = None
    confidence_level: Optional[int] = Field(None, ge=1, le=10)
    is_active: Optional[bool] = None


class TermResponse(BaseModel):
    """术语响应模型"""
    id: int
    category_id: Optional[int]
    category_name: Optional[str]
    source_term: str
    target_term: str
    source_language: str
    target_language: str
    definition: Optional[str]
    context: Optional[str]
    notes: Optional[str]
    is_active: bool
    confidence_level: int
    usage_count: int
    created_by: Optional[str]
    approved_by: Optional[str]
    created_at: str
    updated_at: Optional[str]
    
    class Config:
        from_attributes = True


class TermListResponse(BaseModel):
    """术语列表响应模型"""
    terms: List[TermResponse]
    total: int
    page: int
    page_size: int


class TermSearchRequest(BaseModel):
    """术语搜索请求模型"""
    query: Optional[str] = Field(None, description="搜索关键词")
    source_language: Optional[str] = Field(None, description="源语言过滤")
    target_language: Optional[str] = Field(None, description="目标语言过滤")
    category_id: Optional[int] = Field(None, description="分类过滤")
    is_active: Optional[bool] = Field(None, description="状态过滤")


class TermImportRequest(BaseModel):
    """术语导入请求模型"""
    source_language: str = Field(..., max_length=10, description="源语言代码")
    target_language: str = Field(..., max_length=10, description="目标语言代码")
    category_id: Optional[int] = Field(None, description="默认分类ID")
    overwrite_existing: bool = Field(default=False, description="是否覆盖已存在的术语")


class TermImportResponse(BaseModel):
    """术语导入响应模型"""
    import_id: int
    total_terms: int
    imported_terms: int
    failed_terms: int
    status: str
    error_message: Optional[str]


class TermValidationResult(BaseModel):
    """术语验证结果模型"""
    term: str
    is_valid: bool
    suggested_translation: Optional[str]
    confidence: float
    source: str  # "database" 或 "api"


class TermCorrectionRequest(BaseModel):
    """术语校正请求模型"""
    text: str = Field(..., description="要校正的文本")
    source_language: str = Field(..., max_length=10)
    target_language: str = Field(..., max_length=10)


class TermCorrectionResponse(BaseModel):
    """术语校正响应模型"""
    original_text: str
    corrected_text: str
    corrections: List[dict]  # 校正详情
    confidence_score: float


class TermStatistics(BaseModel):
    """术语统计模型"""
    total_terms: int
    active_terms: int
    inactive_terms: int
    categories_count: int
    language_pairs_count: int
    most_used_terms: List[TermResponse]
    recent_terms: List[TermResponse]
