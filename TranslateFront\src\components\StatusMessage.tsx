import React from 'react';
import { CheckCircle, AlertCircle, Info, X } from 'lucide-react';

export type MessageType = 'success' | 'error' | 'info' | 'warning';

interface StatusMessageProps {
  type: MessageType;
  message: string;
  onClose?: () => void;
  autoClose?: boolean;
  duration?: number;
}

const StatusMessage: React.FC<StatusMessageProps> = ({
  type,
  message,
  onClose,
  autoClose = true,
  duration = 5000
}) => {
  React.useEffect(() => {
    if (autoClose && onClose) {
      const timer = setTimeout(onClose, duration);
      return () => clearTimeout(timer);
    }
  }, [autoClose, duration, onClose]);

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle size={20} />;
      case 'error':
        return <AlertCircle size={20} />;
      case 'warning':
        return <AlertCircle size={20} />;
      case 'info':
      default:
        return <Info size={20} />;
    }
  };

  const getClassName = () => {
    const baseClass = 'status-message';
    return `${baseClass} ${baseClass}--${type}`;
  };

  return (
    <div className={getClassName()}>
      <div className="status-message__content">
        <div className="status-message__icon">
          {getIcon()}
        </div>
        <div className="status-message__text">
          {message}
        </div>
      </div>
      {onClose && (
        <button
          className="status-message__close"
          onClick={onClose}
          aria-label="关闭消息"
        >
          <X size={16} />
        </button>
      )}
    </div>
  );
};

export default StatusMessage;
