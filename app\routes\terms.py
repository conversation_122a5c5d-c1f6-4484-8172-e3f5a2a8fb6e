"""
术语管理相关路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_db
from app.schemas.term import (
    TermCreate, TermUpdate, TermResponse, TermListResponse,
    TermCategoryCreate, TermCategoryResponse, TermSearchRequest,
    TermImportRequest, TermImportResponse, TermStatistics
)
from app.services.term_service import TermService
from app.utils.dependencies import get_current_active_user, get_moderator_user
from app.utils.logger import logger


router = APIRouter()


# 术语分类管理
@router.post("/categories", response_model=TermCategoryResponse, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_data: TermCategoryCreate,
    current_user = Depends(get_moderator_user),
    db: AsyncSession = Depends(get_async_db)
):
    """创建术语分类 (需要版主权限)"""
    try:
        category = await TermService.create_category(db, category_data, current_user)
        return TermCategoryResponse(
            id=category.id,
            name=category.name,
            description=category.description,
            created_at=category.created_at.isoformat() if category.created_at else None,
            updated_at=category.updated_at.isoformat() if category.updated_at else None
        )
    except Exception as e:
        logger.error(f"Create category error: {e}")
        raise


@router.get("/categories", response_model=List[TermCategoryResponse])
async def get_categories(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取所有术语分类"""
    try:
        categories = await TermService.get_categories(db)
        return [
            TermCategoryResponse(
                id=category.id,
                name=category.name,
                description=category.description,
                created_at=category.created_at.isoformat() if category.created_at else None,
                updated_at=category.updated_at.isoformat() if category.updated_at else None
            )
            for category in categories
        ]
    except Exception as e:
        logger.error(f"Get categories error: {e}")
        raise


# 术语管理
@router.post("/", response_model=TermResponse, status_code=status.HTTP_201_CREATED)
async def create_term(
    term_data: TermCreate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """创建术语"""
    try:
        term = await TermService.create_term(db, term_data, current_user)
        
        # 获取分类名称
        category_name = None
        if term.category_id:
            categories = await TermService.get_categories(db)
            category = next((c for c in categories if c.id == term.category_id), None)
            category_name = category.name if category else None
        
        return TermResponse(
            id=term.id,
            category_id=term.category_id,
            category_name=category_name,
            source_term=term.source_term,
            target_term=term.target_term,
            source_language=term.source_language,
            target_language=term.target_language,
            definition=term.definition,
            context=term.context,
            notes=term.notes,
            is_active=term.is_active,
            confidence_level=term.confidence_level,
            usage_count=term.usage_count,
            created_by=term.created_by,
            approved_by=term.approved_by,
            created_at=term.created_at.isoformat() if term.created_at else None,
            updated_at=term.updated_at.isoformat() if term.updated_at else None
        )
    except Exception as e:
        logger.error(f"Create term error: {e}")
        raise


@router.get("/search", response_model=TermListResponse)
async def search_terms(
    query: Optional[str] = Query(None, description="搜索关键词"),
    source_language: Optional[str] = Query(None, description="源语言过滤"),
    target_language: Optional[str] = Query(None, description="目标语言过滤"),
    category_id: Optional[int] = Query(None, description="分类过滤"),
    is_active: Optional[bool] = Query(None, description="状态过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """搜索术语"""
    try:
        search_request = TermSearchRequest(
            query=query,
            source_language=source_language,
            target_language=target_language,
            category_id=category_id,
            is_active=is_active
        )
        
        skip = (page - 1) * page_size
        terms, total = await TermService.search_terms(db, search_request, skip, page_size)
        
        # 获取所有分类用于显示分类名称
        categories = await TermService.get_categories(db)
        category_map = {c.id: c.name for c in categories}
        
        term_list = [
            TermResponse(
                id=term.id,
                category_id=term.category_id,
                category_name=category_map.get(term.category_id),
                source_term=term.source_term,
                target_term=term.target_term,
                source_language=term.source_language,
                target_language=term.target_language,
                definition=term.definition,
                context=term.context,
                notes=term.notes,
                is_active=term.is_active,
                confidence_level=term.confidence_level,
                usage_count=term.usage_count,
                created_by=term.created_by,
                approved_by=term.approved_by,
                created_at=term.created_at.isoformat() if term.created_at else None,
                updated_at=term.updated_at.isoformat() if term.updated_at else None
            )
            for term in terms
        ]
        
        return TermListResponse(
            terms=term_list,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        logger.error(f"Search terms error: {e}")
        raise


@router.get("/{term_id}", response_model=TermResponse)
async def get_term(
    term_id: int,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取术语详情"""
    try:
        term = await TermService.get_term_by_id(db, term_id)
        
        if not term:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Term not found"
            )
        
        # 获取分类名称
        category_name = None
        if term.category_id:
            categories = await TermService.get_categories(db)
            category = next((c for c in categories if c.id == term.category_id), None)
            category_name = category.name if category else None
        
        return TermResponse(
            id=term.id,
            category_id=term.category_id,
            category_name=category_name,
            source_term=term.source_term,
            target_term=term.target_term,
            source_language=term.source_language,
            target_language=term.target_language,
            definition=term.definition,
            context=term.context,
            notes=term.notes,
            is_active=term.is_active,
            confidence_level=term.confidence_level,
            usage_count=term.usage_count,
            created_by=term.created_by,
            approved_by=term.approved_by,
            created_at=term.created_at.isoformat() if term.created_at else None,
            updated_at=term.updated_at.isoformat() if term.updated_at else None
        )
    except Exception as e:
        logger.error(f"Get term error: {e}")
        raise


@router.put("/{term_id}", response_model=TermResponse)
async def update_term(
    term_id: int,
    term_update: TermUpdate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """更新术语"""
    try:
        term = await TermService.update_term(db, term_id, term_update, current_user)
        
        # 获取分类名称
        category_name = None
        if term.category_id:
            categories = await TermService.get_categories(db)
            category = next((c for c in categories if c.id == term.category_id), None)
            category_name = category.name if category else None
        
        return TermResponse(
            id=term.id,
            category_id=term.category_id,
            category_name=category_name,
            source_term=term.source_term,
            target_term=term.target_term,
            source_language=term.source_language,
            target_language=term.target_language,
            definition=term.definition,
            context=term.context,
            notes=term.notes,
            is_active=term.is_active,
            confidence_level=term.confidence_level,
            usage_count=term.usage_count,
            created_by=term.created_by,
            approved_by=term.approved_by,
            created_at=term.created_at.isoformat() if term.created_at else None,
            updated_at=term.updated_at.isoformat() if term.updated_at else None
        )
    except Exception as e:
        logger.error(f"Update term error: {e}")
        raise


@router.delete("/{term_id}")
async def delete_term(
    term_id: int,
    current_user = Depends(get_moderator_user),
    db: AsyncSession = Depends(get_async_db)
):
    """删除术语 (需要版主权限)"""
    try:
        success = await TermService.delete_term(db, term_id, current_user)
        
        if success:
            return {"message": "Term deleted successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete term"
            )
    except Exception as e:
        logger.error(f"Delete term error: {e}")
        raise


@router.post("/import", response_model=TermImportResponse)
async def import_terms(
    file: UploadFile = File(..., description="CSV格式的术语文件"),
    source_language: str = Query(..., description="源语言代码"),
    target_language: str = Query(..., description="目标语言代码"),
    category_id: Optional[int] = Query(None, description="默认分类ID"),
    overwrite_existing: bool = Query(False, description="是否覆盖已存在的术语"),
    current_user = Depends(get_moderator_user),
    db: AsyncSession = Depends(get_async_db)
):
    """
    导入术语 (需要版主权限)
    
    CSV文件格式要求：
    - 必须包含列：source_term, target_term
    - 可选列：definition, context, notes
    """
    try:
        # 验证文件格式
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only CSV files are supported"
            )
        
        import_request = TermImportRequest(
            source_language=source_language,
            target_language=target_language,
            category_id=category_id,
            overwrite_existing=overwrite_existing
        )
        
        import_history = await TermService.import_terms_from_csv(
            db, file, import_request, current_user
        )
        
        return TermImportResponse(
            import_id=import_history.id,
            total_terms=import_history.total_terms,
            imported_terms=import_history.imported_terms,
            failed_terms=import_history.failed_terms,
            status=import_history.status,
            error_message=import_history.error_message
        )
    except Exception as e:
        logger.error(f"Import terms error: {e}")
        raise
