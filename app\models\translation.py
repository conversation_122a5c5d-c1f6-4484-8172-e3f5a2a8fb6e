"""
翻译相关模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean, Float, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.db.database import Base


class TranslationStatus(str, enum.Enum):
    """翻译状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TranslationHistory(Base):
    """翻译历史记录模型"""
    __tablename__ = "translation_histories"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    file_id = Column(Integer, ForeignKey("uploaded_files.id"), nullable=False)
    
    # 翻译配置
    source_language = Column(String(10), nullable=False)
    target_language = Column(String(10), nullable=False)
    
    # 翻译状态和结果
    status = Column(Enum(TranslationStatus), default=TranslationStatus.PENDING)
    progress = Column(Float, default=0.0)  # 翻译进度 (0.0 - 1.0)
    
    # 翻译内容
    original_text = Column(Text, nullable=True)
    translated_text = Column(Text, nullable=True)
    
    # 翻译统计
    total_characters = Column(Integer, default=0)
    translated_characters = Column(Integer, default=0)
    
    # 质量和成本信息
    confidence_score = Column(Float, nullable=True)  # 翻译置信度
    cost = Column(Float, default=0.0)  # 翻译成本
    
    # 错误信息
    error_message = Column(Text, nullable=True)
    
    # 术语校对
    terms_checked = Column(Boolean, default=False)
    term_corrections = Column(Text, nullable=True)  # JSON 格式的术语校正信息

    # 明道云集成
    mingdao_row_id = Column(String(100), nullable=True, comment="明道云记录ID")

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="translation_histories")
    file = relationship("UploadedFile", back_populates="translation_histories")
    
    def __repr__(self):
        return f"<TranslationHistory(id={self.id}, status='{self.status}', progress={self.progress})>"
    
    @property
    def is_completed(self) -> bool:
        """检查翻译是否完成"""
        return self.status == TranslationStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """检查翻译是否失败"""
        return self.status == TranslationStatus.FAILED
