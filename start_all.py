"""
启动前后端服务的脚本
"""
import subprocess
import time
import sys
import os
from pathlib import Path

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端 FastAPI 服务...")
    backend_process = subprocess.Popen([
        sys.executable, "start.py"
    ], cwd=Path(__file__).parent)
    return backend_process

def start_frontend():
    """启动前端服务"""
    print("🌐 启动前端服务...")
    frontend_process = subprocess.Popen([
        sys.executable, "server.py"
    ], cwd=Path(__file__).parent / "frontend")
    return frontend_process

def main():
    print("🎯 启动翻译服务系统")
    print("=" * 50)
    
    try:
        # 启动后端
        backend_process = start_backend()
        print("✅ 后端服务启动中...")
        
        # 等待后端启动
        time.sleep(3)
        
        # 启动前端
        frontend_process = start_frontend()
        print("✅ 前端服务启动中...")
        
        print("\n" + "=" * 50)
        print("🎉 服务启动完成！")
        print("📖 后端 API 文档: http://localhost:8000/docs")
        print("🌐 前端界面: http://localhost:3000")
        print("=" * 50)
        print("💡 测试账号: testuser / testpass123")
        print("📝 按 Ctrl+C 停止所有服务")
        print("=" * 50)
        
        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            
            # 停止进程
            backend_process.terminate()
            frontend_process.terminate()
            
            # 等待进程结束
            backend_process.wait()
            frontend_process.wait()
            
            print("✅ 所有服务已停止")
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
