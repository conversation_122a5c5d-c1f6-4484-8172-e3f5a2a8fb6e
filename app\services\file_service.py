"""
文件处理服务
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from fastapi import UploadFile, HTTPException, status

from app.models.file import UploadedFile, FileStatus
from app.models.user import User
from app.utils.file_utils import (
    save_uploaded_file, get_file_mime_type, extract_docx_content,
    delete_file, get_file_size
)
from app.utils.logger import logger


class FileService:
    """文件处理服务类"""
    
    @staticmethod
    async def upload_file(
        db: AsyncSession,
        file: UploadFile,
        user: User
    ) -> UploadedFile:
        """上传并处理文件"""
        try:
            # 保存文件
            filename, file_path = await save_uploaded_file(file)
            
            # 获取文件信息
            mime_type = get_file_mime_type(file.filename)
            file_size = get_file_size(file_path)
            
            # 创建文件记录
            db_file = UploadedFile(
                user_id=user.id,
                filename=filename,
                original_filename=file.filename,
                file_path=file_path,
                file_size=file_size,
                mime_type=mime_type,
                status=FileStatus.UPLOADED
            )
            
            db.add(db_file)
            await db.commit()
            await db.refresh(db_file)
            
            # 异步处理文档内容提取
            await FileService._process_document_content(db, db_file)
            
            logger.info(f"File uploaded successfully: {filename} by user {user.username}")
            return db_file
            
        except Exception as e:
            logger.error(f"File upload error: {e}")
            # 清理已保存的文件
            if 'file_path' in locals():
                delete_file(file_path)
            raise
    
    @staticmethod
    async def _process_document_content(db: AsyncSession, file_record: UploadedFile):
        """处理文档内容提取"""
        try:
            # 更新状态为处理中
            file_record.status = FileStatus.PROCESSING
            await db.commit()
            
            # 提取文档内容
            if file_record.mime_type in [
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/msword"
            ]:
                content = extract_docx_content(file_record.file_path)
                
                # 更新文件记录
                file_record.total_paragraphs = content["total_paragraphs"]
                file_record.total_characters = content["total_characters"]
                file_record.extracted_text = content["extracted_text"]
                file_record.status = FileStatus.PROCESSED
            else:
                file_record.status = FileStatus.ERROR
                file_record.error_message = "Unsupported file type for content extraction"
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"Document processing error: {e}")
            file_record.status = FileStatus.ERROR
            file_record.error_message = str(e)
            await db.commit()
    
    @staticmethod
    async def get_user_files(
        db: AsyncSession,
        user: User,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[UploadedFile], int]:
        """获取用户的文件列表"""
        # 获取总数
        count_result = await db.execute(
            select(UploadedFile).where(UploadedFile.user_id == user.id)
        )
        total = len(count_result.scalars().all())
        
        # 获取分页数据
        result = await db.execute(
            select(UploadedFile)
            .where(UploadedFile.user_id == user.id)
            .order_by(UploadedFile.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        files = result.scalars().all()
        
        return files, total
    
    @staticmethod
    async def get_file_by_id(
        db: AsyncSession,
        file_id: int,
        user: User
    ) -> Optional[UploadedFile]:
        """根据ID获取用户的文件"""
        result = await db.execute(
            select(UploadedFile).where(
                and_(
                    UploadedFile.id == file_id,
                    UploadedFile.user_id == user.id
                )
            )
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def delete_file(
        db: AsyncSession,
        file_id: int,
        user: User
    ) -> bool:
        """删除用户的文件"""
        file_record = await FileService.get_file_by_id(db, file_id, user)
        
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        try:
            # 删除物理文件
            delete_file(file_record.file_path)
            
            # 删除数据库记录
            await db.delete(file_record)
            await db.commit()
            
            logger.info(f"File deleted: {file_record.filename} by user {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"File deletion error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete file"
            )
    
    @staticmethod
    async def get_file_content(
        db: AsyncSession,
        file_id: int,
        user: User
    ) -> dict:
        """获取文件内容"""
        file_record = await FileService.get_file_by_id(db, file_id, user)
        
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        if file_record.status != FileStatus.PROCESSED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File has not been processed yet"
            )
        
        try:
            # 重新提取内容（确保最新）
            content = extract_docx_content(file_record.file_path)
            return content
            
        except Exception as e:
            logger.error(f"Error getting file content: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to extract file content"
            )
