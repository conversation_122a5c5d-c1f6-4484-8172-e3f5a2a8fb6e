# FastAPI 翻译服务

基于 FastAPI 框架的文档翻译服务，严格按照您的 VBA 宏流程实现翻译功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并修改配置：

```bash
cp .env.example .env
```

重要配置项：
- `AZURE_TRANSLATOR_KEY`: 您的 Azure 翻译服务密钥
- `AZURE_TRANSLATOR_REGION`: Azure 服务区域
- `SECRET_KEY`: JWT 密钥（生产环境请使用强密钥）

### 3. 启动服务

#### 方法一：使用启动脚本（推荐）

```bash
python start.py
```

这将启动完整的翻译服务，包括：
- 后端 API 服务（端口 8000）
- 前端 Web 界面（端口 3000）
- 自动打开浏览器

#### 方法二：分别启动后端和前端（推荐用于开发）

**第一步：启动前端服务**
```bash
# 使用专用的前端启动脚本
python start_frontend.py

# 或者手动启动
cd frontend
python -m http.server 3000
```

**第二步：启动后端服务**
```bash
# 在新的终端窗口中启动后端
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或者使用 run.py
python run.py
```

#### 方法三：仅启动后端 API

如果只需要 API 服务（不需要 Web 界面）：

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

#### ⚠️ 故障排除：前端无法访问

如果遇到 `http://localhost:3000` 拒绝访问的问题：

1. **检查前端服务是否启动**：
   ```bash
   netstat -an | findstr :3000
   ```

2. **手动启动前端服务**：
   ```bash
   python start_frontend.py
   ```

3. **检查端口占用**：
   ```bash
   # 查看端口占用
   netstat -an | findstr :3000

   # 如果端口被占用，杀死进程
   taskkill /PID <进程ID> /F
   ```

### 4. 访问服务

启动成功后，您可以访问：

- **Web 界面**: http://localhost:3000
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

### 5. 测试服务

运行测试脚本验证服务：

```bash
# 测试基本 API 功能
python test_api.py

# 测试文档下载功能
python test_docx_download.py

# 测试翻译历史功能
python test_history.py
```

## � 使用说明

### 快速开始

1. **启动服务**
   ```bash
   python start.py
   ```

2. **访问 Web 界面**
   - 打开浏览器访问：http://localhost:3000
   - 或者服务启动后会自动打开浏览器

3. **用户登录**
   - 使用测试账号：`testuser` / `testpass123`
   - 或者注册新账号

### 完整使用流程

#### 1. 用户认证
- **注册**：创建新用户账号
- **登录**：使用用户名和密码登录
- **配额管理**：每个用户有翻译字符配额限制

#### 2. 文档上传
- **支持格式**：.docx、.doc Word 文档
- **文件大小**：最大 10MB
- **自动解析**：系统自动提取文档内容和结构

#### 3. 翻译设置
- **源语言**：中文、英文、日文、韩文、法文、德文、西班牙文等
- **目标语言**：支持多种语言互译
- **实时预览**：显示预估翻译字符数和成本

#### 4. 翻译处理
- **实时进度**：显示翻译进度百分比
- **状态更新**：等待中 → 进行中 → 已完成
- **错误处理**：自动重试和错误提示

#### 5. 结果查看
- **在线查看**：直接在网页中查看翻译结果
- **双语对照**：原文和译文对照显示
- **质量评估**：显示翻译置信度分数

#### 6. 文档下载
- **📄 文本格式**：纯文本双语对照文档
- **📝 Word格式**：保持原文档格式的双语Word文档
  - 原文格式完全保留
  - 译文使用 Arial 小四号黑色格式
  - 支持页眉页脚翻译
  - 避免重复翻译

#### 7. 历史管理
- **翻译历史**：查看所有历史翻译记录
- **状态筛选**：按翻译状态筛选记录
- **批量下载**：下载任意历史翻译的文档

## �📖 API 文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🔧 主要功能

### 1. 用户认证
- ✅ 用户注册/登录
- ✅ JWT 令牌认证
- ✅ 角色权限控制
- ✅ 用户配额管理

### 2. 文件上传
- ✅ 支持 .docx/.doc 文件
- ✅ 文件内容解析
- ✅ 段落和表格提取

### 3. 翻译服务
- ✅ 严格按照 VBA 宏流程
- ✅ 段落翻译（跳过已翻译内容）
- ✅ 表格单元格翻译
- ✅ 详细翻译日志
- ✅ 错误处理和重试

### 4. 术语管理
- ✅ 专业术语库
- ✅ 术语分类管理
- ✅ CSV 批量导入
- ✅ 术语校对功能

### 5. 管理功能
- ✅ 系统统计
- ✅ 用户管理
- ✅ 日志查看
- ✅ 系统健康监控

## 🔄 翻译流程

本服务严格按照您的 VBA 宏流程实现：

1. **文档解析**: 提取段落和表格内容
2. **段落翻译**: 
   - 清理文本（去除分页符、表格符等）
   - 跳过短文本和已翻译内容（以"["开头）
   - 逐段翻译并记录日志
3. **表格翻译**:
   - 逐个单元格翻译
   - 保持表格结构
   - 详细位置日志
4. **结果组合**:
   - 支持原文译文对照
   - 支持纯译文输出
   - VBA 风格格式

## 📝 API 使用示例

### 用户注册
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "testpass123"
  }'
```

### 用户登录
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpass123"
  }'
```

### 文件上传
```bash
curl -X POST "http://localhost:8000/api/v1/upload/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@document.docx"
```

### 创建翻译任务
```bash
curl -X POST "http://localhost:8000/api/v1/translate/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "file_id": 1,
    "source_language": "zh",
    "target_language": "en",
    "include_original": true
  }'
```

## 🐳 Docker 部署

```bash
# 构建镜像
docker build -t translation-service .

# 运行容器
docker run -p 8000:8000 -v $(pwd)/.env:/app/.env translation-service
```

## 🔍 故障排除

### 服务启动问题

#### 1. 端口被占用
```bash
# Windows 检查端口占用
netstat -an | findstr :8000
netstat -an | findstr :3000

# 杀死占用端口的进程
taskkill /PID <进程ID> /F

# Linux/Mac 检查端口占用
lsof -i :8000
lsof -i :3000

# 杀死占用端口的进程
kill -9 <进程ID>
```

#### 2. 依赖安装失败
```bash
# 升级 pip
python -m pip install --upgrade pip

# 清理缓存重新安装
pip cache purge
pip install -r requirements.txt --no-cache-dir

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 3. 虚拟环境问题
```bash
# 重新创建虚拟环境
rmdir /s .venv  # Windows
rm -rf .venv    # Linux/Mac

python -m venv .venv
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/Mac

pip install -r requirements.txt
```

### 常见运行问题

#### 1. Azure 翻译服务错误
- 检查 `.env` 文件中的 `AZURE_TRANSLATOR_KEY` 是否正确
- 确认 Azure 翻译服务配额是否充足
- 检查网络连接是否正常
- 验证 Azure 服务区域设置

#### 2. 文件上传失败
- 确认文件格式为 .docx 或 .doc
- 检查文件大小是否超过 10MB
- 确认文件没有密码保护
- 验证 uploads 目录权限

#### 3. 翻译进度卡住
- 刷新页面重新查看进度
- 检查服务器日志是否有错误信息
- 重启服务后重新尝试
- 检查用户翻译配额是否充足

#### 4. Word 文档下载问题
- 确认翻译状态为"已完成"
- 检查浏览器是否阻止了文件下载
- 尝试使用不同的浏览器
- 清除浏览器缓存

#### 5. 数据库相关问题
- 检查数据库文件 `test.db` 是否存在
- 确认数据库权限设置
- 重启服务自动创建数据库表

### 日志查看

服务运行时会在控制台显示详细日志，包括：
- 用户登录/注册信息
- 文件上传和解析状态
- 翻译进度和结果
- 错误信息和堆栈跟踪

### 数据库管理

#### 查看数据库内容
```bash
python -c "
import sqlite3
conn = sqlite3.connect('test.db')
cursor = conn.cursor()

# 查看用户列表
cursor.execute('SELECT id, username, translation_quota, used_quota FROM users')
print('用户列表:')
for row in cursor.fetchall():
    print(f'  ID: {row[0]}, 用户: {row[1]}, 配额: {row[2]}, 已用: {row[3]}')

# 查看翻译历史
cursor.execute('SELECT id, status, created_at FROM translation_histories ORDER BY id DESC LIMIT 5')
print('\n最近翻译:')
for row in cursor.fetchall():
    print(f'  ID: {row[0]}, 状态: {row[1]}, 时间: {row[2]}')

conn.close()
"
```

#### 重置用户配额
```bash
# 重置所有用户的已使用配额
python -c "
import sqlite3
conn = sqlite3.connect('test.db')
cursor = conn.cursor()
cursor.execute('UPDATE users SET used_quota = 0')
conn.commit()
print('✅ 所有用户配额已重置')
conn.close()
"

# 为测试用户设置无限配额
python init_test_user.py

# 为特定用户设置配额
python -c "
import sqlite3
conn = sqlite3.connect('test.db')
cursor = conn.cursor()
cursor.execute('UPDATE users SET translation_quota = 100000 WHERE username = \"specific_user\"')
conn.commit()
print('✅ 用户配额已更新')
conn.close()
"
```

#### 清理翻译历史
```bash
python -c "
import sqlite3
conn = sqlite3.connect('test.db')
cursor = conn.cursor()
cursor.execute('DELETE FROM translation_histories WHERE status != \"COMPLETED\"')
deleted = cursor.rowcount
conn.commit()
print(f'✅ 已清理 {deleted} 条未完成的翻译记录')
conn.close()
"
```

### 配置文件说明

#### .env 文件配置
```env
# Azure 翻译服务（必需）
AZURE_TRANSLATOR_KEY=your_azure_translator_key
AZURE_TRANSLATOR_REGION=eastasia

# 数据库配置
DATABASE_URL=sqlite:///./test.db

# JWT 安全密钥
SECRET_KEY=your_secret_key_here

# 服务配置
DEBUG=True
LOG_LEVEL=INFO

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads
```

#### 测试账号
系统会自动创建测试账号：
- **用户名**: `testuser`
- **密码**: `testpass123`
- **配额**: 无限制（10亿字符）

**注意**: 测试账号专门用于开发和测试，拥有无限翻译配额。如需重置测试账号配额，可运行：
```bash
python init_test_user.py
```

## 🛡️ 安全注意事项

- 生产环境请修改默认密码
- 使用强 JWT 密钥
- 配置 HTTPS
- 限制文件上传大小
- 定期备份数据

## 📞 技术支持

如有问题，请检查：
1. 日志文件中的错误信息
2. API 响应中的错误详情
3. 网络连接和服务配置

## 🎯 项目特色

### ✅ 已完成功能

- **🌐 Web 前端界面**：现代化的响应式 Web 界面
- **📝 Word 文档支持**：完美支持 .docx/.doc 格式
- **🎨 格式保持**：原文格式完全保留，译文统一格式
- **📚 翻译历史**：完整的翻译记录管理
- **📥 双格式下载**：文本和 Word 格式下载
- **👤 用户管理**：注册、登录、配额管理
- **🔄 实时进度**：翻译进度实时显示
- **🌍 多语言支持**：支持中英日韩法德西等多种语言
- **📊 质量评估**：翻译置信度评分
- **🔧 页眉页脚翻译**：完整的文档结构翻译

### 🚀 下一步计划

- [ ] 添加 PDF 文档格式支持
- [ ] 实现批量文档翻译
- [ ] 添加翻译记忆库功能
- [ ] 集成更多翻译服务商（Google、百度等）
- [ ] 添加翻译质量人工评估
- [ ] 实现协作翻译功能
- [ ] 添加 API 访问限流
- [ ] 支持自定义翻译模板
