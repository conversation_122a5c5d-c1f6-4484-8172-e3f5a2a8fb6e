/* 批量下载模态框样式 */
.batch-download-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.batch-download-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
}

.batch-download-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.batch-download-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #e9ecef;
  color: #495057;
}

.batch-download-content {
  padding: 24px;
}

.download-info {
  margin-bottom: 24px;
  text-align: center;
}

.download-info p {
  margin: 8px 0;
  color: #495057;
}

.download-info strong {
  color: #007bff;
  font-weight: 600;
}

.download-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.download-option {
  display: block;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
}

.download-option:hover {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.download-option.selected {
  border-color: #007bff;
  background-color: #e7f3ff;
}

.download-option input[type="radio"] {
  display: none;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.option-icon {
  color: #007bff;
  flex-shrink: 0;
}

.option-text h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.option-text p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.batch-download-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .batch-download-container {
    width: 95%;
    margin: 20px;
  }
  
  .batch-download-header,
  .batch-download-content,
  .batch-download-footer {
    padding: 16px;
  }
  
  .option-content {
    gap: 12px;
  }
  
  .option-text h4 {
    font-size: 15px;
  }
  
  .option-text p {
    font-size: 13px;
  }
}
