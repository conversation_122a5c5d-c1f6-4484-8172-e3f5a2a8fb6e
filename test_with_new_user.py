#!/usr/bin/env python3
"""
测试新用户的翻译设置功能
"""

import requests
import json

def test_with_new_user():
    """测试新用户的翻译设置"""
    
    print("🧪 测试新用户翻译设置功能")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 使用一个新的测试用户rowid
    # 请将此ID替换为您的新测试账号的实际rowid
    new_user_rowid = "test-user-new-123456"  # 请替换为实际的新用户rowid
    
    print(f"🆔 测试用户rowid: {new_user_rowid}")
    print("⚠️  请确保这是一个新的、不存在的用户rowid")
    
    test_settings = {
        "paragraph": {
            "font_family": "新宋体",
            "font_size": 13,
            "bold": True,
            "italic": False,
            "underline": False,
            "text_align": "left",
            "color": "#FF0000"
        },
        "table": {
            "font_family": "楷体",
            "font_size": 9,
            "bold": False,
            "italic": True,
            "underline": False,
            "text_align": "right",
            "color": "#00FF00"
        },
        "header": {
            "font_family": "隶书",
            "font_size": 16,
            "bold": True,
            "italic": True,
            "underline": True,
            "text_align": "center",
            "color": "#0000FF"
        },
        "enable_paragraph": True,
        "enable_table": False,
        "enable_header": True
    }
    
    # 1. 测试获取新用户设置（应该返回默认设置）
    print("\n1️⃣ 测试获取新用户设置（应该返回默认设置）")
    try:
        response = requests.get(f"{base_url}/api/translation-settings/{new_user_rowid}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取成功（应该是默认设置）:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 检查是否是默认设置
            data = result.get("data", {})
            if data.get("paragraph", {}).get("font_family") == "Arial":
                print("✅ 确认返回的是默认设置")
            else:
                print("⚠️ 返回的不是默认设置")
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取测试失败: {e}")
    
    # 2. 测试保存新用户设置
    print("\n2️⃣ 测试保存新用户翻译设置")
    try:
        response = requests.post(
            f"{base_url}/api/translation-settings/{new_user_rowid}",
            json=test_settings,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 保存成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 保存失败:")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 保存测试失败: {e}")
        return
    
    # 3. 再次获取用户设置（应该返回刚才保存的设置）
    print("\n3️⃣ 验证保存的设置")
    try:
        response = requests.get(f"{base_url}/api/translation-settings/{new_user_rowid}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 获取成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 验证设置是否正确
            data = result.get("data", {})
            if (data.get("paragraph", {}).get("font_family") == "新宋体" and
                data.get("paragraph", {}).get("font_size") == 13 and
                data.get("table", {}).get("font_family") == "楷体"):
                print("🎉 设置验证成功！数据已正确保存和读取")
            else:
                print("⚠️ 设置验证失败，数据不匹配")
                print(f"期望段落字体: 新宋体，实际: {data.get('paragraph', {}).get('font_family')}")
                print(f"期望段落大小: 13，实际: {data.get('paragraph', {}).get('font_size')}")
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
    
    # 4. 测试更新设置
    print("\n4️⃣ 测试更新已有设置")
    updated_settings = test_settings.copy()
    updated_settings["paragraph"]["font_family"] = "华文行楷"
    updated_settings["paragraph"]["font_size"] = 15
    updated_settings["enable_table"] = True
    
    try:
        response = requests.post(
            f"{base_url}/api/translation-settings/{new_user_rowid}",
            json=updated_settings,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 更新成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 更新失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 更新测试失败: {e}")
    
    # 5. 最终验证
    print("\n5️⃣ 最终验证更新后的设置")
    try:
        response = requests.get(f"{base_url}/api/translation-settings/{new_user_rowid}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 最终验证成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 验证更新是否正确
            data = result.get("data", {})
            if (data.get("paragraph", {}).get("font_family") == "华文行楷" and
                data.get("paragraph", {}).get("font_size") == 15 and
                data.get("enable_table") == True):
                print("🎉 更新验证成功！设置已正确更新")
            else:
                print("⚠️ 更新验证失败，数据不匹配")
        else:
            print(f"❌ 最终验证失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 最终验证失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎊 测试完成！")
    print(f"\n💡 请到明道云后台查看 yhfygssz 表单")
    print(f"   应该能看到用户 {new_user_rowid} 的翻译设置记录")
    print("   记录中的JSON字段应该包含您刚才设置的格式信息")

if __name__ == "__main__":
    print("⚠️  重要提醒：")
    print("   请确保服务器正在运行: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print("   请将 new_user_rowid 替换为一个新的测试用户ID")
    print()
    
    test_with_new_user()
