.system-status {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 20px;
  margin-bottom: 20px;
}

.status-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.status-header p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #6c757d;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.status-card {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
  transition: all 0.2s ease;
}

.status-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-card.working {
  border-left: 4px solid #28a745;
}

.status-card.pending {
  border-left: 4px solid #ffc107;
}

.status-card.warning {
  border-left: 4px solid #dc3545;
}

.status-card-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.feature-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #dee2e6;
  color: #495057;
}

.feature-info {
  flex: 1;
}

.feature-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-working {
  color: #28a745;
}

.status-pending {
  color: #ffc107;
}

.status-warning {
  color: #dc3545;
}

.feature-description {
  margin: 0;
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

.status-summary {
  display: flex;
  gap: 24px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .status-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .status-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .feature-icon {
    width: 32px;
    height: 32px;
  }
}
