<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明道云API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .test-account {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .test-account h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .test-account code {
            background: #bbdefb;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 明道云翻译API测试</h1>
        
        <div class="test-account">
            <h3>📋 测试账号信息</h3>
            <p>用户名: <code>testuser</code></p>
            <p>密码: <code>testpass123</code></p>
            <p>邮箱: <code><EMAIL></code></p>
        </div>
        
        <!-- 用户注册 -->
        <div class="section">
            <h2>👤 用户注册</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="regUsername" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>邮箱:</label>
                <input type="email" id="regEmail" placeholder="输入邮箱">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="regPassword" placeholder="输入密码">
            </div>
            <div class="form-group">
                <label>姓名:</label>
                <input type="text" id="regFullName" placeholder="输入姓名(可选)">
            </div>
            <button onclick="register()">注册</button>
            <div id="registerResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 用户登录 -->
        <div class="section">
            <h2>🔐 用户登录</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="loginUsername" value="testuser" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="loginPassword" value="testpass123" placeholder="输入密码">
            </div>
            <button onclick="login()">登录</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 获取用户资料 -->
        <div class="section">
            <h2>👨‍💼 用户资料</h2>
            <button onclick="getProfile()">获取用户资料</button>
            <div id="profileResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 获取翻译历史 -->
        <div class="section">
            <h2>📚 翻译历史</h2>
            <div class="form-group">
                <label>页码:</label>
                <input type="number" id="historyPage" value="1" min="1">
            </div>
            <div class="form-group">
                <label>每页数量:</label>
                <input type="number" id="historyPageSize" value="10" min="1" max="100">
            </div>
            <button onclick="getHistory()">获取翻译历史</button>
            <div id="historyResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 健康检查 -->
        <div class="section">
            <h2>💚 健康检查</h2>
            <button onclick="healthCheck()">检查API状态</button>
            <div id="healthResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1/mingdao';
        let authToken = '';

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function register() {
            const username = document.getElementById('regUsername').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const fullName = document.getElementById('regFullName').value;

            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('email', email);
                formData.append('password', password);
                formData.append('full_name', fullName);

                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                showResult('registerResult', data, !response.ok);
            } catch (error) {
                showResult('registerResult', { error: error.message }, true);
            }
        }

        async function login() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    authToken = data.access_token;
                    showResult('loginResult', data);
                } else {
                    showResult('loginResult', data, true);
                }
            } catch (error) {
                showResult('loginResult', { error: error.message }, true);
            }
        }

        async function getProfile() {
            if (!authToken) {
                showResult('profileResult', { error: '请先登录' }, true);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/profile`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();
                showResult('profileResult', data, !response.ok);
            } catch (error) {
                showResult('profileResult', { error: error.message }, true);
            }
        }

        async function getHistory() {
            if (!authToken) {
                showResult('historyResult', { error: '请先登录' }, true);
                return;
            }

            const page = document.getElementById('historyPage').value;
            const pageSize = document.getElementById('historyPageSize').value;

            try {
                const response = await fetch(`${API_BASE}/translate/history?page=${page}&page_size=${pageSize}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();
                showResult('historyResult', data, !response.ok);
            } catch (error) {
                showResult('historyResult', { error: error.message }, true);
            }
        }

        async function healthCheck() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('healthResult', data, !response.ok);
            } catch (error) {
                showResult('healthResult', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
