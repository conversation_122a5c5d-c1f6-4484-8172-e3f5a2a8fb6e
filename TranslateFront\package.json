{"name": "translate-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npx vite build --mode production", "build-with-check": "npx tsc && npx vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "node server.cjs", "serve": "npm run build && npm run start", "pm2:start": "pm2 start ecosystem.config.cjs", "pm2:stop": "pm2 stop translate-front", "pm2:restart": "pm2 restart translate-front", "pm2:logs": "pm2 logs translate-front", "pm2:status": "pm2 status", "production": "npm run build && npm run pm2:start"}, "dependencies": {"docx-preview": "^0.3.0", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "lucide-react": "^0.263.1", "mammoth": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}