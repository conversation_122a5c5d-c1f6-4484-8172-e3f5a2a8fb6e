import { useState } from 'react';
import { X, LogIn, UserPlus, Eye, EyeOff, Globe, Zap, Shield } from 'lucide-react';
import { LoginRequest, RegisterRequest } from '../types/document';
import './LoginModal.css';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLogin: (credentials: LoginRequest) => Promise<void>;
  onRegister?: (userData: RegisterRequest) => Promise<void>;
}

export default function LoginModal({ isOpen, onClose, onLogin, onRegister }: LoginModalProps) {
  const [isLoginMode, setIsLoginMode] = useState(true);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    full_name: ''
  });

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isLoginMode) {
        await onLogin({
          username: formData.username,
          password: formData.password
        });
      } else if (onRegister) {
        await onRegister({
          username: formData.username,
          email: formData.email,
          password: formData.password,
          full_name: formData.full_name || undefined
        });
      }
    } catch (error) {
      console.error('认证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <div className="login-modal-overlay">
      <div className="login-modal-container">
        <div className="login-modal-content">
          {/* 左侧装饰区域 */}
          <div className="login-modal-left">
            <div className="login-modal-brand">
              <Globe size={48} />
              <h1>SMILE TRANS</h1>
              <p>智能翻译系统</p>
            </div>

            <div className="login-modal-features">
              <div className="feature-item">
                <div className="feature-icon">
                  <Zap size={24} />
                </div>
                <div className="feature-text">
                  <h3>高效翻译</h3>
                  <p>AI驱动的智能翻译引擎</p>
                </div>
              </div>

              <div className="feature-item">
                <div className="feature-icon">
                  <Shield size={24} />
                </div>
                <div className="feature-text">
                  <h3>安全可靠</h3>
                  <p>企业级数据安全保护</p>
                </div>
              </div>

              <div className="feature-item">
                <div className="feature-icon">
                  <Globe size={24} />
                </div>
                <div className="feature-text">
                  <h3>多语言支持</h3>
                  <p>支持多种语言互译</p>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧表单区域 */}
          <div className="login-modal-right">
            <div className="login-modal-header">
              <div className="login-modal-title">
                <h2>
                  {isLoginMode ? <LogIn size={28} /> : <UserPlus size={28} />}
                  {isLoginMode ? '欢迎回来' : '创建账号'}
                </h2>
                <p>{isLoginMode ? '登录您的翻译账号' : '注册新的翻译账号'}</p>
              </div>
              <button onClick={onClose} className="login-modal-close">
                <X size={24} />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="login-form">
              <div className="form-group">
                <label>用户名</label>
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  placeholder="请输入用户名"
                />
              </div>

              {!isLoginMode && (
                <>
                  <div className="form-group">
                    <label>邮箱地址</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      placeholder="请输入邮箱地址"
                    />
                  </div>

                  <div className="form-group">
                    <label>姓名 <span>(可选)</span></label>
                    <input
                      type="text"
                      name="full_name"
                      value={formData.full_name}
                      onChange={handleInputChange}
                      placeholder="请输入您的姓名"
                    />
                  </div>
                </>
              )}

              <div className="form-group">
                <label>密码</label>
                <div className="password-input">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    placeholder="请输入密码"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              {/* 测试账号提示 */}
              {isLoginMode && (
                <div className="test-account-tip">
                  <div className="tip-icon">
                    <LogIn size={16} />
                  </div>
                  <div className="tip-content">
                    <h4>测试账号</h4>
                    <p>
                      用户名: <code>testuser</code><br />
                      密码: <code>testpass123</code>
                    </p>
                  </div>
                </div>
              )}

              <button type="submit" disabled={loading} className="login-submit-btn">
                {loading ? (
                  <div className="loading-spinner"></div>
                ) : (
                  <>
                    {isLoginMode ? <LogIn size={20} /> : <UserPlus size={20} />}
                    <span>{isLoginMode ? '立即登录' : '创建账号'}</span>
                  </>
                )}
              </button>
            </form>

            <div className="login-switch">
              <div className="divider">
                <span>或者</span>
              </div>
              <button
                onClick={() => setIsLoginMode(!isLoginMode)}
                className="switch-mode-btn"
              >
                {isLoginMode ? '没有账号？立即注册' : '已有账号？立即登录'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
