<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明道云翻译系统演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        
        .section h2 {
            color: #555;
            margin-top: 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .info {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .info code {
            background: #bbdefb;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .pricing-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .pricing-table th,
        .pricing-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .pricing-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        
        .pricing-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        input[type="number"] {
            width: 150px;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .demo-data {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .demo-data h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        
        .architecture {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .arch-item {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
        }
        
        .arch-item h4 {
            color: #495057;
            margin-top: 0;
        }
        
        .arch-item ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 明道云翻译系统演示</h1>
        
        <div class="info">
            <h3>📋 系统架构说明</h3>
            <p>这是一个完全基于明道云的翻译系统，无需本地数据库，所有数据存储在明道云中。</p>
            <p>明道云配置：AppKey <code>d88c1d2329c42504</code></p>
            <p>API地址：<code>https://dmit.duoningbio.com/api/v2/open</code></p>
        </div>
        
        <!-- 收费规则演示 -->
        <div class="section">
            <h2>💰 收费规则演示</h2>
            <p>输入字符数查看收费计算：</p>
            <input type="number" id="charCount" placeholder="输入字符数" value="8000" min="1">
            <button onclick="calculateCost()">计算费用</button>
            <button onclick="showPricingTable()">显示收费表</button>
            
            <div id="pricingResult" class="result" style="display: none;"></div>
            
            <div class="demo-data">
                <h4>💡 收费规则说明</h4>
                <ul>
                    <li><strong>月付套餐</strong>：20元/月，10万字额度</li>
                    <li><strong>按次收费</strong>：5000字以内5元一次</li>
                    <li><strong>大文档</strong>：超过5000字，每5000字5元</li>
                    <li><strong>批量优惠</strong>：30万字100元</li>
                    <li><strong>免费额度</strong>：新用户1000字符</li>
                </ul>
            </div>
        </div>
        
        <!-- 明道云表结构演示 -->
        <div class="section">
            <h2>📊 明道云表结构</h2>
            <button onclick="showUserTable()">用户表结构</button>
            <button onclick="showTranslationTable()">翻译记录表</button>
            <button onclick="showConsumptionTable()">消费记录表</button>
            
            <div id="tableResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 用户管理演示 -->
        <div class="section">
            <h2>👤 用户管理演示</h2>
            <button onclick="showUserDemo()">用户注册流程</button>
            <button onclick="showLoginDemo()">用户登录流程</button>
            <button onclick="showQuotaDemo()">配额管理演示</button>
            
            <div id="userResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 翻译流程演示 -->
        <div class="section">
            <h2>📚 翻译流程演示</h2>
            <button onclick="showTranslationFlow()">翻译流程</button>
            <button onclick="showFileManagement()">文件管理</button>
            <button onclick="showProgressTracking()">进度跟踪</button>
            
            <div id="translationResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 系统架构 -->
        <div class="section">
            <h2>🏗️ 系统架构</h2>
            <div class="architecture">
                <div class="arch-item">
                    <h4>🗄️ 数据存储</h4>
                    <ul>
                        <li>用户表：用户信息和配额</li>
                        <li>翻译记录表：任务和文件</li>
                        <li>消费记录表：详细账单</li>
                    </ul>
                </div>
                <div class="arch-item">
                    <h4>🔧 后端服务</h4>
                    <ul>
                        <li>FastAPI框架</li>
                        <li>明道云API集成</li>
                        <li>Azure翻译服务</li>
                        <li>JWT认证</li>
                    </ul>
                </div>
                <div class="arch-item">
                    <h4>🌐 前端界面</h4>
                    <ul>
                        <li>响应式设计</li>
                        <li>文件上传</li>
                        <li>进度显示</li>
                        <li>历史记录</li>
                    </ul>
                </div>
                <div class="arch-item">
                    <h4>💳 支付系统</h4>
                    <ul>
                        <li>多种付费方式</li>
                        <li>配额管理</li>
                        <li>自动扣费</li>
                        <li>账单记录</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result success';
            element.textContent = JSON.stringify(data, null, 2);
        }

        // 计算费用
        function calculateCost() {
            const charCount = parseInt(document.getElementById('charCount').value);
            
            if (!charCount || charCount <= 0) {
                showResult('pricingResult', { error: '请输入有效的字符数' });
                return;
            }

            let cost, paymentType, description;

            if (charCount <= 5000) {
                cost = 5;
                paymentType = '按次付费';
                description = `${charCount}字符，按次收费`;
            } else if (charCount >= 300000) {
                cost = 100;
                paymentType = '批量优惠';
                description = `${charCount}字符，批量优惠`;
            } else {
                cost = Math.ceil(charCount / 5000) * 5;
                paymentType = '按次付费';
                description = `${charCount}字符，分段收费`;
            }

            const result = {
                success: true,
                input: {
                    charCount: charCount,
                    userType: '普通用户'
                },
                calculation: {
                    cost: cost,
                    paymentType: paymentType,
                    description: description
                },
                alternatives: {
                    monthly: charCount <= 100000 ? '可使用月付套餐(20元/月)' : '超出月付额度',
                    freeQuota: charCount <= 1000 ? '可使用免费额度' : '超出免费额度'
                }
            };

            showResult('pricingResult', result);
        }

        // 显示收费表
        function showPricingTable() {
            const pricingData = {
                title: '明道云翻译系统收费表',
                rules: [
                    { type: '免费额度', chars: '1000字符', price: '0元', desc: '新用户免费' },
                    { type: '按次付费(小)', chars: '1-5000字符', price: '5元/次', desc: '适合小文档' },
                    { type: '按次付费(大)', chars: '5001-299999字符', price: '5元/5000字', desc: '按段收费' },
                    { type: '批量优惠', chars: '300000字符+', price: '100元', desc: '大批量优惠' },
                    { type: '月付套餐', chars: '100000字符/月', price: '20元/月', desc: '包月服务' }
                ],
                examples: [
                    { chars: 1000, cost: 0, method: '免费额度' },
                    { chars: 3000, cost: 5, method: '按次付费' },
                    { chars: 8000, cost: 10, method: '按次付费(分段)' },
                    { chars: 15000, cost: 15, method: '按次付费(分段)' },
                    { chars: 300000, cost: 100, method: '批量优惠' }
                ]
            };

            showResult('pricingResult', pricingData);
        }

        // 显示用户表结构
        function showUserTable() {
            const userTableStructure = {
                tableName: '用户表 (users)',
                tableId: '6886e20ba849420e13f69b23',
                fields: [
                    { id: '6886e20ba849420e13f69b24', name: '用户名', type: 'text', required: true },
                    { id: '6886e4c8a849420e13f69b30', name: '邮箱', type: 'email', required: true },
                    { id: '6886e4c8a849420e13f69b31', name: '密码哈希', type: 'text', required: true },
                    { id: '6886e4c8a849420e13f69b32', name: '真实姓名', type: 'text', required: false },
                    { id: '6886e4c8a849420e13f69b33', name: '手机号', type: 'text', required: false },
                    { id: '6886e4c8a849420e13f69b34', name: '用户类型', type: 'option', required: true },
                    { id: '6886e4c8a849420e13f69b35', name: '用户状态', type: 'option', required: true },
                    { id: '6886e4c8a849420e13f69b36', name: '账户余额', type: 'number', required: true },
                    { id: '6886e4c8a849420e13f69b37', name: '总字符配额', type: 'number', required: true },
                    { id: '6886e4c8a849420e13f69b38', name: '已使用字符数', type: 'number', required: true }
                ],
                sampleData: {
                    username: 'testuser',
                    email: '<EMAIL>',
                    userType: '免费用户',
                    status: '正常',
                    balance: 50.0,
                    totalQuota: 10000,
                    usedQuota: 2000
                }
            };

            showResult('tableResult', userTableStructure);
        }

        // 显示翻译记录表
        function showTranslationTable() {
            const translationTableStructure = {
                tableName: '翻译记录表 (translations)',
                tableId: '6886f053a849420e13f69b61',
                fields: [
                    { id: '6886f7a4a849420e13f69b6f', name: '原文件', type: 'attachment', required: true },
                    { id: '6886f7a4a849420e13f69b70', name: '翻译后文件', type: 'attachment', required: false },
                    { id: '6886f7a4a849420e13f69b71', name: '翻译状态', type: 'option', required: true },
                    { id: '6886f7a4a849420e13f69b72', name: '总字符数', type: 'number', required: true },
                    { id: '6886f7a4a849420e13f69b73', name: '翻译进度', type: 'number', required: true },
                    { id: '6886f7a4a849420e13f69b74', name: '翻译费用', type: 'number', required: true },
                    { id: '6886f887a849420e13f69b8f', name: '用户关联', type: 'relation', required: true }
                ],
                statusOptions: ['待处理', '翻译中', '已完成', '失败', '已取消'],
                sampleData: {
                    originalFile: 'document.docx',
                    status: '已完成',
                    totalCharacters: 8000,
                    progress: 100,
                    cost: 10,
                    paymentType: '按次付费'
                }
            };

            showResult('tableResult', translationTableStructure);
        }

        // 显示消费记录表
        function showConsumptionTable() {
            const consumptionTableStructure = {
                tableName: '消费记录表 (consumption_records)',
                tableId: '6886f9ffa849420e13f69bc5',
                fields: [
                    { id: '6886fbeda849420e13f69bd3', name: '用户关联', type: 'relation', required: true },
                    { id: '6886fbeda849420e13f69bd5', name: '翻译记录关联', type: 'relation', required: true },
                    { id: '6886fbeda849420e13f69bd7', name: '消费类型', type: 'option', required: true },
                    { id: '6886fbeda849420e13f69bd8', name: '使用字符数', type: 'number', required: true },
                    { id: '6886fbeda849420e13f69bd9', name: '消费金额', type: 'number', required: true },
                    { id: '6886fbeda849420e13f69bda', name: '扣费来源', type: 'option', required: true }
                ],
                consumptionTypes: ['翻译扣费', '退款', '赠送', '调整'],
                paymentSources: ['月付配额', '按次付费', '余额扣费', '免费额度'],
                sampleData: {
                    consumptionType: '翻译扣费',
                    charactersUsed: 8000,
                    amount: 10,
                    paymentSource: '余额扣费',
                    beforeBalance: 50,
                    afterBalance: 40
                }
            };

            showResult('tableResult', consumptionTableStructure);
        }

        // 显示用户管理演示
        function showUserDemo() {
            const userManagementDemo = {
                title: '用户管理演示',
                registration: {
                    step1: '用户填写注册信息',
                    step2: '系统验证邮箱和用户名唯一性',
                    step3: '密码哈希加密存储',
                    step4: '创建明道云用户记录',
                    step5: '分配初始免费配额(1000字符)',
                    result: '注册成功，返回用户ID'
                },
                authentication: {
                    step1: '用户输入用户名和密码',
                    step2: '查询明道云用户表',
                    step3: '验证密码哈希',
                    step4: '更新最后登录时间',
                    step5: '生成JWT访问令牌',
                    result: '登录成功，返回用户信息和令牌'
                },
                quotaManagement: {
                    freeQuota: '新用户1000字符免费额度',
                    monthlyQuota: '月付用户10万字符/月',
                    balancePayment: '余额不足时按次付费',
                    quotaCheck: '翻译前检查配额充足性',
                    autoDeduction: '翻译完成后自动扣除配额'
                }
            };

            showResult('userResult', userManagementDemo);
        }

        // 显示登录演示
        function showLoginDemo() {
            const loginDemo = {
                title: '用户登录流程演示',
                testAccount: {
                    username: 'testuser',
                    password: 'testpass123',
                    email: '<EMAIL>'
                },
                loginProcess: {
                    step1: 'POST /api/v1/mingdao/auth/login',
                    step2: '验证用户名和密码',
                    step3: '查询明道云用户表',
                    step4: '比较密码哈希',
                    step5: '生成JWT令牌',
                    step6: '返回用户信息和令牌'
                },
                responseExample: {
                    success: true,
                    message: '登录成功',
                    access_token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
                    token_type: 'bearer',
                    user: {
                        id: '93235f77-767c-41ce-b4df-65a3c58ba828',
                        username: 'testuser',
                        email: '<EMAIL>',
                        balance: 50.0,
                        totalQuota: 10000,
                        usedQuota: 2000
                    }
                }
            };

            showResult('userResult', loginDemo);
        }

        // 显示配额演示
        function showQuotaDemo() {
            const quotaDemo = {
                title: '配额管理演示',
                quotaTypes: {
                    freeQuota: {
                        name: '免费配额',
                        amount: 1000,
                        description: '新用户注册时获得',
                        priority: 2
                    },
                    monthlyQuota: {
                        name: '月付配额',
                        amount: 100000,
                        description: '月付用户每月配额',
                        priority: 1
                    },
                    balancePayment: {
                        name: '余额付费',
                        description: '配额不足时使用余额',
                        priority: 3
                    }
                },
                deductionLogic: {
                    step1: '检查月付配额是否充足',
                    step2: '月付不足则检查免费配额',
                    step3: '免费配额不足则检查余额',
                    step4: '余额充足则按费用扣除',
                    step5: '记录消费记录到明道云'
                },
                exampleScenario: {
                    user: {
                        monthlyQuota: 100000,
                        monthlyUsed: 95000,
                        freeQuota: 1000,
                        freeUsed: 500,
                        balance: 50
                    },
                    translation: {
                        characters: 8000,
                        cost: 10
                    },
                    result: {
                        useMonthly: 5000,
                        useFree: 500,
                        useBalance: 2500,
                        totalCost: 5
                    }
                }
            };

            showResult('userResult', quotaDemo);
        }

        // 显示翻译流程
        function showTranslationFlow() {
            const translationFlow = {
                title: '翻译流程演示',
                uploadProcess: {
                    step1: '用户上传Word文档',
                    step2: '系统解析文档内容',
                    step3: '计算字符数和费用',
                    step4: '检查用户配额',
                    step5: '创建翻译记录',
                    step6: '上传文件到明道云',
                    step7: '扣除用户配额',
                    step8: '启动翻译任务'
                },
                translationProcess: {
                    step1: '调用Azure翻译API',
                    step2: '分段翻译文档内容',
                    step3: '实时更新翻译进度',
                    step4: '保持原文档格式',
                    step5: '生成翻译后文档',
                    step6: '上传结果文件',
                    step7: '更新记录状态为完成'
                },
                statusTracking: {
                    PENDING: '待处理 - 任务已创建',
                    PROCESSING: '翻译中 - 正在翻译',
                    COMPLETED: '已完成 - 翻译完成',
                    FAILED: '失败 - 翻译出错',
                    CANCELLED: '已取消 - 用户取消'
                }
            };

            showResult('translationResult', translationFlow);
        }

        // 显示文件管理
        function showFileManagement() {
            const fileManagement = {
                title: '文件管理演示',
                supportedFormats: ['.docx', '.doc'],
                fileUpload: {
                    maxSize: '50MB',
                    encoding: 'base64',
                    storage: '明道云附件字段',
                    preview: 'OnlyOffice在线预览'
                },
                fileProcessing: {
                    step1: '文件格式验证',
                    step2: '文件大小检查',
                    step3: '转换为base64编码',
                    step4: '上传到明道云',
                    step5: '获取文件预览链接',
                    step6: '解析文档内容'
                },
                downloadFeatures: {
                    originalFile: '原文件下载',
                    translatedFile: '翻译文件下载',
                    previewUrl: '在线预览链接',
                    directDownload: '直接下载链接'
                }
            };

            showResult('translationResult', fileManagement);
        }

        // 显示进度跟踪
        function showProgressTracking() {
            const progressTracking = {
                title: '进度跟踪演示',
                realTimeUpdates: {
                    method: 'WebSocket连接',
                    frequency: '每秒更新',
                    data: ['进度百分比', '当前状态', '预计完成时间']
                },
                progressStages: [
                    { stage: 0, status: '任务创建', description: '翻译记录已创建' },
                    { stage: 10, status: '文件解析', description: '正在解析文档内容' },
                    { stage: 30, status: '开始翻译', description: '调用翻译API' },
                    { stage: 70, status: '翻译进行中', description: '正在翻译文档内容' },
                    { stage: 90, status: '格式处理', description: '保持原文档格式' },
                    { stage: 100, status: '翻译完成', description: '文件已生成' }
                ],
                errorHandling: {
                    networkError: '网络错误 - 自动重试',
                    apiError: 'API错误 - 记录错误信息',
                    formatError: '格式错误 - 返回错误详情',
                    quotaError: '配额不足 - 提示充值'
                }
            };

            showResult('translationResult', progressTracking);
        }

        // 页面加载时显示欢迎信息
        window.addEventListener('load', function() {
            console.log('🚀 明道云翻译系统演示页面已加载');
            
            // 自动显示收费规则
            setTimeout(() => {
                showPricingTable();
            }, 1000);
        });
    </script>
</body>
</html>
