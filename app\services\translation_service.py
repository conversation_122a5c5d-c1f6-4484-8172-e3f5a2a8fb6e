"""
翻译服务
"""
import json
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from fastapi import HTTPException, status

from app.models.user import User
from app.models.file import UploadedFile, FileStatus
from app.models.translation import TranslationHistory, TranslationStatus
from app.schemas.translation import TranslationRequest
from app.services.azure_translator import azure_translator
from app.services.file_service import FileService
from app.services.mingdao_service import mingdao_service
from app.services.mingdao_full_service import mingdao_full_service
from app.utils.logger import logger


class TranslationService:
    """翻译服务类"""
    
    @staticmethod
    async def create_translation(
        db: AsyncSession,
        request: TranslationRequest,
        user: User
    ) -> TranslationHistory:
        """创建翻译任务"""
        # 获取文件
        file_record = await FileService.get_file_by_id(db, request.file_id, user)
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        if file_record.status != FileStatus.PROCESSED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File has not been processed yet"
            )
        
        # 检查用户配额
        estimated_chars = file_record.total_characters
        if not user.can_translate(estimated_chars):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Translation quota exceeded. Remaining: {user.remaining_quota}"
            )
        
        # 验证语言对
        is_valid = await azure_translator.validate_language_pair(
            request.source_language, request.target_language
        )
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported language pair"
            )
        
        # 计算成本
        cost = azure_translator.calculate_cost(estimated_chars)
        
        # 创建翻译记录
        translation = TranslationHistory(
            user_id=user.id,
            file_id=file_record.id,
            source_language=request.source_language,
            target_language=request.target_language,
            status=TranslationStatus.PENDING,
            total_characters=estimated_chars,
            cost=cost,
            original_text=file_record.extracted_text
        )
        
        db.add(translation)
        await db.commit()
        await db.refresh(translation)
        
        # 异步开始翻译处理，避免阻塞当前请求
        import asyncio
        asyncio.create_task(
            TranslationService._process_translation_async(translation.id, request)
        )
        
        logger.info(f"Translation task created: {translation.id} for user {user.username}")
        return translation

    @staticmethod
    async def _process_translation_async(translation_id: int, request):
        """异步处理翻译任务（带超时处理）"""
        from app.db.database import get_async_db_session
        import asyncio

        async with get_async_db_session() as db:
            try:
                # 获取翻译记录
                result = await db.execute(
                    select(TranslationHistory).where(TranslationHistory.id == translation_id)
                )
                translation = result.scalar_one_or_none()

                if not translation:
                    logger.error(f"Translation {translation_id} not found")
                    return

                # 设置超时时间（基于文档大小，最少5分钟，最多30分钟）
                timeout_minutes = min(30, max(5, translation.total_characters // 1000))
                timeout_seconds = timeout_minutes * 60

                logger.info(f"Starting translation {translation_id} with {timeout_minutes} minute timeout")

                # 使用asyncio.wait_for添加超时处理
                await asyncio.wait_for(
                    TranslationService._process_translation(db, translation, request),
                    timeout=timeout_seconds
                )

            except asyncio.TimeoutError:
                logger.error(f"Translation {translation_id} timed out after {timeout_minutes} minutes")
                # 更新翻译状态为失败
                try:
                    result = await db.execute(
                        select(TranslationHistory).where(TranslationHistory.id == translation_id)
                    )
                    translation = result.scalar_one_or_none()
                    if translation:
                        translation.status = TranslationStatus.FAILED
                        translation.error_message = f"翻译超时（{timeout_minutes}分钟）"
                        await db.commit()
                except Exception:
                    pass
            except Exception as e:
                logger.error(f"Async translation processing error: {e}")
                # 更新翻译状态为失败
                try:
                    result = await db.execute(
                        select(TranslationHistory).where(TranslationHistory.id == translation_id)
                    )
                    translation = result.scalar_one_or_none()
                    if translation:
                        translation.status = TranslationStatus.FAILED
                        translation.error_message = str(e)
                        await db.commit()
                except Exception:
                    pass


    @staticmethod
    async def _process_translation(
        db: AsyncSession,
        translation: TranslationHistory,
        request: TranslationRequest
    ):
        """处理翻译任务 - 严格按照VBA宏流程"""
        translation_log = []

        try:
            # 更新状态为进行中
            translation.status = TranslationStatus.IN_PROGRESS
            translation.progress = 0.1
            await db.commit()

            translation_log.append("=== Translation Log Start ===")

            # 获取文件内容
            file_content = await FileService.get_file_content(
                db, translation.file_id,
                await TranslationService._get_user_by_id(db, translation.user_id)
            )

            paragraphs = file_content["paragraphs"]
            tables = file_content["tables"]

            # 第一步：翻译正文段落（跳过表格内容和已翻译内容）
            translated_paragraphs = []
            translation_log.append("\n==== 翻译正文段落 ====")

            for i, para_text in enumerate(paragraphs):
                # 清理文本（模拟VBA中的清理过程）
                cleaned_text = TranslationService._clean_paragraph_text(para_text)

                # 跳过短文本和已翻译内容（以"["开头）
                if len(cleaned_text) > 1 and not cleaned_text.startswith("["):
                    try:
                        translated_text = await azure_translator.translate_text(
                            [cleaned_text], request.target_language, request.source_language
                        )

                        if translated_text and translated_text[0].get('translations'):
                            result_text = translated_text[0]['translations'][0]['text']
                            result_text = TranslationService._clean_translated_text(result_text)
                            translated_paragraphs.append(result_text)

                            # 记录日志（模拟VBA日志格式）
                            translation_log.append(f"---- Paragraph {i + 1} ----")
                            translation_log.append(f"Translated: {result_text}")
                            if "\\" in result_text:
                                translation_log.append("[!] Contains slashes")
                            if "[" in result_text:
                                translation_log.append("[!] Possibly failed")
                            translation_log.append("")
                        else:
                            translated_paragraphs.append("[Translation failed]")
                            translation_log.append(f"---- Paragraph {i + 1} ----")
                            translation_log.append("[!] Translation failed")
                            translation_log.append("")
                    except Exception as e:
                        translated_paragraphs.append(f"[Error: {str(e)}]")
                        translation_log.append(f"---- Paragraph {i + 1} ----")
                        translation_log.append(f"[!] Error: {str(e)}")
                        translation_log.append("")
                else:
                    translated_paragraphs.append("")  # 跳过的段落

                # 更新进度（每10个段落更新一次，减少数据库压力）
                if i % 10 == 0 or i == len(paragraphs) - 1:
                    translation.progress = 0.2 + (i / len(paragraphs)) * 0.4
                    await db.commit()

            # 第二步：翻译表格单元格
            translated_tables = []
            translation_log.append("\n==== 翻译表格单元格 ====")

            total_cells = sum(len(table) * len(table[0]) if table and table[0] else 0 for table in tables)
            processed_cells = 0

            for table_idx, table in enumerate(tables):
                translated_table = []
                for row_idx, row in enumerate(table):
                    translated_row = []
                    for col_idx, cell_text in enumerate(row):
                        # 清理单元格文本
                        cleaned_cell = TranslationService._clean_cell_text(cell_text)

                        if len(cleaned_cell) > 1 and not cleaned_cell.startswith("["):
                            try:
                                translated_cell = await azure_translator.translate_text(
                                    [cleaned_cell], request.target_language, request.source_language
                                )

                                if translated_cell and translated_cell[0].get('translations'):
                                    result_text = translated_cell[0]['translations'][0]['text']
                                    result_text = TranslationService._clean_translated_text(result_text)
                                    translated_row.append(result_text)

                                    # 记录表格翻译日志
                                    translation_log.append(f"[Table r{row_idx + 1} c{col_idx + 1}]")
                                    translation_log.append(f"Original: {TranslationService._clean_text_for_log(cleaned_cell)}")
                                    translation_log.append(f"Translated: {result_text}")
                                    if "\\" in result_text:
                                        translation_log.append("[!] Contains slashes")
                                    if "[" in result_text:
                                        translation_log.append("[!] Possibly failed")
                                    translation_log.append("")
                                else:
                                    translated_row.append("[Translation failed]")
                            except Exception as e:
                                translated_row.append(f"[Error: {str(e)}]")
                        else:
                            translated_row.append(cell_text)  # 保持原文

                        # 更新表格翻译进度（每5个单元格更新一次）
                        processed_cells += 1
                        if processed_cells % 5 == 0 or processed_cells == total_cells:
                            if total_cells > 0:
                                table_progress = (processed_cells / total_cells) * 0.2  # 表格翻译占20%进度
                                translation.progress = 0.6 + table_progress
                                await db.commit()

                    translated_table.append(translated_row)
                translated_tables.append(translated_table)

            # 更新进度到80%
            translation.progress = 0.8
            await db.commit()

            # 第三步：组合翻译结果（按照VBA格式）
            if request.include_original:
                combined_text = TranslationService._create_vba_style_bilingual_document(
                    paragraphs, translated_paragraphs, tables, translated_tables
                )
            else:
                combined_text = TranslationService._create_vba_style_translated_document(
                    translated_paragraphs, translated_tables
                )

            # 完成翻译
            translation.status = TranslationStatus.COMPLETED
            translation.progress = 1.0
            translation.translated_text = combined_text
            translation.confidence_score = 0.85
            translation.completed_at = datetime.utcnow()
            translation.translated_characters = len(combined_text)

            # 保存翻译日志
            translation_log.append("\n=== Translation Finished ===")
            translation.term_corrections = "\n".join(translation_log)

            # 更新用户配额
            user = await TranslationService._get_user_by_id(db, translation.user_id)
            user.used_quota += translation.total_characters

            await db.commit()

            # 上传到明道云
            await TranslationService._upload_to_mingdao(translation, request)

            logger.info(f"Translation completed: {translation.id}")

        except Exception as e:
            logger.error(f"Translation processing error: {e}")
            translation.status = TranslationStatus.FAILED
            translation.error_message = str(e)
            translation.term_corrections = "\n".join(translation_log + [f"Error: {str(e)}"])
            await db.commit()

    @staticmethod
    async def _upload_to_mingdao(translation: TranslationHistory, request: TranslationRequest):
        """上传翻译结果到明道云"""
        try:
            from app.db.database import get_async_db_session
            import os
            import tempfile

            async with get_async_db_session() as db:
                # 获取原文件信息
                file_result = await db.execute(
                    select(UploadedFile).where(UploadedFile.id == translation.file_id)
                )
                original_file = file_result.scalar_one_or_none()

                if not original_file:
                    logger.error(f"Original file not found: {translation.file_id}")
                    return

                # 创建临时文件保存翻译结果
                with tempfile.NamedTemporaryFile(mode='w', suffix='.docx', delete=False) as temp_file:
                    # 这里应该生成实际的Word文档，暂时用文本代替
                    temp_file.write(translation.translated_content or "翻译内容")
                    temp_file_path = temp_file.name

                try:
                    # 构建备注信息
                    remark = f"翻译任务ID: {translation.id}\n"
                    remark += f"源语言: {request.source_language}\n"
                    remark += f"目标语言: {request.target_language}\n"
                    remark += f"翻译时间: {translation.created_at}\n"
                    remark += f"字符数: {translation.total_characters}"

                    # 如果是新建记录
                    if not translation.mingdao_row_id:
                        # 创建明道云记录
                        result = await mingdao_service.create_translation_record(
                            original_file_path=original_file.file_path,
                            original_filename=original_file.filename,
                            remark=remark
                        )

                        if result.get("success"):
                            # 保存明道云记录ID
                            translation.mingdao_row_id = result.get("data")
                            await db.commit()
                            logger.info(f"Created mingdao record: {translation.mingdao_row_id}")

                    # 更新翻译文件
                    if translation.mingdao_row_id:
                        translated_filename = f"translated_{original_file.filename}"
                        await mingdao_service.update_translation_record(
                            row_id=translation.mingdao_row_id,
                            translated_file_path=temp_file_path,
                            translated_filename=translated_filename,
                            remark=remark + f"\n状态: 翻译完成"
                        )
                        logger.info(f"Updated mingdao record: {translation.mingdao_row_id}")

                finally:
                    # 清理临时文件
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Upload to mingdao failed: {e}")
            # 不抛出异常，避免影响翻译主流程
    
    @staticmethod
    def _clean_paragraph_text(text: str) -> str:
        """清理段落文本（模拟VBA清理过程）"""
        # 去除分页符和表格符
        text = text.replace('\f', '')  # 分页符
        text = text.replace('\x07', '')  # 表格符
        text = text.replace('\r', '')  # 回车符
        text = text.replace('\n', ' ')  # 换行符替换为空格
        return text.strip()

    @staticmethod
    def _clean_cell_text(text: str) -> str:
        """清理表格单元格文本"""
        text = text.replace('\x07', '')  # 表格符
        text = text.replace('\r', '')  # 回车符
        text = text.replace('\n', ' ')  # 换行符
        return text.strip()

    @staticmethod
    def _clean_translated_text(text: str) -> str:
        """清理翻译后的文本（模拟VBA CleanTranslatedText函数）"""
        text = text.replace('\\\\', '\\')
        text = text.replace('\\r', '')
        text = text.replace('\\t', '')
        text = text.replace('\\n', '')
        text = text.replace('\r', '')
        text = text.replace('\n', '')
        return text.strip()

    @staticmethod
    def _clean_text_for_log(text: str) -> str:
        """清理文本用于日志记录（模拟VBA CleanTextForLog函数）"""
        result = ""
        for char in text:
            # 只保留可打印字符(32-126)和换行(13,10)
            ascii_val = ord(char)
            if (32 <= ascii_val <= 126) or ascii_val in [13, 10]:
                result += char
            else:
                result += " "
        return result

    @staticmethod
    def _create_vba_style_bilingual_document(
        original_paragraphs: List[str],
        translated_paragraphs: List[str],
        original_tables: List[List[List[str]]],
        translated_tables: List[List[List[str]]]
    ) -> str:
        """创建VBA风格的双语对照文档，支持Word文档处理"""
        result = []

        # 处理段落 - 使用标记格式便于Word文档处理
        for i, (orig, trans) in enumerate(zip(original_paragraphs, translated_paragraphs)):
            if orig.strip() and trans.strip():
                # 使用标记格式，便于docx_processor解析
                result.append(f"[原文] {orig.strip()}\n")
                result.append(f"[译文] {trans.strip()}\n")
                result.append("\n")  # 段落间空行

        # 处理表格 - 每个原文单元格后紧跟译文
        for table_idx, (orig_table, trans_table) in enumerate(zip(original_tables, translated_tables)):
            result.append(f"\n=== 表格 {table_idx + 1} ===\n")
            for row_idx, (orig_row, trans_row) in enumerate(zip(orig_table, trans_table)):
                for col_idx, (orig_cell, trans_cell) in enumerate(zip(orig_row, trans_row)):
                    if orig_cell.strip():
                        result.append(f"[原文] {orig_cell.strip()}\n")
                        if trans_cell.strip() and not trans_cell.startswith("["):
                            result.append(f"[译文] {trans_cell.strip()}\n")
                        result.append("\n")
            result.append("\n")

        return "".join(result)

    @staticmethod
    def _create_vba_style_translated_document(
        translated_paragraphs: List[str],
        translated_tables: List[List[List[str]]]
    ) -> str:
        """创建VBA风格的纯译文文档"""
        result = []

        # 添加翻译后的段落
        for para in translated_paragraphs:
            if para.strip() and not para.startswith("["):
                result.append(f"{para}\n\n")

        # 添加翻译后的表格
        for table_idx, table in enumerate(translated_tables):
            result.append(f"\n=== 表格 {table_idx + 1} ===\n")
            for row in table:
                translated_cells = []
                for cell in row:
                    if cell.strip() and not cell.startswith("["):
                        translated_cells.append(cell)
                    else:
                        translated_cells.append("")
                if any(translated_cells):
                    result.append("\t".join(translated_cells) + "\n")
            result.append("\n")

        return "".join(result)
    
    @staticmethod
    async def _get_user_by_id(db: AsyncSession, user_id: int) -> User:
        """根据ID获取用户"""
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalar_one()
    
    @staticmethod
    async def get_translation_by_id(
        db: AsyncSession,
        translation_id: int,
        user: User
    ) -> Optional[TranslationHistory]:
        """根据ID获取翻译记录"""
        result = await db.execute(
            select(TranslationHistory).where(
                and_(
                    TranslationHistory.id == translation_id,
                    TranslationHistory.user_id == user.id
                )
            )
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_user_translations(
        db: AsyncSession,
        user: User,
        skip: int = 0,
        limit: int = 20
    ) -> tuple[List[TranslationHistory], int]:
        """获取用户的翻译历史"""
        # 获取总数
        count_result = await db.execute(
            select(TranslationHistory).where(TranslationHistory.user_id == user.id)
        )
        total = len(count_result.scalars().all())
        
        # 获取分页数据
        result = await db.execute(
            select(TranslationHistory)
            .where(TranslationHistory.user_id == user.id)
            .order_by(TranslationHistory.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        translations = result.scalars().all()
        
        return translations, total

    @staticmethod
    async def process_mingdao_translation(
        db: AsyncSession,
        row_id: str,
        source_language: str,
        target_language: str,
        translation_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        处理明道云触发的翻译请求

        Args:
            db: 数据库会话
            row_id: 明道云记录ID
            source_language: 源语言代码
            target_language: 目标语言代码
            translation_settings: 翻译格式设置（可选）

        Returns:
            翻译处理结果
        """
        try:
            logger.info(f"开始处理明道云翻译: row_id={row_id}, {source_language}->{target_language}")

            # 1. 从明道云获取记录详情
            record_data = await mingdao_full_service.get_record_detail(row_id)
            if not record_data:
                raise Exception(f"无法获取明道云记录: {row_id}")

            logger.info(f"获取到记录数据，字段数量: {len(record_data)}")
            logger.info(f"记录数据键: {list(record_data.keys())}")

            # 2. 获取原文件信息
            # 尝试多种字段名获取原文件
            original_file_field = (
                record_data.get('original_file') or
                record_data.get('6886f7a4a849420e13f69b6f')
            )

            if not original_file_field:
                logger.error(f"记录数据: {record_data}")
                raise Exception("记录中没有找到原文件")

            logger.info(f"原文件字段类型: {type(original_file_field)}")
            logger.info(f"原文件字段内容: {str(original_file_field)[:200]}...")

            # 解析文件信息
            if isinstance(original_file_field, str):
                try:
                    file_data = json.loads(original_file_field)
                    if isinstance(file_data, list) and len(file_data) > 0:
                        file_info = file_data[0]
                    else:
                        raise Exception("文件数据格式错误")
                except json.JSONDecodeError:
                    raise Exception("无法解析文件数据")
            elif isinstance(original_file_field, list) and len(original_file_field) > 0:
                # 如果已经是列表格式
                file_info = original_file_field[0]
            else:
                file_info = original_file_field

            # 3. 下载文件内容
            # 根据明道云返回的数据结构获取下载链接
            file_url = (
                file_info.get('DownloadUrl') or
                file_info.get('original_file_full_path') or
                file_info.get('url') or
                file_info.get('previewUrl')
            )

            if not file_url:
                logger.error(f"文件信息: {file_info}")
                raise Exception("无法获取文件下载链接")

            # 4. 获取文件名
            filename = (
                file_info.get('original_file_name') or
                file_info.get('originalFilename') or
                file_info.get('file_name') or
                'unknown.txt'
            )

            logger.info(f"文件下载链接: {file_url}")
            logger.info(f"文件名: {filename}")

            # 5. 处理文件并提取文本
            file_content = await mingdao_full_service.download_file(file_url)
            if not file_content:
                raise Exception("无法下载文件内容")

            # 6. 提取文本内容（根据文件类型）
            extracted_content = await TranslationService._extract_text_from_content(
                file_content, filename, translation_settings
            )

            # 检查是否是Word文档（已经翻译完成）
            if filename.lower().endswith(('.doc', '.docx')):
                # Word文档已经在解析阶段完成翻译，extracted_content是翻译后的文档bytes
                if not extracted_content:
                    raise Exception("Word文档翻译失败")

                # 估算字符数（用于计费）
                char_count = 1000  # Word文档的估算字符数
                estimated_cost = char_count * 0.001

                # 直接跳到文件上传步骤
                translated_filename = TranslationService._generate_translated_filename(
                    filename, source_language, target_language
                )

                # 上传翻译后的Word文档
                translated_file_data = await mingdao_full_service.upload_docx_file(
                    extracted_content, translated_filename
                )

                # 更新明道云记录为完成状态
                await mingdao_full_service.update_record(row_id, {
                    '6886f7a4a849420e13f69b71': '7c60c3c6-c56e-4ab8-8979-278f1b359e80',  # 已完成状态
                    '6886f7a4a849420e13f69b70': json.dumps([translated_file_data]),  # 翻译后文件
                    '6886f7a4a849420e13f69b72': str(char_count),  # 总字符数
                    '6886f7a4a849420e13f69b73': f"{estimated_cost:.2f}",  # 翻译费用
                    '6886f7a4a849420e13f69b76': datetime.now().isoformat()  # 完成时间
                })

                logger.info(f"Word文档翻译完成: row_id={row_id}, 估算字符数={char_count}")

                return {
                    "row_id": row_id,
                    "status": "completed",
                    "char_count": char_count,
                    "cost": estimated_cost,
                    "translated_filename": translated_filename
                }

            # 对于其他文件类型，继续原有的翻译流程
            if not extracted_content or not extracted_content.strip():
                raise Exception("无法从文件中提取文本内容")

            # 7. 计算字符数和费用
            char_count = len(extracted_content)
            estimated_cost = char_count * 0.001  # 每1000字符1元

            # 7. 更新明道云记录状态为"翻译中"
            await mingdao_full_service.update_record(row_id, {
                '6886f7a4a849420e13f69b71': 'd9a70a50-3369-4e62-96c8-4e183996368c',  # 翻译中状态
                '6886f7a4a849420e13f69b72': str(char_count),  # 总字符数
                '6886f7a4a849420e13f69b73': f"{estimated_cost:.2f}",  # 翻译费用
                '6886f7a4a849420e13f69b75': datetime.now().isoformat()  # 更新时间
            })

            # 8. 执行翻译
            logger.info(f"开始翻译文本，字符数: {char_count}")
            # Azure翻译器期望输入是列表，返回也是列表
            translation_results = await azure_translator.translate_text(
                [extracted_content], target_language, source_language
            )

            if not translation_results or len(translation_results) == 0:
                raise Exception("翻译服务返回空结果")

            logger.info(f"翻译API返回结果: {translation_results}")

            # 提取翻译后的文本
            # Azure翻译器返回格式: [{'translations': [{'text': '翻译文本'}]}]
            first_result = translation_results[0]
            logger.info(f"第一个翻译结果: {first_result}")

            if 'translations' in first_result and len(first_result['translations']) > 0:
                translated_text = first_result['translations'][0].get('text', '')
            else:
                translated_text = ''

            logger.info(f"提取的翻译文本: {translated_text[:100]}...")

            if not translated_text:
                raise Exception("翻译结果中没有找到文本内容")

            logger.info(f"翻译完成，翻译后字符数: {len(translated_text)}")

            # 9. 生成翻译后的文件
            translated_filename = TranslationService._generate_translated_filename(
                filename, source_language, target_language
            )

            # 10. 上传翻译后的文件到明道云
            translated_file_data = await mingdao_full_service.upload_text_as_file(
                translated_text, translated_filename
            )

            # 11. 更新明道云记录为完成状态
            await mingdao_full_service.update_record(row_id, {
                '6886f7a4a849420e13f69b71': '7c60c3c6-c56e-4ab8-8979-278f1b359e80',  # 已完成状态
                '6886f7a4a849420e13f69b70': json.dumps([translated_file_data]),  # 翻译后文件
                '6886f7a4a849420e13f69b76': datetime.now().isoformat()  # 完成时间
            })

            logger.info(f"翻译完成: row_id={row_id}, 字符数={char_count}")

            return {
                "row_id": row_id,
                "status": "completed",
                "char_count": char_count,
                "cost": estimated_cost,
                "translated_filename": translated_filename
            }

        except Exception as e:
            logger.error(f"明道云翻译处理失败: {e}")

            # 更新记录状态为失败
            try:
                await mingdao_full_service.update_record(row_id, {
                    '6886f7a4a849420e13f69b71': '80fa2f81-3381-4228-b0a6-0ec3517fe205',  # 失败状态
                    '6886f7a4a849420e13f69b75': datetime.now().isoformat()  # 更新时间
                })
            except:
                pass

            raise e

    @staticmethod
    async def _extract_text_from_content(content: bytes, filename: str, translation_settings: Optional[Dict[str, Any]] = None) -> str:
        """从文件内容中提取文本"""
        try:
            # 根据文件扩展名处理不同类型的文件
            ext = filename.lower().split('.')[-1] if '.' in filename else ''

            if ext in ['txt', 'md']:
                # 文本文件
                return content.decode('utf-8', errors='ignore')
            elif ext in ['doc', 'docx']:
                # Word文档 - 进行上下对照翻译，返回翻译后的Word文档
                translated_doc_bytes = await TranslationService._extract_docx_text(content, translation_settings)
                # 对于Word文档，我们直接返回翻译后的文档，不需要再次翻译
                return translated_doc_bytes
            elif ext == 'pdf':
                # PDF文件 - 这里需要实现PDF解析
                return "PDF文档内容提取功能待实现"
            else:
                # 默认按文本处理
                return content.decode('utf-8', errors='ignore')

        except Exception as e:
            logger.error(f"文本提取失败: {e}")
            raise Exception(f"无法提取文件文本内容: {e}")

    @staticmethod
    async def _extract_docx_text(content: bytes, translation_settings=None, user=None) -> bytes:
        """在原Word文档中插入译文，保持原有格式（模仿VBA宏逻辑）"""
        try:
            import io
            from docx import Document
            from docx.shared import Pt
            from docx.shared import RGBColor
            from docx.enum.text import WD_ALIGN_PARAGRAPH

            # 设置翻译格式 - 优先级：传入参数 > 明道云用户设置 > 本地用户设置 > 默认设置
            if translation_settings is None or not isinstance(translation_settings, dict):
                # 尝试从明道云用户设置中获取（如果有user_rowid）
                if hasattr(user, 'mingdao_rowid') and user.mingdao_rowid:
                    try:
                        from app.services.mingdao_service import mingdao_service
                        mingdao_settings = await mingdao_service.get_user_translation_settings(user.mingdao_rowid)
                        if mingdao_settings:
                            translation_settings = mingdao_settings
                            logger.info(f"使用明道云用户 {user.mingdao_rowid} 的翻译设置")
                    except Exception as e:
                        logger.warning(f"获取明道云用户翻译设置失败: {e}")

                # 尝试从本地用户设置中获取
                if translation_settings is None and user is not None:
                    try:
                        translation_settings = user.get_translation_format_settings()
                        logger.info(f"使用本地用户 {user.username} 的翻译设置")
                    except Exception as e:
                        logger.warning(f"获取本地用户翻译设置失败: {e}")

                # 如果仍然没有设置，使用默认设置
                if translation_settings is None:
                    logger.info("使用默认翻译设置")
                    # 使用默认设置
                    default_paragraph = {
                        'font_family': 'Arial', 'font_size': 10.5, 'bold': False, 'italic': False,
                        'underline': False, 'text_align': 'inherit', 'color': '#000000'
                    }
                    default_table = {
                        'font_family': 'Arial', 'font_size': 6, 'bold': False, 'italic': False,
                        'underline': False, 'text_align': 'inherit', 'color': '#000000'
                    }
                    default_header = {
                        'font_family': 'Arial', 'font_size': 10.5, 'bold': False, 'italic': False,
                        'underline': False, 'text_align': 'inherit', 'color': '#000000'
                    }
                    translation_settings = {
                        'paragraph': default_paragraph,
                        'table': default_table,
                        'header': default_header,
                        'enable_paragraph': True,
                        'enable_table': True,
                        'enable_header': True
                    }
            else:
                logger.info(f"使用传入的翻译设置: {translation_settings}")

            # 确保所有必需的键都存在
            if 'enable_paragraph' not in translation_settings:
                translation_settings['enable_paragraph'] = True
            if 'enable_table' not in translation_settings:
                translation_settings['enable_table'] = True
            if 'enable_header' not in translation_settings:
                translation_settings['enable_header'] = True

            # 将bytes内容转换为文件对象
            doc_stream = io.BytesIO(content)
            doc = Document(doc_stream)

            logger.info(f"开始处理Word文档，段落数: {len(doc.paragraphs)}, 表格数: {len(doc.tables)}")
            logger.info(f"翻译设置 - 段落: {translation_settings.get('enable_paragraph')}, 表格: {translation_settings.get('enable_table')}, 页眉: {translation_settings.get('enable_header')}")

            from app.services.azure_translator import azure_translator

            # 提前从translation_settings中读取格式参数
            style_config = translation_settings
            body_font_name = style_config.get('paragraph', {}).get('font_family', 'Arial')
            body_font_size_pt = style_config.get('paragraph', {}).get('font_size', 10.5)
            table_font_name = style_config.get('table', {}).get('font_family', 'Arial')
            table_font_size_pt = style_config.get('table', {}).get('font_size', 6)
            header_font_name = style_config.get('header', {}).get('font_family', 'Arial')
            header_font_size_pt = style_config.get('header', {}).get('font_size', 10.5)

            logger.info(f"格式参数 - 正文: {body_font_name} {body_font_size_pt}pt, 表格: {table_font_name} {table_font_size_pt}pt, 页眉: {header_font_name} {header_font_size_pt}pt")

            # 封装翻译结果提取逻辑
            def extract_translation(translation_results):
                """提取翻译结果"""
                if translation_results and len(translation_results) > 0:
                    trans = translation_results[0].get("translations", [])
                    if len(trans) > 0:
                        return trans[0].get("text", "[Translation failed]")
                return "[Translation failed]"

            # 封装单元格是否已翻译的函数
            def is_translated_cell(cell):
                """检查单元格是否已经翻译过"""
                for para in cell.paragraphs[1:]:
                    para_text = para.text.strip()
                    if para_text and any(c.isascii() and c.isalpha() for c in para_text):
                        return True
                return False

            # 格式参数动态传入的统一处理函数
            def apply_translation_style(paragraph, font_name, font_size_pt, format_settings=None):
                """统一样式设置函数，支持格式动态配置"""
                # 继承原段落的缩进
                if hasattr(paragraph, '_element') and paragraph._element.getparent() is not None:
                    try:
                        # 尝试获取前一个段落的格式
                        parent = paragraph._element.getparent()
                        prev_para = None
                        for i, child in enumerate(parent):
                            if child == paragraph._element and i > 0:
                                prev_para = parent[i-1]
                                break

                        if prev_para is not None and hasattr(prev_para, 'pPr'):
                            # 继承缩进格式
                            if hasattr(prev_para.pPr, 'ind'):
                                paragraph.paragraph_format.left_indent = prev_para.pPr.ind.left
                    except:
                        pass

                # 应用字体格式
                for run in paragraph.runs:
                    run.font.name = font_name
                    run.font.size = Pt(font_size_pt)

                    # 应用其他格式设置
                    if format_settings:
                        run.font.bold = format_settings.get('bold', False)
                        run.font.italic = format_settings.get('italic', False)
                        run.font.underline = format_settings.get('underline', False)

                        # 设置颜色
                        color_hex = format_settings.get('color', '#000000')
                        if color_hex.startswith('#'):
                            color_hex = color_hex[1:]
                        try:
                            r = int(color_hex[0:2], 16)
                            g = int(color_hex[2:4], 16)
                            b = int(color_hex[4:6], 16)
                            run.font.color.rgb = RGBColor(r, g, b)
                        except:
                            pass

            # 格式应用辅助函数
            def apply_format_to_run(run, format_settings):
                """将格式设置应用到文本运行"""
                run.font.name = format_settings.get('font_family', 'Arial')
                run.font.size = Pt(format_settings.get('font_size', 10.5))
                run.font.bold = format_settings.get('bold', False)
                run.font.italic = format_settings.get('italic', False)
                run.font.underline = format_settings.get('underline', False)

                # 设置颜色
                color_hex = format_settings.get('color', '#000000')
                if color_hex.startswith('#'):
                    color_hex = color_hex[1:]
                try:
                    r = int(color_hex[0:2], 16)
                    g = int(color_hex[2:4], 16)
                    b = int(color_hex[4:6], 16)
                    run.font.color.rgb = RGBColor(r, g, b)
                except:
                    pass  # 如果颜色格式错误，使用默认颜色

            def apply_paragraph_format(paragraph, format_settings, original_paragraph=None):
                """将段落格式设置应用到段落，同时继承原段落的格式"""
                # 如果有原段落，先继承其格式
                if original_paragraph:
                    try:
                        # 继承对齐方式
                        if original_paragraph.alignment is not None:
                            paragraph.alignment = original_paragraph.alignment
                            logger.debug(f"继承对齐方式: {original_paragraph.alignment}")

                        # 继承首行缩进
                        if original_paragraph.paragraph_format.first_line_indent is not None:
                            paragraph.paragraph_format.first_line_indent = original_paragraph.paragraph_format.first_line_indent
                            logger.debug(f"继承首行缩进: {original_paragraph.paragraph_format.first_line_indent}")

                        # 继承左缩进
                        if original_paragraph.paragraph_format.left_indent is not None:
                            paragraph.paragraph_format.left_indent = original_paragraph.paragraph_format.left_indent
                            logger.debug(f"继承左缩进: {original_paragraph.paragraph_format.left_indent}")

                        # 继承右缩进
                        if original_paragraph.paragraph_format.right_indent is not None:
                            paragraph.paragraph_format.right_indent = original_paragraph.paragraph_format.right_indent

                        # 继承段前间距
                        if original_paragraph.paragraph_format.space_before is not None:
                            paragraph.paragraph_format.space_before = original_paragraph.paragraph_format.space_before

                        # 继承段后间距
                        if original_paragraph.paragraph_format.space_after is not None:
                            paragraph.paragraph_format.space_after = original_paragraph.paragraph_format.space_after

                        # 继承行间距
                        if original_paragraph.paragraph_format.line_spacing is not None:
                            paragraph.paragraph_format.line_spacing = original_paragraph.paragraph_format.line_spacing
                    except Exception as e:
                        logger.warning(f"继承段落格式失败: {e}")
                        pass  # 如果继承失败，使用默认格式

                # 然后应用用户设置（如果不是inherit）
                text_align = format_settings.get('text_align', 'inherit')
                if text_align != 'inherit':
                    if text_align == 'left':
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                    elif text_align == 'center':
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    elif text_align == 'right':
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                    elif text_align == 'justify':
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY

                # 用户设置的首行缩进（覆盖继承的）
                text_indent = format_settings.get('text_indent', 0)
                if text_indent > 0:
                    paragraph.paragraph_format.first_line_indent = Pt(text_indent * 12)  # 1字符约12pt

                # 用户设置的段前间距（覆盖继承的）
                margin_top = format_settings.get('margin_top', 0)
                if margin_top > 0:
                    paragraph.paragraph_format.space_before = Pt(margin_top)

            # 处理段落 - 模仿VBA宏的逻辑
            paragraph_count = 0

            # 检查是否启用段落翻译
            enable_paragraph = translation_settings.get('enable_paragraph', True)
            logger.info(f"段落翻译设置: {enable_paragraph}")
            if not enable_paragraph:
                logger.info("段落翻译已禁用，跳过段落处理")
            else:
                # 获取所有段落，但要动态处理以避免重复翻译
                paragraphs_list = list(doc.paragraphs)
                logger.info(f"总共找到 {len(paragraphs_list)} 个段落")

            i = 0
            while i < len(paragraphs_list):
                paragraph = paragraphs_list[i]
                original_text = paragraph.text.strip()

                logger.info(f"检查段落 {i}: '{original_text[:30]}...' (长度: {len(original_text)})")

                # 跳过空段落
                if len(original_text) <= 1:
                    logger.info(f"跳过空段落 {i}")
                    i += 1
                    continue

                # 跳过已经是翻译的段落（以[开头的）
                if original_text.startswith('['):
                    logger.info(f"跳过翻译标记段落 {i}")
                    i += 1
                    continue

                # 检查是否已经是译文段落（更精确的检测）
                is_translation = False
                # 检查段落是否有特定的译文标识，而不仅仅是Arial字体
                # 因为原文档可能本身就使用Arial字体
                for run in paragraph.runs:
                    # 更严格的检测：Arial字体 + 特定字号（10.5或6）
                    if (run.font.name and 'Arial' in run.font.name and
                        run.font.size and (run.font.size.pt == 10.5 or run.font.size.pt == 6)):
                        is_translation = True
                        break

                if is_translation:
                    logger.info(f"跳过译文段落 {i} (Arial字体+特定字号)")
                    i += 1
                    continue

                # 检查是否在表格中（表格中的段落单独处理）
                # 注释掉这个检查，因为它可能过于严格
                # if paragraph._element.getparent().tag.endswith('tc'):  # tc = table cell
                #     logger.info(f"跳过表格段落 {i}")
                #     i += 1
                #     continue

                paragraph_count += 1
                logger.info(f"处理段落 {paragraph_count}: {original_text[:50]}...")

                # 翻译文本
                try:
                    translation_results = await azure_translator.translate_text(
                        [original_text], 'en', 'zh'
                    )
                    translated_text = extract_translation(translation_results)

                except Exception as e:
                    logger.error(f"段落翻译失败: {e}")
                    translated_text = f'[Translation error: {str(e)}]'

                # 在原段落后插入译文段落
                para_element = paragraph._element
                parent = para_element.getparent()
                para_index = list(parent).index(para_element)

                # 创建新的译文段落
                new_para = doc.add_paragraph()
                new_para.text = translated_text

                # 应用用户设置的段落格式，同时继承原段落格式
                paragraph_settings = translation_settings.get('paragraph', {})
                apply_paragraph_format(new_para, paragraph_settings, paragraph)
                apply_translation_style(new_para, body_font_name, body_font_size_pt, paragraph_settings)

                # 移动新段落到正确位置
                new_para_element = new_para._element
                parent.remove(new_para_element)
                parent.insert(para_index + 1, new_para_element)

                logger.info(f"段落 {paragraph_count} 翻译完成: {translated_text[:50]}...")

                # 更新段落列表，跳过刚插入的译文段落
                paragraphs_list = list(doc.paragraphs)
                i += 2  # 跳过原段落和刚插入的译文段落

            # 处理表格（模仿VBA宏的表格处理）
            table_count = 0

            # 检查是否启用表格翻译
            enable_table = translation_settings.get('enable_table', True)
            logger.info(f"表格翻译设置: {enable_table}")
            if not enable_table:
                logger.info("表格翻译已禁用，跳过表格处理")
            else:
                for table in doc.tables:
                    table_count += 1
                    logger.info(f"处理表格 {table_count}")

                    for row_idx, row in enumerate(table.rows):
                        for col_idx, cell in enumerate(row.cells):
                            # 使用新的单元格翻译检测函数
                            if is_translated_cell(cell):
                                logger.info(f"表格单元格 [{row_idx},{col_idx}] 已有译文，跳过")
                                continue

                            # 获取原始文本（只从第一个段落获取，避免重复）
                            if len(cell.paragraphs) > 0:
                                original_text = cell.paragraphs[0].text.strip()
                            else:
                                original_text = cell.text.strip()

                            original_text = original_text.replace('\x07', '').replace('\r', '').replace('\n', '')  # 清除特殊字符

                            if len(original_text) > 1 and not original_text.startswith('['):
                                logger.info(f"翻译表格单元格 [{row_idx},{col_idx}]: {original_text[:30]}...")

                                # 翻译单元格文本
                                try:
                                    translation_results = await azure_translator.translate_text(
                                        [original_text], 'en', 'zh'
                                    )
                                    translated_text = extract_translation(translation_results)

                                except Exception as e:
                                    logger.error(f"表格单元格翻译失败: {e}")
                                    translated_text = f'[Translation error: {str(e)}]'

                                # 在原单元格内容后添加译文段落
                                translated_para = cell.add_paragraph()
                                translated_para.text = translated_text

                                # 应用表格样式，同时继承原单元格第一个段落的格式
                                table_settings = translation_settings.get('table', {})
                                original_para = cell.paragraphs[0] if len(cell.paragraphs) > 0 else None
                                apply_paragraph_format(translated_para, table_settings, original_para)
                                apply_translation_style(translated_para, table_font_name, table_font_size_pt, table_settings)

                                logger.info(f"表格单元格 [{row_idx},{col_idx}] 翻译完成: {translated_text[:30]}...")

            # 处理页眉（模仿VBA宏的页眉处理）
            header_paragraph_count = 0

            # 检查是否启用页眉翻译
            enable_header = translation_settings.get('enable_header', True)
            logger.info(f"页眉翻译设置: {enable_header}")
            if not enable_header:
                logger.info("页眉翻译已禁用，跳过页眉处理")
            else:
                for section in doc.sections:
                    header = section.header
                    if header:
                        logger.info(f"处理页眉，段落数: {len(header.paragraphs)}")

                    # 获取页眉段落列表
                    header_paragraphs = list(header.paragraphs)

                    i = 0
                    while i < len(header_paragraphs):
                        paragraph = header_paragraphs[i]
                        original_text = paragraph.text.strip()

                        # 跳过空段落
                        if len(original_text) <= 1:
                            i += 1
                            continue

                        # 跳过已经是翻译的段落
                        if original_text.startswith('['):
                            i += 1
                            continue

                        # 检查是否已经是译文段落（更精确的检测）
                        is_translation = False
                        for run in paragraph.runs:
                            # 更严格的检测：Arial字体 + 10.5字号（页眉译文的特征）
                            if (run.font.name and 'Arial' in run.font.name and
                                run.font.size and run.font.size.pt == 10.5):
                                is_translation = True
                                break

                        if is_translation:
                            i += 1
                            continue

                        # 检查是否在表格中
                        # 注释掉这个检查，因为它可能过于严格
                        # if paragraph._element.getparent().tag.endswith('tc'):
                        #     i += 1
                        #     continue

                        header_paragraph_count += 1
                        logger.info(f"处理页眉段落 {header_paragraph_count}: {original_text[:50]}...")

                        # 翻译页眉文本
                        try:
                            translation_results = await azure_translator.translate_text(
                                [original_text], 'en', 'zh'
                            )

                            translated_text = extract_translation(translation_results)

                        except Exception as e:
                            logger.error(f"页眉段落翻译失败: {e}")
                            translated_text = f'[Translation error: {str(e)}]'

                        # 在页眉段落后插入译文
                        para_element = paragraph._element
                        parent = para_element.getparent()
                        para_index = list(parent).index(para_element)

                        # 创建新的译文段落
                        new_para = header.add_paragraph()
                        new_para.text = translated_text

                        # 应用用户设置的页眉格式，同时继承原段落格式
                        header_settings = translation_settings.get('header', {})
                        apply_paragraph_format(new_para, header_settings, paragraph)
                        apply_translation_style(new_para, header_font_name, header_font_size_pt, header_settings)

                        # 移动新段落到正确位置
                        new_para_element = new_para._element
                        parent.remove(new_para_element)
                        parent.insert(para_index + 1, new_para_element)

                        logger.info(f"页眉段落 {header_paragraph_count} 翻译完成: {translated_text[:50]}...")

                        # 更新段落列表，跳过刚插入的译文段落
                        header_paragraphs = list(header.paragraphs)
                        i += 2  # 跳过原段落和刚插入的译文段落

                    # 处理页眉中的表格
                    for table_idx, table in enumerate(header.tables):
                        logger.info(f"处理页眉表格 {table_idx + 1}，行数: {len(table.rows)}，列数: {len(table.rows[0].cells) if len(table.rows) > 0 else 0}")

                        for row_idx, row in enumerate(table.rows):
                            for col_idx, cell in enumerate(row.cells):
                                # 使用新的单元格翻译检测函数
                                if is_translated_cell(cell):
                                    logger.info(f"页眉表格单元格 [{row_idx},{col_idx}] 已有译文，跳过")
                                    continue

                                # 获取原始文本
                                if len(cell.paragraphs) > 0:
                                    original_text = cell.paragraphs[0].text.strip()
                                else:
                                    original_text = cell.text.strip()

                                original_text = original_text.replace('\x07', '').replace('\r', '').replace('\n', '')

                                if len(original_text) > 1 and not original_text.startswith('['):
                                    logger.info(f"翻译页眉表格单元格 [{row_idx},{col_idx}]: {original_text[:30]}...")
                                    # 翻译页眉表格单元格文本
                                    try:
                                        translation_results = await azure_translator.translate_text(
                                            [original_text], 'en', 'zh'
                                        )

                                        translated_text = extract_translation(translation_results)

                                    except Exception as e:
                                        logger.error(f"页眉表格单元格翻译失败: {e}")
                                        translated_text = f'[Translation error: {str(e)}]'

                                    # 在原单元格内容后添加译文段落
                                    translated_para = cell.add_paragraph()
                                    translated_para.text = translated_text

                                    # 应用用户设置的页眉表格格式（使用header设置），继承原单元格格式
                                    header_settings = translation_settings.get('header', {})
                                    original_para = cell.paragraphs[0] if len(cell.paragraphs) > 0 else None
                                    apply_paragraph_format(translated_para, header_settings, original_para)
                                    apply_translation_style(translated_para, header_font_name, header_font_size_pt, header_settings)

                                    logger.info(f"页眉表格单元格 [{row_idx},{col_idx}] 翻译完成: {translated_text[:30]}...")

            # 保存修改后的文档到内存
            output_stream = io.BytesIO()
            doc.save(output_stream)
            output_stream.seek(0)

            logger.info(f"Word文档翻译完成，处理了 {paragraph_count} 个段落，{table_count} 个表格，{header_paragraph_count} 个页眉段落")

            # 返回修改后的文档内容（bytes格式）
            return output_stream.getvalue()

        except ImportError:
            logger.error("python-docx库未安装，无法解析Word文档")
            raise Exception("需要安装python-docx库来解析Word文档")
        except Exception as e:
            logger.error(f"Word文档翻译失败: {e}")
            raise Exception(f"Word文档翻译失败: {str(e)}")

    @staticmethod
    def _generate_translated_filename(original_filename: str, source_lang: str, target_lang: str) -> str:
        """生成翻译后的文件名"""
        name_without_ext = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
        ext = original_filename.rsplit('.', 1)[1] if '.' in original_filename else 'txt'

        return f"{name_without_ext}_translated_{source_lang}_to_{target_lang}.{ext}"
