"""
翻译设置API - 明道云集成
"""
from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any
import logging
from pydantic import BaseModel

from app.services.mingdao_service import mingdao_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/translation-settings", tags=["翻译设置"])


class TranslationSettings(BaseModel):
    """翻译设置模型"""
    paragraph: Dict[str, Any] = {
        "font_family": "Arial",
        "font_size": 10.5,
        "bold": False,
        "italic": False,
        "underline": False,
        "text_align": "inherit",
        "color": "#000000"
    }
    table: Dict[str, Any] = {
        "font_family": "Arial", 
        "font_size": 6,
        "bold": False,
        "italic": False,
        "underline": False,
        "text_align": "inherit",
        "color": "#000000"
    }
    header: Dict[str, Any] = {
        "font_family": "Arial",
        "font_size": 10.5,
        "bold": False,
        "italic": False,
        "underline": False,
        "text_align": "inherit",
        "color": "#000000"
    }
    enable_paragraph: bool = True
    enable_table: bool = True
    enable_header: bool = True


@router.get("/{user_rowid}")
async def get_user_translation_settings(user_rowid: str):
    """获取用户的翻译格式设置"""
    try:
        settings = await mingdao_service.get_user_translation_settings(user_rowid)
        
        if settings:
            logger.info(f"获取用户 {user_rowid} 翻译设置成功")
            return {
                "success": True,
                "data": settings,
                "message": "获取翻译设置成功"
            }
        else:
            # 返回默认设置
            default_settings = TranslationSettings().dict()
            return {
                "success": True,
                "data": default_settings,
                "message": "用户暂无自定义设置，返回默认设置"
            }
    except Exception as e:
        logger.error(f"获取翻译设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取翻译设置失败"
        )


@router.post("/{user_rowid}")
async def save_user_translation_settings(user_rowid: str, settings: TranslationSettings, owner_id: str = "2499c06b-cecc-484d-ae58-16271bfa70ce"):
    """保存用户的翻译格式设置"""
    try:
        # 转换为字典
        settings_dict = settings.dict()

        # 保存到明道云
        success = await mingdao_service.save_user_translation_settings(user_rowid, settings_dict, owner_id)
        
        if success:
            logger.info(f"用户 {user_rowid} 翻译设置保存成功")
            return {
                "success": True,
                "data": settings_dict,
                "message": "翻译设置保存成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="保存翻译设置失败"
            )
    except Exception as e:
        logger.error(f"保存翻译设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="保存翻译设置失败"
        )


@router.delete("/{user_rowid}")
async def reset_user_translation_settings(user_rowid: str, owner_id: str = "2499c06b-cecc-484d-ae58-16271bfa70ce"):
    """重置用户的翻译设置为默认值"""
    try:
        # 保存默认设置
        default_settings = TranslationSettings().dict()
        success = await mingdao_service.save_user_translation_settings(user_rowid, default_settings, owner_id)
        
        if success:
            logger.info(f"用户 {user_rowid} 翻译设置重置成功")
            return {
                "success": True,
                "data": default_settings,
                "message": "翻译设置已重置为默认值"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="重置翻译设置失败"
            )
    except Exception as e:
        logger.error(f"重置翻译设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置翻译设置失败"
        )
