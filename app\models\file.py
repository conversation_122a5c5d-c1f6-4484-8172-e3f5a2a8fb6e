"""
文件相关模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, BigInteger, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.db.database import Base


class FileStatus(str, enum.Enum):
    """文件状态枚举"""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    ERROR = "error"


class UploadedFile(Base):
    """上传文件模型"""
    __tablename__ = "uploaded_files"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 文件信息
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger, nullable=False)
    mime_type = Column(String(100), nullable=False)
    
    # 文件状态
    status = Column(Enum(FileStatus), default=FileStatus.UPLOADED)
    error_message = Column(Text, nullable=True)
    
    # 文档内容信息
    total_paragraphs = Column(Integer, default=0)
    total_characters = Column(Integer, default=0)
    extracted_text = Column(Text, nullable=True)  # 提取的文本内容
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="uploaded_files")
    translation_histories = relationship("TranslationHistory", back_populates="file")
    
    def __repr__(self):
        return f"<UploadedFile(id={self.id}, filename='{self.filename}', status='{self.status}')>"
