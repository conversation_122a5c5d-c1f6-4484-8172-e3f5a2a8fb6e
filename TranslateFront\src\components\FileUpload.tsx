import React, { useCallback, useState } from 'react';
import { Upload, X, File, AlertCircle } from 'lucide-react';
import { UploadFile } from '../types/document';

interface FileUploadProps {
  onFilesSelected: (files: UploadFile[]) => void;
  onUpload: (files: UploadFile[], sourceLanguage: string, targetLanguage: string) => Promise<void>;
  maxFileSize?: number; // MB
  acceptedTypes?: string[];
  multiple?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFilesSelected,
  onUpload,
  maxFileSize = 50,
  acceptedTypes = ['.doc', '.docx', '.pdf', '.txt', '.xlsx', '.pptx'],
  multiple = true
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [sourceLanguage, setSourceLanguage] = useState('zh');
  const [targetLanguage, setTargetLanguage] = useState('en');

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const validateFile = (file: File): string | null => {
    // 检查文件大小
    if (file.size > maxFileSize * 1024 * 1024) {
      return `文件大小不能超过 ${maxFileSize}MB`;
    }

    // 检查文件类型
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedTypes.includes(fileExtension)) {
      return `不支持的文件类型，支持的类型: ${acceptedTypes.join(', ')}`;
    }

    return null;
  };

  const processFiles = (files: FileList) => {
    const newFiles: UploadFile[] = [];
    
    Array.from(files).forEach((file) => {
      const error = validateFile(file);
      const uploadFile: UploadFile = {
        file,
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        progress: 0,
        status: error ? 'error' : 'pending',
        error: error || undefined
      };
      newFiles.push(uploadFile);
    });

    setUploadFiles(prev => [...prev, ...newFiles]);
    onFilesSelected(newFiles);
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processFiles(e.dataTransfer.files);
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      processFiles(e.target.files);
    }
  };

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(file => file.id !== id));
  };

  const handleUpload = async () => {
    const filesToUpload = uploadFiles.filter(file => file.status === 'pending');
    if (filesToUpload.length === 0) return;

    setIsUploading(true);
    try {
      await onUpload(filesToUpload, sourceLanguage, targetLanguage);
      setUploadFiles([]);
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 语言选项
  const languageOptions = [
    { code: 'zh', name: '中文' },
    { code: 'en', name: 'English' },
    { code: 'ja', name: '日语' },
    { code: 'ko', name: '韩语' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
    { code: 'es', name: 'Español' }
  ];

  return (
    <div className="file-upload">
      {/* 翻译设置 */}
      <div className="translation-settings">
        <h3>翻译设置</h3>
        <div className="language-selector">
          <div className="language-group">
            <label htmlFor="source-language">源语言:</label>
            <select
              id="source-language"
              value={sourceLanguage}
              onChange={(e) => setSourceLanguage(e.target.value)}
              className="language-select"
            >
              {languageOptions.map(lang => (
                <option key={lang.code} value={lang.code}>{lang.name}</option>
              ))}
            </select>
          </div>
          <div className="language-arrow">→</div>
          <div className="language-group">
            <label htmlFor="target-language">目标语言:</label>
            <select
              id="target-language"
              value={targetLanguage}
              onChange={(e) => setTargetLanguage(e.target.value)}
              className="language-select"
            >
              {languageOptions.map(lang => (
                <option key={lang.code} value={lang.code}>{lang.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* 拖拽上传区域 */}
      <div
        className={`upload-zone ${dragActive ? 'drag-active' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => document.getElementById('file-input')?.click()}
      >
        <Upload size={48} className="upload-icon" />
        <p className="upload-text">
          拖拽文件到此处或 <span className="upload-link">点击选择文件</span>
        </p>
        <p className="upload-hint">
          支持 {acceptedTypes.join(', ')} 格式，最大 {maxFileSize}MB
        </p>
        <input
          id="file-input"
          type="file"
          multiple={multiple}
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          style={{ display: 'none' }}
        />
      </div>

      {/* 文件列表 */}
      {uploadFiles.length > 0 && (
        <div className="file-list">
          <h4>待上传文件</h4>
          {uploadFiles.map((file) => (
            <div key={file.id} className={`file-item ${file.status}`}>
              <div className="file-info">
                <File size={20} />
                <div className="file-details">
                  <span className="file-name">{file.name}</span>
                  <span className="file-size">{formatFileSize(file.size)}</span>
                </div>
              </div>
              <div className="file-actions">
                {file.status === 'error' && (
                  <div className="error-message">
                    <AlertCircle size={16} />
                    <span>{file.error}</span>
                  </div>
                )}
                <button
                  className="btn-remove"
                  onClick={() => removeFile(file.id)}
                  disabled={isUploading}
                >
                  <X size={16} />
                </button>
              </div>
            </div>
          ))}
          
          {uploadFiles.some(file => file.status === 'pending') && (
            <button
              className="btn btn-primary upload-btn"
              onClick={handleUpload}
              disabled={isUploading}
            >
              {isUploading ? '上传中...' : '开始上传'}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
