// 注意：jsonwebtoken 在浏览器环境中不可用，我们需要使用浏览器兼容的方案
import { PREVIEW_CONFIG } from '../config/api.config';

// 安全的 Base64 编码函数（支持 UTF-8）
const base64UrlEncode = (str: string): string => {
  // 先将字符串转换为 UTF-8 字节
  const utf8Bytes = new TextEncoder().encode(str);
  // 将字节数组转换为二进制字符串
  const binaryString = String.fromCharCode(...utf8Bytes);
  // 进行 Base64 编码
  return btoa(binaryString)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

// 简单的 HMAC-SHA256 实现（使用 Web Crypto API）
const hmacSha256 = async (message: string, secret: string): Promise<string> => {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(secret);
  const messageData = encoder.encode(message);

  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
  const signatureArray = new Uint8Array(signature);

  // 将字节数组转换为 Base64URL
  const binaryString = String.fromCharCode(...signatureArray);
  return btoa(binaryString)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

/**
 * 生成 OnlyOffice JWT 令牌（浏览器兼容版本）
 * @param payload JWT 载荷数据
 * @returns 签名后的 JWT 令牌
 */
export const generateOnlyOfficeJWT = async (payload: any): Promise<string> => {
  if (!PREVIEW_CONFIG.onlyoffice.jwt.enabled) {
    return '';
  }

  const secret = PREVIEW_CONFIG.onlyoffice.jwt.secret;

  // JWT 头部
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  // 添加过期时间（1小时后）
  const now = Math.floor(Date.now() / 1000);
  const exp = now + 3600; // 1小时

  const payloadWithExp = {
    ...payload,
    iat: now,
    exp: exp
  };

  // 编码头部和载荷
  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payloadWithExp));

  // 创建签名字符串
  const signatureInput = `${encodedHeader}.${encodedPayload}`;

  // 生成签名
  const signature = await hmacSha256(signatureInput, secret);

  // 返回完整的 JWT
  return `${encodedHeader}.${encodedPayload}.${signature}`;
};

/**
 * 为 OnlyOffice 配置生成带 JWT 的完整配置
 * @param config OnlyOffice 配置对象
 * @returns 带 JWT 签名的配置对象
 */
export const signOnlyOfficeConfig = async (config: any): Promise<any> => {
  if (!PREVIEW_CONFIG.onlyoffice.jwt.enabled) {
    return config;
  }

  // 提取事件回调（不参与 JWT 签名）
  const { events, ...configForSigning } = config;

  // 生成 JWT 令牌（不包含事件回调）
  const token = await generateOnlyOfficeJWT(configForSigning);

  // 返回带 token 的完整配置（包含事件回调）
  return {
    ...config,
    token: token
  };
};

/**
 * 验证 JWT 令牌（用于调试）
 * @param token JWT 令牌
 * @returns 解码后的载荷或 null
 */
export const verifyOnlyOfficeJWT = (token: string): any | null => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    // 解码载荷部分
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
    return payload;
  } catch (error) {
    console.error('JWT 验证失败:', error);
    return null;
  }
};
