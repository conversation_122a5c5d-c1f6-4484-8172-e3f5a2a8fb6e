"""
直接测试 Azure 翻译 API
"""
import asyncio
import httpx
import json
import uuid

# 您的 Azure 配置
API_KEY = "FpyApWXGNX8MvH59fvH57g89cNNFT0lMble8NkiEGnhoRNNHd1t5JQQJ99BGAC3pKaRXJ3w3AAAbACOGWVpY"
REGION = "eastasia"
ENDPOINT = "https://api.cognitive.microsofttranslator.com"

async def test_azure_translator():
    """测试 Azure 翻译 API"""
    print("🔍 测试 Azure 翻译 API")
    print("=" * 50)
    
    # 准备请求头
    headers = {
        'Ocp-Apim-Subscription-Key': API_KEY,
        'Ocp-Apim-Subscription-Region': REGION,
        'Content-type': 'application/json',
        'X-ClientTraceId': str(uuid.uuid4())
    }
    
    async with httpx.AsyncClient() as client:
        
        # 1. 测试获取支持的语言
        print("1. 测试获取支持的语言...")
        try:
            response = await client.get(
                f"{ENDPOINT}/translator/text/v3.0/languages",
                params={'api-version': '3.0', 'scope': 'translation'}
            )
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                languages = response.json()
                translation_langs = languages.get('translation', {})
                print(f"✅ 支持的语言数量: {len(translation_langs)}")
                
                # 显示一些常用语言
                common_langs = ['zh', 'en', 'ja', 'ko', 'fr', 'de', 'es']
                for lang in common_langs:
                    if lang in translation_langs:
                        lang_info = translation_langs[lang]
                        print(f"   - {lang}: {lang_info.get('name', 'Unknown')}")
            else:
                print(f"❌ 获取语言失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 获取语言错误: {e}")
        
        print()
        
        # 2. 测试语言检测
        print("2. 测试语言检测...")
        try:
            test_text = "这是一个测试文本"
            body = [{'text': test_text}]
            
            response = await client.post(
                f"{ENDPOINT}/translator/text/v3.0/detect",
                headers=headers,
                json=body,
                params={'api-version': '3.0'}
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0:
                    detected = result[0]
                    print(f"✅ 检测到语言: {detected.get('language')}")
                    print(f"   置信度: {detected.get('score', 0):.2f}")
                else:
                    print("❌ 检测结果为空")
            else:
                print(f"❌ 语言检测失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 语言检测错误: {e}")
        
        print()
        
        # 3. 测试翻译功能
        print("3. 测试翻译功能...")
        try:
            test_texts = [
                "这是一个测试文档。",
                "人工智能技术正在快速发展。",
                "机器学习算法在各个领域都有应用。"
            ]
            
            body = [{'Text': text} for text in test_texts]
            
            response = await client.post(
                f"{ENDPOINT}/translator/text/v3.0/translate",
                headers=headers,
                json=body,
                params={
                    'api-version': '3.0',
                    'from': 'zh',
                    'to': 'en'
                }
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ 翻译成功，结果数量: {len(results)}")
                
                for i, (original, result) in enumerate(zip(test_texts, results)):
                    if 'translations' in result and len(result['translations']) > 0:
                        translated = result['translations'][0]['text']
                        print(f"   {i+1}. 原文: {original}")
                        print(f"      译文: {translated}")
                    else:
                        print(f"   {i+1}. 翻译失败: {original}")
                    print()
            else:
                print(f"❌ 翻译失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 翻译错误: {e}")
        
        print()
        
        # 4. 测试不同的端点格式
        print("4. 测试不同的端点格式...")
        
        # 尝试不同的 URL 格式
        test_urls = [
            f"{ENDPOINT}/translator/text/v3.0/languages",
            f"https://api.cognitive.microsofttranslator.com/translator/text/v3.0/languages",
            f"https://{REGION}.api.cognitive.microsofttranslator.com/translator/text/v3.0/languages"
        ]
        
        for url in test_urls:
            try:
                response = await client.get(
                    url,
                    params={'api-version': '3.0'},
                    headers={'Ocp-Apim-Subscription-Key': API_KEY}
                )
                print(f"   {url}")
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✅ 成功")
                    break
                else:
                    print(f"   ❌ 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ 错误: {e}")
        
        print()
        print("🎯 测试总结:")
        print("   - 检查 API 密钥是否有效")
        print("   - 检查区域设置是否正确")
        print("   - 检查网络连接")
        print("   - 检查 Azure 服务状态")

if __name__ == "__main__":
    asyncio.run(test_azure_translator())
