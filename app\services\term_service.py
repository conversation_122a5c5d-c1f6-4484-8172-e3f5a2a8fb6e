"""
术语管理服务
"""
import csv
import io
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from fastapi import HTTPException, status, UploadFile

from app.models.term import Term, TermCategory, TermImportHistory
from app.models.user import User
from app.schemas.term import (
    TermCreate, TermUpdate, TermCategoryCreate, TermSearchRequest,
    TermImportRequest
)
from app.utils.logger import logger


class TermService:
    """术语管理服务类"""
    
    @staticmethod
    async def create_category(
        db: AsyncSession,
        category_data: TermCategoryCreate,
        user: User
    ) -> TermCategory:
        """创建术语分类"""
        # 检查分类名称是否已存在
        result = await db.execute(
            select(TermCategory).where(TermCategory.name == category_data.name)
        )
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Category name already exists"
            )
        
        category = TermCategory(
            name=category_data.name,
            description=category_data.description
        )
        
        db.add(category)
        await db.commit()
        await db.refresh(category)
        
        logger.info(f"Term category created: {category.name} by user {user.username}")
        return category
    
    @staticmethod
    async def get_categories(db: AsyncSession) -> List[TermCategory]:
        """获取所有术语分类"""
        result = await db.execute(
            select(TermCategory).order_by(TermCategory.name)
        )
        return result.scalars().all()
    
    @staticmethod
    async def create_term(
        db: AsyncSession,
        term_data: TermCreate,
        user: User
    ) -> Term:
        """创建术语"""
        # 检查术语是否已存在
        result = await db.execute(
            select(Term).where(
                and_(
                    Term.source_term == term_data.source_term,
                    Term.source_language == term_data.source_language,
                    Term.target_language == term_data.target_language
                )
            )
        )
        existing_term = result.scalar_one_or_none()
        
        if existing_term:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Term already exists for this language pair"
            )
        
        term = Term(
            category_id=term_data.category_id,
            source_term=term_data.source_term,
            target_term=term_data.target_term,
            source_language=term_data.source_language,
            target_language=term_data.target_language,
            definition=term_data.definition,
            context=term_data.context,
            notes=term_data.notes,
            confidence_level=term_data.confidence_level,
            created_by=user.username
        )
        
        db.add(term)
        await db.commit()
        await db.refresh(term)
        
        logger.info(f"Term created: {term.source_term} -> {term.target_term} by user {user.username}")
        return term
    
    @staticmethod
    async def search_terms(
        db: AsyncSession,
        search_request: TermSearchRequest,
        skip: int = 0,
        limit: int = 20
    ) -> Tuple[List[Term], int]:
        """搜索术语"""
        query = select(Term)
        
        # 构建查询条件
        conditions = []
        
        if search_request.query:
            conditions.append(
                or_(
                    Term.source_term.ilike(f"%{search_request.query}%"),
                    Term.target_term.ilike(f"%{search_request.query}%"),
                    Term.definition.ilike(f"%{search_request.query}%")
                )
            )
        
        if search_request.source_language:
            conditions.append(Term.source_language == search_request.source_language)
        
        if search_request.target_language:
            conditions.append(Term.target_language == search_request.target_language)
        
        if search_request.category_id:
            conditions.append(Term.category_id == search_request.category_id)
        
        if search_request.is_active is not None:
            conditions.append(Term.is_active == search_request.is_active)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count(Term.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        count_result = await db.execute(count_query)
        total = count_result.scalar()
        
        # 获取分页数据
        query = query.order_by(Term.usage_count.desc(), Term.created_at.desc())
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        terms = result.scalars().all()
        
        return terms, total
    
    @staticmethod
    async def get_term_by_id(db: AsyncSession, term_id: int) -> Optional[Term]:
        """根据ID获取术语"""
        result = await db.execute(select(Term).where(Term.id == term_id))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_term(
        db: AsyncSession,
        term_id: int,
        term_update: TermUpdate,
        user: User
    ) -> Term:
        """更新术语"""
        term = await TermService.get_term_by_id(db, term_id)
        
        if not term:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Term not found"
            )
        
        # 更新字段
        if term_update.category_id is not None:
            term.category_id = term_update.category_id
        if term_update.target_term is not None:
            term.target_term = term_update.target_term
        if term_update.definition is not None:
            term.definition = term_update.definition
        if term_update.context is not None:
            term.context = term_update.context
        if term_update.notes is not None:
            term.notes = term_update.notes
        if term_update.confidence_level is not None:
            term.confidence_level = term_update.confidence_level
        if term_update.is_active is not None:
            term.is_active = term_update.is_active
        
        await db.commit()
        await db.refresh(term)
        
        logger.info(f"Term updated: {term.id} by user {user.username}")
        return term
    
    @staticmethod
    async def delete_term(db: AsyncSession, term_id: int, user: User) -> bool:
        """删除术语"""
        term = await TermService.get_term_by_id(db, term_id)
        
        if not term:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Term not found"
            )
        
        await db.delete(term)
        await db.commit()
        
        logger.info(f"Term deleted: {term_id} by user {user.username}")
        return True
    
    @staticmethod
    async def import_terms_from_csv(
        db: AsyncSession,
        file: UploadFile,
        import_request: TermImportRequest,
        user: User
    ) -> TermImportHistory:
        """从CSV文件导入术语"""
        # 创建导入历史记录
        import_history = TermImportHistory(
            filename=file.filename,
            imported_by=user.username,
            status="pending"
        )
        
        db.add(import_history)
        await db.commit()
        await db.refresh(import_history)
        
        try:
            # 读取CSV文件
            content = await file.read()
            csv_content = content.decode('utf-8')
            csv_reader = csv.DictReader(io.StringIO(csv_content))
            
            imported_count = 0
            failed_count = 0
            total_count = 0
            
            for row in csv_reader:
                total_count += 1
                
                try:
                    # 期望的CSV格式：source_term, target_term, definition, context, notes
                    source_term = row.get('source_term', '').strip()
                    target_term = row.get('target_term', '').strip()
                    
                    if not source_term or not target_term:
                        failed_count += 1
                        continue
                    
                    # 检查术语是否已存在
                    existing_result = await db.execute(
                        select(Term).where(
                            and_(
                                Term.source_term == source_term,
                                Term.source_language == import_request.source_language,
                                Term.target_language == import_request.target_language
                            )
                        )
                    )
                    existing_term = existing_result.scalar_one_or_none()
                    
                    if existing_term and not import_request.overwrite_existing:
                        failed_count += 1
                        continue
                    
                    if existing_term and import_request.overwrite_existing:
                        # 更新现有术语
                        existing_term.target_term = target_term
                        existing_term.definition = row.get('definition', '').strip() or None
                        existing_term.context = row.get('context', '').strip() or None
                        existing_term.notes = row.get('notes', '').strip() or None
                        existing_term.category_id = import_request.category_id
                    else:
                        # 创建新术语
                        new_term = Term(
                            category_id=import_request.category_id,
                            source_term=source_term,
                            target_term=target_term,
                            source_language=import_request.source_language,
                            target_language=import_request.target_language,
                            definition=row.get('definition', '').strip() or None,
                            context=row.get('context', '').strip() or None,
                            notes=row.get('notes', '').strip() or None,
                            created_by=user.username
                        )
                        db.add(new_term)
                    
                    imported_count += 1
                    
                except Exception as e:
                    logger.error(f"Error importing term row: {e}")
                    failed_count += 1
            
            # 更新导入历史
            import_history.total_terms = total_count
            import_history.imported_terms = imported_count
            import_history.failed_terms = failed_count
            import_history.status = "completed"
            
            await db.commit()
            
            logger.info(f"Terms imported: {imported_count}/{total_count} by user {user.username}")
            return import_history
            
        except Exception as e:
            logger.error(f"Error importing terms: {e}")
            import_history.status = "failed"
            import_history.error_message = str(e)
            await db.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to import terms"
            )
    
    @staticmethod
    async def find_term_translation(
        db: AsyncSession,
        source_term: str,
        source_language: str,
        target_language: str
    ) -> Optional[Term]:
        """查找术语翻译"""
        result = await db.execute(
            select(Term).where(
                and_(
                    Term.source_term == source_term,
                    Term.source_language == source_language,
                    Term.target_language == target_language,
                    Term.is_active == True
                )
            ).order_by(Term.confidence_level.desc(), Term.usage_count.desc())
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def increment_term_usage(db: AsyncSession, term: Term):
        """增加术语使用次数"""
        term.increment_usage()
        await db.commit()
