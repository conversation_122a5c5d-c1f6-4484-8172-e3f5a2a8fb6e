@echo off
echo 🚀 启动翻译系统后端服务...
echo ================================

REM 激活虚拟环境
call .venv\Scripts\activate.bat

REM 安装缺失的依赖
echo 📦 检查并安装依赖...
pip install email-validator

REM 启动后端服务
echo 🌐 启动FastAPI服务器...
echo 服务地址: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo 按 Ctrl+C 停止服务器
echo ================================

python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

pause
