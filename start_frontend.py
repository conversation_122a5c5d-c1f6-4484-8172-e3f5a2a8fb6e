"""
单独启动前端服务的脚本
"""
import subprocess
import sys
import os
import webbrowser
import time

def start_frontend_server():
    """启动前端HTTP服务器"""
    frontend_dir = os.path.join(os.getcwd(), "frontend")
    
    if not os.path.exists(frontend_dir):
        print("❌ frontend 目录不存在")
        return False
    
    print("🌐 启动前端服务...")
    print(f"📁 前端目录: {frontend_dir}")
    print("🌐 访问地址: http://localhost:3000")
    print("=" * 50)
    
    try:
        # 启动HTTP服务器
        subprocess.run([
            sys.executable, "-m", "http.server", "3000"
        ], cwd=frontend_dir)
        
    except KeyboardInterrupt:
        print("\n🛑 前端服务已停止")
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 启动前端服务...")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        try:
            webbrowser.open("http://localhost:3000")
        except:
            pass
    
    import threading
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    start_frontend_server()
