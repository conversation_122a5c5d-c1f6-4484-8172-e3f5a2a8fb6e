"""
用户模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.db.database import Base


class UserRole(str, enum.Enum):
    """用户角色枚举"""
    USER = "user"
    ADMIN = "admin"
    MODERATOR = "moderator"


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    
    # 用户状态
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    role = Column(Enum(UserRole), default=UserRole.USER)
    
    # 配额管理
    translation_quota = Column(Integer, default=1000)  # 翻译配额
    used_quota = Column(Integer, default=0)  # 已使用配额
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # 用户偏好设置
    preferred_source_lang = Column(String(10), default="en")
    preferred_target_lang = Column(String(10), default="zh")

    # 翻译格式设置（JSON格式存储）
    translation_format_settings = Column(Text, nullable=True)  # 存储JSON格式的翻译设置
    
    # 关联关系
    translation_histories = relationship("TranslationHistory", back_populates="user")
    uploaded_files = relationship("UploadedFile", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"
    
    @property
    def remaining_quota(self) -> int:
        """剩余配额"""
        return max(0, self.translation_quota - self.used_quota)
    
    def can_translate(self, estimated_chars: int = 1) -> bool:
        """检查是否可以进行翻译"""
        return self.is_active and self.remaining_quota >= estimated_chars

    def get_translation_format_settings(self) -> dict:
        """获取用户的翻译格式设置"""
        if self.translation_format_settings:
            try:
                import json
                return json.loads(self.translation_format_settings)
            except (json.JSONDecodeError, TypeError):
                pass

        # 返回默认设置
        return {
            "paragraph": {
                "font_family": "Arial",
                "font_size": 10.5,
                "bold": False,
                "italic": False,
                "underline": False,
                "text_align": "inherit",
                "color": "#000000"
            },
            "table": {
                "font_family": "Arial",
                "font_size": 6,
                "bold": False,
                "italic": False,
                "underline": False,
                "text_align": "inherit",
                "color": "#000000"
            },
            "header": {
                "font_family": "Arial",
                "font_size": 10.5,
                "bold": False,
                "italic": False,
                "underline": False,
                "text_align": "inherit",
                "color": "#000000"
            },
            "enable_paragraph": True,
            "enable_table": True,
            "enable_header": True
        }

    def set_translation_format_settings(self, settings: dict) -> None:
        """设置用户的翻译格式设置"""
        import json
        self.translation_format_settings = json.dumps(settings, ensure_ascii=False)
