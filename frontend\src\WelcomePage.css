/* 欢迎页面样式 */
.welcome-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #eff6ff, #f0f9ff, #faf5ff);
  position: relative;
}

.welcome-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 顶部导航 */
.welcome-nav {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  padding: 16px 24px;
  position: relative;
  z-index: 10;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.brand-text h1 {
  font-size: 20px;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
}

.brand-text p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.nav-login-btn {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-login-btn:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

/* 主要内容区域 */
.welcome-main {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 80px);
  padding: 24px;
  position: relative;
  z-index: 1;
}

.welcome-content {
  max-width: 1000px;
  text-align: center;
}

/* 主标题区域 */
.welcome-hero {
  margin-bottom: 80px;
}

.hero-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: white;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
}

.hero-title {
  font-size: 48px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16px;
  line-height: 1.2;
}

.title-highlight {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 20px;
  color: #6b7280;
  margin-bottom: 32px;
  line-height: 1.6;
}

.hero-cta-btn {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 16px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.hero-cta-btn:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
}

/* 功能特色 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-bottom: 80px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.feature-card-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
}

.feature-card-icon.blue {
  background: #dbeafe;
  color: #3b82f6;
}

.feature-card-icon.purple {
  background: #f3e8ff;
  color: #8b5cf6;
}

.feature-card-icon.indigo {
  background: #e0e7ff;
  color: #6366f1;
}

.feature-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.feature-card p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

/* 底部提示 */
.welcome-footer {
  text-align: center;
}

.welcome-footer p {
  color: #9ca3af;
  margin-bottom: 16px;
}

.footer-login-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s;
  font-size: 16px;
}

.footer-login-btn:hover {
  color: #1d4ed8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 16px;
  }
  
  .brand-text h1 {
    font-size: 18px;
  }
  
  .brand-text p {
    font-size: 12px;
  }
  
  .nav-login-btn {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .welcome-main {
    padding: 16px;
  }
  
  .hero-title {
    font-size: 32px;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .hero-cta-btn {
    font-size: 16px;
    padding: 14px 24px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .feature-card {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .hero-icon {
    width: 60px;
    height: 60px;
  }
  
  .hero-title {
    font-size: 28px;
  }
  
  .hero-subtitle {
    font-size: 16px;
  }
}
