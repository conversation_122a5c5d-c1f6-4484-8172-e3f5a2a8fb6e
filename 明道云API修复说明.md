# 🔧 明道云API修复说明

## 🚨 **问题分析**

### **错误信息**
```
明道云API请求异常: 0, message='Attempt to decode JSON with unexpected mimetype: ', url=URL('https://dmit.duoningbio.com/api/v2/open/worksheet/getRowDetail')
获取记录详情异常: 0, message='Attempt to decode JSON with unexpected mimetype: ', url=URL('https://dmit.duoningbio.com/api/v2/open/worksheet/getRowDetail')
```

### **问题原因**
1. **API端点错误** - 使用了错误的API端点`/worksheet/getRowDetail`
2. **响应格式问题** - 明道云API返回的不是标准JSON格式
3. **参数缺失** - 缺少必要的参数如`getSystemControl`

## ✅ **修复内容**

### **1. 更正API端点**

#### **修复前**
```python
result = await self._make_request("/worksheet/getRowDetail", data)
```

#### **修复后**
```python
result = await self._make_request("/worksheet/getRowByIdPost", data)
```

### **2. 完善请求参数**

#### **修复前**
```python
data = {
    "worksheetId": worksheet_id,
    "rowId": row_id
}
```

#### **修复后**
```python
data = {
    "worksheetId": worksheet_id,
    "rowId": row_id,
    "getSystemControl": True  # 获取系统字段
}
```

### **3. 增强响应处理**

#### **修复前**
```python
result = await response.json()
```

#### **修复后**
```python
# 先获取响应文本
response_text = await response.text()
logger.info(f"明道云API响应内容: {response_text[:500]}...")

# 尝试解析JSON
try:
    result = json.loads(response_text) if response_text else {}
    return result
except json.JSONDecodeError as e:
    logger.error(f"JSON解析失败: {e}")
    return {"success": False, "error": "响应不是有效的JSON格式", "raw_response": response_text}
```

## 🎯 **正确的API调用格式**

### **API端点**
```
POST: https://dmit.duoningbio.com/api/v2/open/worksheet/getRowByIdPost
```

### **请求参数**
```json
{
  "appKey": "d88c1d2329c42504",
  "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
  "worksheetId": "6886f053a849420e13f69b61",
  "rowId": "行记录ID",
  "getSystemControl": true
}
```

### **响应格式**
```json
{
  "success": true,
  "data": {
    "rowid": "记录ID",
    "6886f7a4a849420e13f69b6f": "原文件字段数据",
    "6886f7a4a849420e13f69b70": "翻译后文件字段数据",
    "6886f7a4a849420e13f69b71": "翻译状态字段数据",
    // ... 其他字段
  }
}
```

## 🔧 **调试功能增强**

### **详细日志记录**
现在API调用会记录：
```
明道云API响应状态: 200
明道云API响应头: {'content-type': 'application/json', ...}
明道云API响应内容: {"success":true,"data":{...}}...
```

### **错误处理改进**
- ✅ HTTP状态码检查
- ✅ JSON解析错误处理
- ✅ 响应内容日志记录
- ✅ 详细错误信息

## 🚀 **后端服务状态**

### **重启成功**
```
INFO: Started server process [311472]
INFO: Application startup complete.
INFO: Uvicorn running on http://0.0.0.0:8000
```

### **数据库初始化**
```
INFO: Starting Translation Service v1.0.0
INFO: Database tables created successfully
```

## 📊 **测试验证**

### **1. API端点测试**
访问：`http://localhost:8000/docs`
测试：`POST /api/v1/translate/process`

### **2. 翻译流程测试**
1. 前端上传文件
2. 点击"开始翻译"
3. 观察后端日志：

#### **期望看到的日志**
```
INFO: Processing translation request for row_id: xxx, zh -> en
INFO: 开始处理明道云翻译: row_id=xxx, zh->en
INFO: 明道云API响应状态: 200
INFO: 明道云API响应内容: {"success":true,"data":{...}}
INFO: 翻译完成: row_id=xxx, 字符数=1000
```

#### **如果仍有问题**
```
ERROR: 明道云API请求失败: HTTP 400
ERROR: JSON解析失败: Expecting value: line 1 column 1 (char 0)
```

## 🔍 **故障排除**

### **如果API调用失败**
1. **检查网络连接** - 确认能访问明道云API
2. **检查API密钥** - 确认appKey和sign正确
3. **检查工作表ID** - 确认worksheetId正确
4. **检查记录ID** - 确认rowId存在

### **如果JSON解析失败**
1. **查看响应内容** - 检查raw_response字段
2. **检查Content-Type** - 确认响应头正确
3. **检查API版本** - 确认使用正确的API版本

### **如果记录不存在**
1. **检查rowId格式** - 确认是有效的UUID格式
2. **检查权限** - 确认API有访问该记录的权限
3. **检查工作表** - 确认记录在正确的工作表中

## 🎯 **完整翻译流程**

### **修复后的流程**
```
1. 接收翻译请求 → row_id, source_lang, target_lang
2. 调用正确API → /worksheet/getRowByIdPost
3. 获取记录详情 → 包含原文件信息
4. 下载文件内容 → 从明道云文件URL
5. 提取文本内容 → 根据文件类型
6. 执行翻译处理 → Azure翻译API
7. 上传翻译结果 → 更新明道云记录
8. 更新翻译状态 → 完成或失败
```

### **错误恢复机制**
- ✅ API调用失败自动重试
- ✅ 翻译失败状态更新
- ✅ 详细错误日志记录
- ✅ 用户友好错误信息

## 🚀 **立即测试**

现在请：

1. **测试API调用**：
   - 访问 `http://localhost:8000/docs`
   - 测试 `POST /api/v1/translate/process`

2. **测试前端翻译**：
   - 登录前端系统
   - 上传文件并开始翻译
   - 观察后端日志输出

3. **验证数据流转**：
   - 检查明道云记录状态
   - 确认翻译文件生成
   - 验证状态更新正确

### **预期结果**
- ✅ 不再出现JSON解析错误
- ✅ 成功获取明道云记录详情
- ✅ 完整的翻译处理流程
- ✅ 正确的状态更新

现在明道云API调用应该可以正常工作了！🎊
