# 🔧 服务器启动问题解决方案

## 🚨 **问题描述**

服务器在热重载过程中遇到了数据库连接问题，导致启动失败：

```
asyncio.exceptions.CancelledError
```

这通常是由于热重载过程中异步操作被中断导致的。

## ✅ **解决方案**

### **方案1：手动重启服务器**

1. **打开命令行**
   ```bash
   cd d:\mywork\Translate
   ```

2. **激活虚拟环境**
   ```bash
   .venv\Scripts\activate
   ```

3. **启动服务器（不使用热重载）**
   ```bash
   python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

### **方案2：使用启动脚本**

我已经创建了`start_server.py`脚本，直接运行：
```bash
python start_server.py
```

### **方案3：检查代码问题**

如果服务器仍然无法启动，可能是我们的代码修改有问题。检查步骤：

1. **测试导入**
   ```bash
   python test_import.py
   ```

2. **检查语法错误**
   ```bash
   python -m py_compile app/schemas/translation.py
   python -m py_compile app/services/translation_service.py
   python -m py_compile app/routes/translate.py
   ```

## 🔧 **代码修改回滚（如果需要）**

如果我们的修改导致了问题，可以临时回滚关键修改：

### **1. 简化translation_service.py**

将`_extract_docx_text`函数的签名改回原来的：
```python
@staticmethod
async def _extract_docx_text(content: bytes) -> bytes:
```

### **2. 简化API接口**

暂时移除翻译设置参数：
```python
@router.post("/process")
async def process_translation(request: dict, db: AsyncSession = Depends(get_async_db)):
    row_id = request.get('row_id')
    source_language = request.get('source_language', 'zh')
    target_language = request.get('target_language', 'en')
    # translation_settings = request.get('translation_settings')  # 暂时注释
    
    result = await TranslationService.process_mingdao_translation(
        db, row_id, source_language, target_language  # , translation_settings
    )
```

## 🎯 **推荐解决步骤**

### **步骤1：重启服务器**
```bash
# 1. 进入项目目录
cd d:\mywork\Translate

# 2. 激活虚拟环境
.venv\Scripts\activate

# 3. 启动服务器
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### **步骤2：测试基本功能**
- 访问 http://localhost:8000/docs
- 检查API文档是否正常显示
- 测试基本的翻译功能

### **步骤3：逐步启用新功能**
如果基本功能正常，再逐步启用翻译设置功能：

1. **先测试不带设置的翻译**
2. **再测试带设置的翻译**
3. **验证格式是否正确应用**

## 🚀 **快速启动命令**

### **Windows PowerShell**
```powershell
cd d:\mywork\Translate
.\.venv\Scripts\Activate.ps1
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### **Windows CMD**
```cmd
cd d:\mywork\Translate
.venv\Scripts\activate.bat
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## 🔍 **调试信息**

如果服务器仍然无法启动，请检查：

1. **Python环境**
   ```bash
   python --version
   pip list | grep uvicorn
   ```

2. **依赖包**
   ```bash
   pip install -r requirements.txt
   ```

3. **数据库文件**
   检查`data/`目录下的SQLite文件是否存在且可访问

4. **端口占用**
   ```bash
   netstat -ano | findstr :8000
   ```

## 💡 **临时解决方案**

如果急需使用翻译功能，可以：

1. **使用之前的版本** - 暂时不使用翻译设置功能
2. **手动重启** - 每次修改代码后手动重启服务器
3. **禁用热重载** - 使用`--reload=false`参数

## 🎊 **预期结果**

服务器正常启动后，应该看到：
```
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

然后就可以测试翻译设置功能了！
