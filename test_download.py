"""
测试下载功能
"""
import asyncio
import httpx
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_download():
    """测试下载翻译文档"""
    print("🔍 测试下载翻译文档功能")
    print("=" * 50)
    
    # 测试用户登录
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    async with httpx.AsyncClient() as client:
        # 1. 登录获取令牌
        print("1. 用户登录...")
        try:
            response = await client.post("http://localhost:8000/api/v1/auth/login", json=login_data)
            if response.status_code == 200:
                token = response.json()["access_token"]
                print("✅ 登录成功")
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return
        
        # 2. 获取翻译历史
        print("\n2. 获取翻译历史...")
        headers = {"Authorization": f"Bearer {token}"}
        
        try:
            response = await client.get("http://localhost:8000/api/v1/translate/", headers=headers)
            if response.status_code == 200:
                translations = response.json()["translations"]
                print(f"✅ 找到 {len(translations)} 个翻译记录")
                
                # 显示所有翻译状态
                print("   翻译记录:")
                for t in translations:
                    print(f"     ID: {t['id']}, 状态: {t['status']}")

                # 找到已完成的翻译
                completed_translations = [t for t in translations if t["status"] == "COMPLETED"]
                if completed_translations:
                    translation_id = completed_translations[0]["id"]
                    print(f"📝 使用翻译 ID: {translation_id}")
                else:
                    # 如果没有 COMPLETED，尝试使用第一个翻译
                    if translations:
                        translation_id = translations[0]["id"]
                        print(f"📝 使用第一个翻译 ID: {translation_id} (状态: {translations[0]['status']})")
                    else:
                        print("❌ 没有找到任何翻译")
                        return
            else:
                print(f"❌ 获取翻译历史失败: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 获取翻译历史错误: {e}")
            return
        
        # 3. 测试下载
        print(f"\n3. 下载翻译文档 (ID: {translation_id})...")
        try:
            response = await client.get(
                f"http://localhost:8000/api/v1/translate/{translation_id}/download",
                headers=headers
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                # 保存文件
                content = response.content.decode('utf-8')
                filename = f"downloaded_translation_{translation_id}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 下载成功！文件保存为: {filename}")
                print(f"📊 文件大小: {len(content)} 字符")
                
                # 显示文件内容预览
                print("\n📖 文件内容预览:")
                print("-" * 40)
                print(content[:300])
                if len(content) > 300:
                    print("...")
                print("-" * 40)
                
            else:
                print(f"❌ 下载失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 下载错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_download())
