"""
测试Word文档下载功能
"""
import asyncio
import httpx
import os

async def test_docx_download():
    """测试Word文档下载功能"""
    print("🔍 测试Word文档下载功能")
    print("=" * 50)
    
    # 测试用户登录
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    async with httpx.AsyncClient() as client:
        # 1. 登录获取令牌
        print("1. 用户登录...")
        try:
            response = await client.post("http://localhost:8000/api/v1/auth/login", json=login_data)
            if response.status_code == 200:
                token = response.json()["access_token"]
                print("✅ 登录成功")
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return
        
        # 2. 获取翻译历史
        print("\n2. 获取翻译历史...")
        headers = {"Authorization": f"Bearer {token}"}
        
        try:
            response = await client.get("http://localhost:8000/api/v1/translate/", headers=headers)
            if response.status_code == 200:
                data = response.json()
                translations = data.get("translations", [])
                print(f"✅ 找到 {len(translations)} 个翻译记录")
                
                # 找到已完成的翻译
                completed_translations = [t for t in translations if t['status'] == 'completed']
                if completed_translations:
                    translation_id = completed_translations[0]['id']
                    print(f"📝 使用翻译 ID: {translation_id}")
                else:
                    print("❌ 没有找到已完成的翻译")
                    return
            else:
                print(f"❌ 获取翻译历史失败: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 获取翻译历史错误: {e}")
            return
        
        # 3. 测试文本下载
        print(f"\n3. 测试文本下载 (ID: {translation_id})...")
        try:
            response = await client.get(
                f"http://localhost:8000/api/v1/translate/{translation_id}/download",
                headers=headers
            )
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                filename = f"test_text_download_{translation_id}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 文本下载成功！文件保存为: {filename}")
                print(f"   文件大小: {len(content)} 字符")
            else:
                print(f"❌ 文本下载失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 文本下载错误: {e}")
        
        # 4. 测试Word文档下载
        print(f"\n4. 测试Word文档下载 (ID: {translation_id})...")
        try:
            response = await client.get(
                f"http://localhost:8000/api/v1/translate/{translation_id}/download/docx",
                headers=headers
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                # 保存Word文档
                filename = f"test_docx_download_{translation_id}.docx"
                
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ Word文档下载成功！文件保存为: {filename}")
                print(f"   文件大小: {len(response.content)} 字节")
                
                # 检查文件是否存在
                if os.path.exists(filename):
                    file_size = os.path.getsize(filename)
                    print(f"   确认文件存在，大小: {file_size} 字节")
                    
                    # 尝试用python-docx验证文件
                    try:
                        from docx import Document
                        doc = Document(filename)
                        paragraph_count = len(doc.paragraphs)
                        print(f"   Word文档验证成功，包含 {paragraph_count} 个段落")
                        
                        # 显示前几个段落的内容
                        print("\n📖 Word文档内容预览:")
                        for i, para in enumerate(doc.paragraphs[:5]):
                            if para.text.strip():
                                print(f"   段落 {i+1}: {para.text[:100]}...")
                        
                    except Exception as e:
                        print(f"   ⚠️  Word文档验证失败: {e}")
                        
                else:
                    print("❌ 文件保存失败")
                    
            elif response.status_code == 400:
                print(f"❌ Word文档下载失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                print("   可能原因: 只支持Word文档格式的双语下载")
            else:
                print(f"❌ Word文档下载失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ Word文档下载错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n🎯 测试总结:")
    print("   - 翻译历史记录获取")
    print("   - 文本格式下载测试")
    print("   - Word格式下载测试")
    print("   - 双语文档生成验证")

if __name__ == "__main__":
    asyncio.run(test_docx_download())
