"""
测试文件上传和翻译功能
"""
import asyncio
import httpx
import json
from pathlib import Path

BASE_URL = "http://localhost:8000"

async def test_upload_and_translate():
    """测试完整的上传和翻译流程"""
    async with httpx.AsyncClient() as client:
        print("🚀 测试文件上传和翻译功能")
        print("=" * 50)
        
        # 1. 用户登录获取令牌
        print("1. 用户登录...")
        login_data = {
            "username": "testuser",
            "password": "testpass123"
        }
        
        try:
            response = await client.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data["access_token"]
                print(f"✅ 登录成功，获得令牌: {access_token[:20]}...")
                
                # 设置认证头
                headers = {"Authorization": f"Bearer {access_token}"}
                
            else:
                print(f"❌ 登录失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return
        
        print()
        
        # 2. 创建测试文档
        print("2. 创建测试文档...")
        test_doc_path = "test_document.txt"
        test_content = """这是一个测试文档。

第一段：人工智能技术正在快速发展。
第二段：机器学习算法在各个领域都有应用。
第三段：自然语言处理是AI的重要分支。

表格内容：
项目	描述	状态
AI研究	人工智能研究项目	进行中
ML算法	机器学习算法开发	已完成
NLP系统	自然语言处理系统	计划中
"""
        
        with open(test_doc_path, "w", encoding="utf-8") as f:
            f.write(test_content)
        print(f"✅ 测试文档已创建: {test_doc_path}")
        
        print()
        
        # 3. 测试文件上传
        print("3. 测试文件上传...")
        try:
            with open(test_doc_path, "rb") as f:
                files = {"file": (test_doc_path, f, "text/plain")}
                response = await client.post(
                    f"{BASE_URL}/api/v1/upload/",
                    headers=headers,
                    files=files
                )
            
            if response.status_code == 201:
                file_data = response.json()
                file_id = file_data["id"]
                print(f"✅ 文件上传成功")
                print(f"   文件ID: {file_id}")
                print(f"   文件名: {file_data['filename']}")
                print(f"   文件大小: {file_data['file_size']} 字节")
                
            else:
                print(f"❌ 文件上传失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return
                
        except Exception as e:
            print(f"❌ 文件上传错误: {e}")
            return
        
        print()
        
        # 4. 检查文件状态
        print("4. 检查文件处理状态...")
        try:
            response = await client.get(
                f"{BASE_URL}/api/v1/upload/{file_id}/status",
                headers=headers
            )
            
            if response.status_code == 200:
                status_data = response.json()
                print(f"✅ 文件状态: {status_data['status']}")
                print(f"   处理进度: {status_data['progress']:.1%}")
                
                if status_data['error_message']:
                    print(f"   错误信息: {status_data['error_message']}")
            else:
                print(f"❌ 获取文件状态失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 获取文件状态错误: {e}")
        
        print()
        
        # 5. 获取支持的语言（可能失败，因为需要Azure配置）
        print("5. 获取支持的语言...")
        try:
            response = await client.get(
                f"{BASE_URL}/api/v1/translate/languages",
                headers=headers
            )
            
            if response.status_code == 200:
                languages = response.json()
                print(f"✅ 支持的语言数量: {len(languages.get('languages', []))}")
            else:
                print(f"⚠️  获取支持语言失败: {response.status_code} (需要Azure配置)")
                
        except Exception as e:
            print(f"⚠️  获取支持语言跳过: {e}")
        
        print()
        
        # 6. 创建翻译任务（可能失败，因为需要Azure配置）
        print("6. 创建翻译任务...")
        try:
            translate_data = {
                "file_id": file_id,
                "source_language": "zh",
                "target_language": "en",
                "include_original": True
            }
            
            response = await client.post(
                f"{BASE_URL}/api/v1/translate/",
                headers=headers,
                json=translate_data
            )
            
            if response.status_code == 201:
                translation_data = response.json()
                translation_id = translation_data["id"]
                print(f"✅ 翻译任务创建成功")
                print(f"   翻译ID: {translation_id}")
                print(f"   状态: {translation_data['status']}")
                print(f"   进度: {translation_data['progress']:.1%}")
                
                # 等待一会儿再检查翻译进度
                print("\n   等待翻译处理...")
                await asyncio.sleep(3)
                
                # 检查翻译进度
                progress_response = await client.get(
                    f"{BASE_URL}/api/v1/translate/{translation_id}/progress",
                    headers=headers
                )
                
                if progress_response.status_code == 200:
                    progress_data = progress_response.json()
                    print(f"   翻译状态: {progress_data['status']}")
                    print(f"   当前步骤: {progress_data['current_step']}")
                    print(f"   进度: {progress_data['progress']:.1%}")
                    
                    if progress_data.get('error_message'):
                        print(f"   错误信息: {progress_data['error_message']}")
                
            else:
                print(f"❌ 创建翻译任务失败: {response.status_code}")
                print(f"   错误: {response.text}")
                
        except Exception as e:
            print(f"❌ 创建翻译任务错误: {e}")
        
        print()
        
        # 7. 获取用户文件列表
        print("7. 获取用户文件列表...")
        try:
            response = await client.get(
                f"{BASE_URL}/api/v1/upload/",
                headers=headers
            )
            
            if response.status_code == 200:
                files_data = response.json()
                print(f"✅ 用户文件总数: {files_data['total']}")
                for file_info in files_data['files']:
                    print(f"   - {file_info['original_filename']} (ID: {file_info['id']}, 状态: {file_info['status']})")
            else:
                print(f"❌ 获取文件列表失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 获取文件列表错误: {e}")
        
        print()
        
        # 清理测试文件
        try:
            Path(test_doc_path).unlink()
            print(f"🧹 测试文件已清理: {test_doc_path}")
        except:
            pass
        
        print()
        print("🎉 测试完成!")
        print("=" * 50)
        print("📝 说明:")
        print("   - 文件上传功能正常")
        print("   - 翻译功能需要有效的Azure配置才能完全工作")
        print("   - 可以在 http://localhost:8000/docs 中手动测试")
        print("   - 使用Bearer令牌进行认证")

if __name__ == "__main__":
    asyncio.run(test_upload_and_translate())
