import React, { useState } from 'react';
import { User, Mail, Lock, Phone, UserPlus, Eye, EyeOff, CheckCircle, AlertCircle } from 'lucide-react';
import { MINGDAO_CONFIG } from '../config/api.config';
import './MingdaoRegister.css';

interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  phone: string;
}

interface MingdaoRegisterProps {
  onRegisterSuccess?: (userData: any) => void;
  onClose?: () => void;
}

const MingdaoRegister: React.FC<MingdaoRegisterProps> = ({ onRegisterSuccess, onClose }) => {
  const [formData, setFormData] = useState<RegisterFormData>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<RegisterFormData>>({});
  const [registerResult, setRegisterResult] = useState<{
    success: boolean;
    message: string;
    userId?: string;
  } | null>(null);

  // 表单验证
  const validateForm = (): boolean => {
    console.log('🔍 开始表单验证', formData);
    const newErrors: Partial<RegisterFormData> = {};

    // 用户名验证
    if (!formData.username.trim()) {
      newErrors.username = '用户名不能为空';
    } else if (formData.username.length < 3) {
      newErrors.username = '用户名至少3个字符';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = '用户名只能包含字母、数字和下划线';
    }

    // 邮箱验证
    if (!formData.email.trim()) {
      newErrors.email = '邮箱不能为空';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    // 密码验证
    if (!formData.password) {
      newErrors.password = '密码不能为空';
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少6个字符';
    }

    // 确认密码验证
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    // 手机号验证（可选）
    if (formData.phone && !/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入有效的手机号';
    }

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    console.log('📋 表单验证结果:', { isValid, errors: newErrors });
    return isValid;
  };

  // 处理输入变化
  const handleInputChange = (field: keyof RegisterFormData, value: string) => {
    console.log(`📝 输入变化: ${field} = ${value}`);
    setFormData(prev => ({ ...prev, [field]: value }));

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // 测试按钮点击
  const handleTestClick = () => {
    console.log('🧪 测试按钮被点击');
    alert('测试按钮工作正常！');
  };

  // 创建明道云用户记录
  const createMingdaoUser = async (): Promise<{ success: boolean; message: string; userId?: string }> => {
    try {
      console.log('📝 开始创建明道云用户记录');

      // 密码哈希（简单示例，生产环境应该在后端处理）
      const passwordHash = btoa(formData.password); // 简单base64编码，实际应该使用更安全的哈希
      const currentTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

      console.log('🔐 密码已编码，当前时间:', currentTime);

      const controls = [
        { controlId: MINGDAO_CONFIG.userFields.username, value: formData.username },
        { controlId: MINGDAO_CONFIG.userFields.email, value: formData.email },
        { controlId: MINGDAO_CONFIG.userFields.password_hash, value: passwordHash },
        { controlId: MINGDAO_CONFIG.userFields.full_name, value: formData.fullName },
        { controlId: MINGDAO_CONFIG.userFields.phone, value: formData.phone },
        { controlId: MINGDAO_CONFIG.userFields.user_type, value: 'b4e9a50e-e83a-4e1e-ac29-619572a67265' }, // 免费用户
        { controlId: MINGDAO_CONFIG.userFields.status, value: '0f7a48f5-9dc2-496d-96ca-c4a3b49e6d00' }, // 正常状态
        { controlId: MINGDAO_CONFIG.userFields.balance, value: 0 },
        { controlId: MINGDAO_CONFIG.userFields.total_quota, value: 1000 }, // 免费1000字符
        { controlId: MINGDAO_CONFIG.userFields.used_quota, value: 0 },
        { controlId: MINGDAO_CONFIG.userFields.monthly_quota, value: 0 },
        { controlId: MINGDAO_CONFIG.userFields.monthly_used, value: 0 },
        { controlId: MINGDAO_CONFIG.userFields.created_at, value: currentTime },
        { controlId: MINGDAO_CONFIG.userFields.updated_at, value: currentTime }
      ];

      const requestData = {
        appKey: MINGDAO_CONFIG.appKey,
        sign: MINGDAO_CONFIG.sign,
        worksheetId: MINGDAO_CONFIG.worksheets.users,
        triggerWorkflow: true,
        controls: controls
      };

      // 由于跨域问题，这里模拟API调用
      // 在实际应用中，应该通过后端代理调用明道云API
      console.log('📤 模拟创建明道云用户:', requestData);

      // 模拟API响应
      console.log('⏳ 开始模拟网络延迟...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟网络延迟
      console.log('✅ 网络延迟结束');
      
      // 模拟90%成功率
      if (Math.random() > 0.1) {
        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        return {
          success: true,
          message: '用户注册成功！已分配1000字符免费额度',
          userId: userId
        };
      } else {
        return {
          success: false,
          message: '注册失败：用户名或邮箱已存在'
        };
      }

    } catch (error) {
      console.error('创建用户失败:', error);
      return {
        success: false,
        message: `注册失败: ${error}`
      };
    }
  };

  // 处理注册提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🚀 注册表单提交开始', formData);

    // 简化验证 - 只检查必填字段
    if (!formData.username.trim()) {
      console.log('❌ 用户名为空');
      setErrors({ username: '用户名不能为空' });
      return;
    }

    if (!formData.email.trim()) {
      console.log('❌ 邮箱为空');
      setErrors({ email: '邮箱不能为空' });
      return;
    }

    if (!formData.password) {
      console.log('❌ 密码为空');
      setErrors({ password: '密码不能为空' });
      return;
    }

    console.log('✅ 简化验证通过，开始注册流程');
    setIsLoading(true);
    setRegisterResult(null);

    try {
      const result = await createMingdaoUser();
      setRegisterResult(result);

      if (result.success) {
        // 创建用户对象
        const userData = {
          id: result.userId,
          username: formData.username,
          email: formData.email,
          full_name: formData.fullName,
          phone: formData.phone,
          user_type: 'b4e9a50e-e83a-4e1e-ac29-619572a67265',
          status: '0f7a48f5-9dc2-496d-96ca-c4a3b49e6d00',
          balance: 0,
          total_quota: 1000,
          used_quota: 0,
          monthly_quota: 0,
          monthly_used: 0,
          created_at: new Date().toISOString()
        };

        onRegisterSuccess?.(userData);
        
        // 3秒后自动关闭
        setTimeout(() => {
          onClose?.();
        }, 3000);
      }
    } catch (error) {
      setRegisterResult({
        success: false,
        message: '注册过程中发生错误，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mingdao-register">
      <div className="register-header">
        <h2>
          <UserPlus size={24} />
          创建明道云账号
        </h2>
        <p>注册即可获得1000字符免费翻译额度</p>
      </div>

      {registerResult && (
        <div className={`register-result ${registerResult.success ? 'success' : 'error'}`}>
          {registerResult.success ? (
            <CheckCircle size={20} />
          ) : (
            <AlertCircle size={20} />
          )}
          <span>{registerResult.message}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="register-form">
        {/* 用户名 */}
        <div className="form-group">
          <label htmlFor="username">
            <User size={16} />
            用户名 *
          </label>
          <input
            id="username"
            type="text"
            value={formData.username}
            onChange={(e) => handleInputChange('username', e.target.value)}
            placeholder="请输入用户名"
            className={errors.username ? 'error' : ''}
            disabled={isLoading}
          />
          {errors.username && <span className="error-message">{errors.username}</span>}
        </div>

        {/* 邮箱 */}
        <div className="form-group">
          <label htmlFor="email">
            <Mail size={16} />
            邮箱 *
          </label>
          <input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="请输入邮箱地址"
            className={errors.email ? 'error' : ''}
            disabled={isLoading}
          />
          {errors.email && <span className="error-message">{errors.email}</span>}
        </div>

        {/* 密码 */}
        <div className="form-group">
          <label htmlFor="password">
            <Lock size={16} />
            密码 *
          </label>
          <div className="password-input">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="请输入密码（至少6位）"
              className={errors.password ? 'error' : ''}
              disabled={isLoading}
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {errors.password && <span className="error-message">{errors.password}</span>}
        </div>

        {/* 确认密码 */}
        <div className="form-group">
          <label htmlFor="confirmPassword">
            <Lock size={16} />
            确认密码 *
          </label>
          <div className="password-input">
            <input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              placeholder="请再次输入密码"
              className={errors.confirmPassword ? 'error' : ''}
              disabled={isLoading}
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              disabled={isLoading}
            >
              {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {errors.confirmPassword && <span className="error-message">{errors.confirmPassword}</span>}
        </div>

        {/* 姓名 */}
        <div className="form-group">
          <label htmlFor="fullName">
            <User size={16} />
            姓名
          </label>
          <input
            id="fullName"
            type="text"
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
            placeholder="请输入真实姓名（可选）"
            disabled={isLoading}
          />
        </div>

        {/* 手机号 */}
        <div className="form-group">
          <label htmlFor="phone">
            <Phone size={16} />
            手机号
          </label>
          <input
            id="phone"
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            placeholder="请输入手机号（可选）"
            className={errors.phone ? 'error' : ''}
            disabled={isLoading}
          />
          {errors.phone && <span className="error-message">{errors.phone}</span>}
        </div>

        {/* 测试按钮 */}
        <div style={{ marginBottom: '10px' }}>
          <button
            type="button"
            onClick={handleTestClick}
            style={{
              padding: '8px 16px',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            🧪 测试按钮
          </button>
        </div>

        {/* 提交按钮 */}
        <div className="form-actions">
          <button
            type="submit"
            className="register-btn"
            disabled={isLoading || registerResult?.success}
          >
            {isLoading ? (
              <>
                <div className="loading-spinner"></div>
                创建中...
              </>
            ) : registerResult?.success ? (
              <>
                <CheckCircle size={16} />
                注册成功
              </>
            ) : (
              <>
                <UserPlus size={16} />
                创建账号
              </>
            )}
          </button>

          {onClose && (
            <button
              type="button"
              className="cancel-btn"
              onClick={onClose}
              disabled={isLoading}
            >
              取消
            </button>
          )}
        </div>
      </form>

      {/* 注册说明 */}
      <div className="register-info">
        <h4>注册福利</h4>
        <ul>
          <li>🎁 免费获得1000字符翻译额度</li>
          <li>📊 详细的使用统计和配额管理</li>
          <li>💳 灵活的付费方式选择</li>
          <li>🔒 企业级数据安全保障</li>
        </ul>
      </div>
    </div>
  );
};

export default MingdaoRegister;
