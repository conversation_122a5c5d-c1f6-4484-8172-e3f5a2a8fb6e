# 🔧 过滤器参数更新说明

## 📋 **更新内容**

根据您提供的明道云API过滤器示例，我已经更新了用户过滤条件的参数。

### **更新前**
```typescript
const filters = [
  {
    controlId: MINGDAO_AUTH_CONFIG.translationFields.user,
    dataType: 29, // 人员字段类型
    spliceType: 1,
    filterType: 2, // 等于
    value: currentUser.id // 当前用户的rowid
  }
];
```

### **更新后**
```typescript
const filters = [
  {
    controlId: MINGDAO_AUTH_CONFIG.translationFields.user,
    dataType: 29, // 人员字段类型
    spliceType: 2,
    filterType: 11,
    value: currentUser.id // 当前用户的rowid
  }
];
```

## 🔍 **参数变化**

| 参数 | 更新前 | 更新后 | 说明 |
|------|--------|--------|------|
| `spliceType` | 1 | 2 | 连接方式 |
| `filterType` | 2 | 11 | 过滤类型 |
| `dataType` | 29 | 29 | 保持不变（人员字段） |
| `controlId` | user | user | 保持不变 |
| `value` | 用户rowid | 用户rowid | 保持不变 |

## 📊 **明道云过滤器参数说明**

### **spliceType（连接方式）**
- `1`: AND连接
- `2`: OR连接

### **filterType（过滤类型）**
根据明道云API文档，常见的filterType值：
- `2`: 等于
- `11`: 包含（可能用于人员字段的特殊匹配）
- 其他值根据字段类型和需求而定

### **dataType（字段类型）**
- `29`: 人员字段类型（保持不变）

## 🎯 **影响的函数**

### **1. getDocuments函数**
- 用于获取翻译文档列表
- 按当前用户过滤记录
- 确保用户只能看到自己的翻译

### **2. getDocumentsTotalNum函数**
- 用于获取翻译文档总数
- 按当前用户过滤计数
- 确保分页计算正确

## 🚀 **测试验证**

### **预期行为**
1. **用户登录后** - 只能看到自己的翻译记录
2. **文档列表** - 正确按用户过滤
3. **分页功能** - 总数计算正确
4. **数据隔离** - 不同用户的数据完全分离

### **测试步骤**
1. **刷新页面** - 确保使用最新的过滤器参数
2. **登录测试** - 使用测试账号登录
3. **查看列表** - 确认文档列表正确显示
4. **多用户测试** - 验证数据隔离效果

### **调试信息**
在浏览器控制台中，您应该看到类似的请求数据：
```json
{
  "appKey": "d88c1d2329c42504",
  "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
  "worksheetId": "6886f053a849420e13f69b61",
  "pageSize": 200,
  "pageIndex": 1,
  "listType": 0,
  "filters": [
    {
      "controlId": "6886f7a4a849420e13f69b74",
      "dataType": 29,
      "spliceType": 2,
      "filterType": 11,
      "value": "用户的rowid"
    }
  ]
}
```

## 🔧 **故障排除**

### **如果过滤不生效**
1. **检查用户登录状态** - 确保用户已正确登录
2. **检查用户ID** - 确认currentUser.id不为空
3. **检查明道云响应** - 查看API返回的数据
4. **检查字段ID** - 确认user字段ID正确

### **如果看到其他用户的数据**
1. **检查过滤器参数** - 确认spliceType和filterType正确
2. **检查API调用** - 确认filters参数正确传递
3. **检查明道云配置** - 确认字段权限设置

### **如果没有数据显示**
1. **检查用户ID格式** - 确认是正确的rowid格式
2. **检查过滤条件** - 可能过滤条件过于严格
3. **检查数据存在性** - 确认该用户确实有翻译记录

## 📱 **API请求示例**

### **完整的getFilterRows请求**
```json
{
  "appKey": "d88c1d2329c42504",
  "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
  "worksheetId": "6886f053a849420e13f69b61",
  "viewId": "",
  "pageSize": 200,
  "pageIndex": 1,
  "listType": 0,
  "filters": [
    {
      "controlId": "6886f7a4a849420e13f69b74",
      "dataType": 29,
      "spliceType": 2,
      "filterType": 11,
      "value": "e7fbfcfc-21ae-46a6-9fcf-6d031e4045fa"
    }
  ]
}
```

## 🎯 **预期结果**

更新后的过滤器应该：
- ✅ 正确按用户过滤翻译记录
- ✅ 确保数据隔离和安全性
- ✅ 提供准确的分页信息
- ✅ 符合明道云API规范

## 🚀 **立即测试**

现在请：
1. **刷新页面** - 确保使用最新的过滤器参数
2. **登录测试账号** - testuser / testpass123
3. **查看翻译列表** - 确认过滤器正常工作
4. **上传新文件** - 测试完整流程

如果有任何问题，请查看浏览器控制台的网络请求，确认过滤器参数是否正确发送。🔍
