"""Add translation format settings to users table

Revision ID: add_translation_format_settings
Revises: 
Create Date: 2025-01-29 18:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_translation_format_settings'
down_revision = None  # 请根据实际情况修改
branch_labels = None
depends_on = None


def upgrade():
    """添加翻译格式设置字段"""
    # 添加翻译格式设置字段
    op.add_column('users', sa.Column('translation_format_settings', sa.Text(), nullable=True))


def downgrade():
    """移除翻译格式设置字段"""
    op.drop_column('users', 'translation_format_settings')
