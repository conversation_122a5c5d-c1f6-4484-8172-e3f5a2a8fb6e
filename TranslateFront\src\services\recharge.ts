// 充值相关API服务
import { MINGDAO_AUTH_CONFIG, MINGDAO_DATA_TYPES, MINGDAO_FILTER_TYPES } from '../config/api.config';
import { RechargeRequest, RechargeRecord } from '../types/document';
import { authService } from './auth';

// API响应基础接口
interface RechargeApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error_code?: string;
}

// 充值服务类
export class RechargeService {
  private static instance: RechargeService;

  private constructor() {}

  public static getInstance(): RechargeService {
    if (!RechargeService.instance) {
      RechargeService.instance = new RechargeService();
    }
    return RechargeService.instance;
  }

  // 创建充值订单
  public async createRechargeOrder(rechargeData: RechargeRequest): Promise<RechargeApiResponse<RechargeRecord>> {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          message: '用户未登录',
          error_code: 'NOT_AUTHENTICATED'
        };
      }

      console.log('💰 创建充值订单:', rechargeData);

      // 生成订单ID
      const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 构建明道云API请求数据 - 正确处理关联字段
      const controls = [
        {
          controlId: MINGDAO_AUTH_CONFIG.rechargeFields.user,
          value: JSON.stringify([{
            type: 0,
            sid: currentUser.id,  // 用户的rowid
            name: currentUser.username
          }])
        },
        { controlId: MINGDAO_AUTH_CONFIG.rechargeFields.order_id, value: orderId },
        { controlId: MINGDAO_AUTH_CONFIG.rechargeFields.amount, value: rechargeData.amount },
        { controlId: MINGDAO_AUTH_CONFIG.rechargeFields.type, value: rechargeData.type },
        { controlId: MINGDAO_AUTH_CONFIG.rechargeFields.method, value: rechargeData.method },
        { controlId: MINGDAO_AUTH_CONFIG.rechargeFields.status, value: 'pending' },
        { controlId: MINGDAO_AUTH_CONFIG.rechargeFields.description, value: this.getRechargeDescription(rechargeData) },
        { controlId: MINGDAO_AUTH_CONFIG.rechargeFields.quota_granted, value: rechargeData.type === 'quota' ? rechargeData.amount : 0 }
      ];

      const requestData = {
        appKey: MINGDAO_AUTH_CONFIG.appKey,
        sign: MINGDAO_AUTH_CONFIG.sign,
        worksheetId: MINGDAO_AUTH_CONFIG.worksheets.recharge_records,
        triggerWorkflow: true,
        controls: controls
      };

      console.log('📤 发送充值订单请求:', requestData);

      const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/addRow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📥 充值订单响应:', result);

      if (result.success) {
        const rechargeRecord: RechargeRecord = {
          id: result.data,
          userId: currentUser.id,
          amount: rechargeData.amount,
          type: rechargeData.type,
          method: rechargeData.method,
          status: 'pending',
          orderId: orderId,
          description: this.getRechargeDescription(rechargeData),
          createdAt: new Date().toISOString()
        };

        return {
          success: true,
          data: rechargeRecord,
          message: '充值订单创建成功'
        };
      } else {
        return {
          success: false,
          message: result.message || '创建充值订单失败',
          error_code: result.error_code
        };
      }

    } catch (error) {
      console.error('❌ 创建充值订单失败:', error);
      return {
        success: false,
        message: `创建订单失败: ${error}`,
        error_code: 'NETWORK_ERROR'
      };
    }
  }

  // 获取充值记录列表
  public async getRechargeRecords(pageIndex: number = 1, pageSize: number = 10): Promise<RechargeApiResponse<RechargeRecord[]>> {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          message: '用户未登录',
          error_code: 'NOT_AUTHENTICATED'
        };
      }

      console.log('📋 获取充值记录列表');

      // 构建查询条件 - 使用关联字段过滤
      const filters = [
        {
          controlId: MINGDAO_AUTH_CONFIG.rechargeFields.user,
          dataType: MINGDAO_DATA_TYPES.RELATION,
          spliceType: 1,
          filterType: MINGDAO_FILTER_TYPES.RELATION_CONTAINS,
          value: currentUser.id  // 用户的rowid
        }
      ];

      const requestData = {
        appKey: MINGDAO_AUTH_CONFIG.appKey,
        sign: MINGDAO_AUTH_CONFIG.sign,
        worksheetId: MINGDAO_AUTH_CONFIG.worksheets.recharge_records,
        pageSize: pageSize,
        pageIndex: pageIndex,
        listType: 0,
        controls: [],
        filters: filters,
        sortId: MINGDAO_AUTH_CONFIG.rechargeFields.created_at,
        isAsc: false // 按创建时间倒序
      };

      console.log('📤 发送充值记录查询请求:', requestData);

      const response = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/getFilterRows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📥 充值记录响应:', result);

      if (result.success && result.data && result.data.rows) {
        const records: RechargeRecord[] = result.data.rows.map((row: any) => {
          // 解析关联用户字段
          let userId = '';
          try {
            const userField = JSON.parse(row[MINGDAO_AUTH_CONFIG.rechargeFields.user] || '[]');
            if (userField.length > 0) {
              userId = userField[0].sid;
            }
          } catch (e) {
            console.warn('解析用户关联字段失败:', e);
          }

          return {
            id: row.rowid,
            userId: userId,
            amount: parseFloat(row[MINGDAO_AUTH_CONFIG.rechargeFields.amount] || '0'),
            type: row[MINGDAO_AUTH_CONFIG.rechargeFields.type],
            method: row[MINGDAO_AUTH_CONFIG.rechargeFields.method],
            status: row[MINGDAO_AUTH_CONFIG.rechargeFields.status],
            orderId: row[MINGDAO_AUTH_CONFIG.rechargeFields.order_id],
            description: row[MINGDAO_AUTH_CONFIG.rechargeFields.description],
            createdAt: row[MINGDAO_AUTH_CONFIG.rechargeFields.created_at],
            completedAt: row[MINGDAO_AUTH_CONFIG.rechargeFields.completed_at]
          };
        });

        return {
          success: true,
          data: records,
          message: '获取充值记录成功'
        };
      } else {
        return {
          success: false,
          message: '获取充值记录失败',
          error_code: 'NO_DATA'
        };
      }

    } catch (error) {
      console.error('❌ 获取充值记录失败:', error);
      return {
        success: false,
        message: `获取记录失败: ${error}`,
        error_code: 'NETWORK_ERROR'
      };
    }
  }

  // 模拟支付处理（实际项目中需要对接真实的支付接口）
  public async processPayment(orderId: string): Promise<RechargeApiResponse<{ paymentUrl: string }>> {
    try {
      console.log('💳 处理支付:', orderId);

      // 模拟支付处理延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟支付成功（90%成功率）
      const isSuccess = Math.random() > 0.1;

      if (isSuccess) {
        // 更新订单状态为成功
        await this.updateOrderStatus(orderId, 'success');

        // 模拟支付URL（实际项目中应该返回真实的支付链接）
        const paymentUrl = `https://payment.example.com/pay?order=${orderId}`;

        return {
          success: true,
          data: { paymentUrl },
          message: '支付处理成功'
        };
      } else {
        // 更新订单状态为失败
        await this.updateOrderStatus(orderId, 'failed');

        return {
          success: false,
          message: '支付处理失败',
          error_code: 'PAYMENT_FAILED'
        };
      }

    } catch (error) {
      console.error('❌ 支付处理失败:', error);
      return {
        success: false,
        message: `支付失败: ${error}`,
        error_code: 'PAYMENT_ERROR'
      };
    }
  }

  // 更新订单状态
  private async updateOrderStatus(orderId: string, status: 'pending' | 'success' | 'failed'): Promise<void> {
    try {
      // 首先查找订单
      const filters = [
        {
          controlId: MINGDAO_AUTH_CONFIG.rechargeFields.order_id,
          dataType: MINGDAO_DATA_TYPES.TEXT,
          spliceType: 1,
          filterType: MINGDAO_FILTER_TYPES.EQUAL,
          value: orderId
        }
      ];

      const queryData = {
        appKey: MINGDAO_AUTH_CONFIG.appKey,
        sign: MINGDAO_AUTH_CONFIG.sign,
        worksheetId: MINGDAO_AUTH_CONFIG.worksheets.recharge_records,
        pageSize: 1,
        pageIndex: 1,
        listType: 0,
        controls: [],
        filters: filters
      };

      const queryResponse = await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/getFilterRows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(queryData)
      });

      const queryResult = await queryResponse.json();

      if (queryResult.success && queryResult.data && queryResult.data.rows && queryResult.data.rows.length > 0) {
        const orderRow = queryResult.data.rows[0];
        
        // 更新订单状态
        const controls = [
          { controlId: MINGDAO_AUTH_CONFIG.rechargeFields.status, value: status }
        ];

        if (status === 'success') {
          controls.push({ controlId: MINGDAO_AUTH_CONFIG.rechargeFields.completed_at, value: new Date().toISOString() });
        }

        const updateData = {
          appKey: MINGDAO_AUTH_CONFIG.appKey,
          sign: MINGDAO_AUTH_CONFIG.sign,
          worksheetId: MINGDAO_AUTH_CONFIG.worksheets.recharge_records,
          rowId: orderRow.rowid,
          controls: controls
        };

        await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/editRow`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData)
        });

        // 如果支付成功，更新用户余额或配额
        if (status === 'success') {
          await this.updateUserBalance(orderRow);
        }

        console.log(`✅ 订单状态已更新为: ${status}`);
      }

    } catch (error) {
      console.error('⚠️ 更新订单状态失败:', error);
    }
  }

  // 更新用户余额或配额
  private async updateUserBalance(orderData: any): Promise<void> {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) return;

      const amount = parseFloat(orderData[MINGDAO_AUTH_CONFIG.rechargeFields.amount] || '0');
      const type = orderData[MINGDAO_AUTH_CONFIG.rechargeFields.type];

      let controls: any[] = [];

      if (type === 'balance') {
        // 充值余额
        const newBalance = currentUser.balance + amount;
        controls.push({ controlId: MINGDAO_AUTH_CONFIG.userFields.balance, value: newBalance });
      } else if (type === 'quota') {
        // 充值配额
        const newQuota = currentUser.totalQuota + amount;
        controls.push({ controlId: MINGDAO_AUTH_CONFIG.userFields.total_quota, value: newQuota });
      }

      controls.push({ controlId: MINGDAO_AUTH_CONFIG.userFields.updated_at, value: new Date().toISOString() });

      const updateData = {
        appKey: MINGDAO_AUTH_CONFIG.appKey,
        sign: MINGDAO_AUTH_CONFIG.sign,
        worksheetId: MINGDAO_AUTH_CONFIG.worksheets.users,
        rowId: currentUser.id,
        controls: controls
      };

      await fetch(`${MINGDAO_AUTH_CONFIG.baseUrl}/worksheet/editRow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      });

      // 刷新用户信息
      await authService.refreshUserInfo();

      console.log(`✅ 用户${type}已更新，增加: ${amount}`);

    } catch (error) {
      console.error('⚠️ 更新用户余额失败:', error);
    }
  }

  // 获取充值描述
  private getRechargeDescription(rechargeData: RechargeRequest): string {
    const typeText = rechargeData.type === 'balance' ? '余额' : '配额';
    const methodText = {
      'alipay': '支付宝',
      'wechat': '微信支付',
      'bank': '银行卡'
    }[rechargeData.method] || rechargeData.method;

    return `${methodText}充值${typeText} ¥${rechargeData.amount}`;
  }
}

// 导出单例实例
export const rechargeService = RechargeService.getInstance();
