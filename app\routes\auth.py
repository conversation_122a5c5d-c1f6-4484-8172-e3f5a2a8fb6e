"""
认证相关路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_async_db
from app.schemas.auth import (
    UserRegister, User<PERSON>ogin, Token, UserProfile,
    UserUpdate, PasswordChange, TokenRefresh
)
from app.services.auth_service import AuthService
from app.utils.dependencies import get_current_active_user
from app.utils.security import get_password_hash, verify_password, verify_refresh_token, create_access_token
from app.utils.logger import logger
from app.config import settings


router = APIRouter()


@router.post("/register", response_model=UserProfile, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_async_db)
):
    """用户注册"""
    try:
        user = await AuthService.register_user(db, user_data)
        return UserProfile(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            translation_quota=user.translation_quota,
            used_quota=user.used_quota,
            remaining_quota=user.remaining_quota,
            preferred_source_lang=user.preferred_source_lang,
            preferred_target_lang=user.preferred_target_lang,
            created_at=user.created_at.isoformat() if user.created_at else None,
            last_login=user.last_login.isoformat() if user.last_login else None
        )
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise


@router.post("/login", response_model=Token)
async def login(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_async_db)
):
    """用户登录"""
    try:
        token_data = await AuthService.login_user(db, login_data)
        return Token(**token_data)
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: TokenRefresh,
    db: AsyncSession = Depends(get_async_db)
):
    """刷新访问令牌"""
    try:
        # 验证刷新令牌
        payload = verify_refresh_token(token_data.refresh_token)
        username = payload.get("sub")
        
        # 获取用户信息
        user = await AuthService.get_user_by_username(db, username)
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # 创建新的访问令牌
        access_token = create_access_token(
            data={"sub": user.username, "user_id": user.id, "role": user.role.value}
        )
        
        return Token(
            access_token=access_token,
            refresh_token=token_data.refresh_token,  # 保持原刷新令牌
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60
        )
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise


@router.get("/profile", response_model=UserProfile)
async def get_profile(
    current_user = Depends(get_current_active_user)
):
    """获取用户资料"""
    return UserProfile(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        role=current_user.role,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        translation_quota=current_user.translation_quota,
        used_quota=current_user.used_quota,
        remaining_quota=current_user.remaining_quota,
        preferred_source_lang=current_user.preferred_source_lang,
        preferred_target_lang=current_user.preferred_target_lang,
        created_at=current_user.created_at.isoformat() if current_user.created_at else None,
        last_login=current_user.last_login.isoformat() if current_user.last_login else None
    )


@router.put("/profile", response_model=UserProfile)
async def update_profile(
    user_update: UserUpdate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """更新用户资料"""
    try:
        # 更新用户信息
        if user_update.full_name is not None:
            current_user.full_name = user_update.full_name
        if user_update.preferred_source_lang is not None:
            current_user.preferred_source_lang = user_update.preferred_source_lang
        if user_update.preferred_target_lang is not None:
            current_user.preferred_target_lang = user_update.preferred_target_lang
        
        await db.commit()
        await db.refresh(current_user)
        
        logger.info(f"User profile updated: {current_user.username}")
        
        return UserProfile(
            id=current_user.id,
            username=current_user.username,
            email=current_user.email,
            full_name=current_user.full_name,
            role=current_user.role,
            is_active=current_user.is_active,
            is_verified=current_user.is_verified,
            translation_quota=current_user.translation_quota,
            used_quota=current_user.used_quota,
            remaining_quota=current_user.remaining_quota,
            preferred_source_lang=current_user.preferred_source_lang,
            preferred_target_lang=current_user.preferred_target_lang,
            created_at=current_user.created_at.isoformat() if current_user.created_at else None,
            last_login=current_user.last_login.isoformat() if current_user.last_login else None
        )
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise


@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """修改密码"""
    try:
        # 验证当前密码
        if not verify_password(password_data.current_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Incorrect current password"
            )
        
        # 更新密码
        current_user.hashed_password = get_password_hash(password_data.new_password)
        await db.commit()
        
        logger.info(f"Password changed for user: {current_user.username}")
        
        return {"message": "Password changed successfully"}
    except Exception as e:
        logger.error(f"Password change error: {e}")
        raise
