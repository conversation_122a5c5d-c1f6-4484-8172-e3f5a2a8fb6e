#!/bin/bash

# 静态文件部署脚本（无需 Node.js）
# 适用于无法安装 Node.js 的服务器

echo "🚀 开始静态文件部署..."

# 检查是否有 dist 目录
if [ ! -d "dist" ]; then
    echo "❌ 未找到 dist 目录，请先在本地构建："
    echo "   npm run build"
    echo "   然后将整个项目上传到服务器"
    exit 1
fi

echo "✅ 找到构建文件"

# 设置部署目录
DEPLOY_DIR="/var/www/html/translate-front"

# 创建部署目录
echo "📁 创建部署目录: $DEPLOY_DIR"
sudo mkdir -p $DEPLOY_DIR

# 复制静态文件
echo "📋 复制静态文件..."
sudo cp -r dist/* $DEPLOY_DIR/

# 设置权限
echo "🔐 设置文件权限..."
sudo chown -R www-data:www-data $DEPLOY_DIR
sudo chmod -R 755 $DEPLOY_DIR

# 创建简单的 API 代理配置
echo "⚙️ 创建 Nginx 配置..."

# 创建 Nginx 配置文件
sudo tee /etc/nginx/sites-available/translate-front > /dev/null <<EOF
server {
    listen 80;
    server_name _;
    
    root $DEPLOY_DIR;
    index index.html;
    
    # 静态文件服务
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # API 代理到明道云
    location /api/ {
        proxy_pass https://dmit.duoningbio.com;
        proxy_set_header Host dmit.duoningbio.com;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # CORS 头部
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Accept, Authorization";
        
        # 处理 OPTIONS 预检请求
        if (\$request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 启用站点
echo "🔗 启用 Nginx 站点..."
sudo ln -sf /etc/nginx/sites-available/translate-front /etc/nginx/sites-enabled/

# 测试 Nginx 配置
echo "🧪 测试 Nginx 配置..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx 配置正确"
    
    # 重启 Nginx
    echo "🔄 重启 Nginx..."
    sudo systemctl restart nginx
    
    echo ""
    echo "🎉 部署完成！"
    echo "📍 访问地址: http://your-server-ip"
    echo ""
    echo "📋 部署信息:"
    echo "  - 静态文件目录: $DEPLOY_DIR"
    echo "  - Nginx 配置: /etc/nginx/sites-available/translate-front"
    echo "  - API 代理: /api/* -> https://dmit.duoningbio.com/api/*"
    echo ""
    echo "🔧 如需修改配置:"
    echo "  sudo nano /etc/nginx/sites-available/translate-front"
    echo "  sudo systemctl restart nginx"
else
    echo "❌ Nginx 配置有误，请检查"
    exit 1
fi
