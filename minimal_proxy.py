"""
最小化代理服务器
"""
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
import requests
from urllib.parse import urlparse, parse_qs

class ProxyHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_GET(self):
        """处理GET请求"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/health':
            response = {"status": "healthy", "service": "minimal-proxy"}
        elif self.path == '/api/mingdao/pricing':
            response = {
                "success": True,
                "pricing_rules": {
                    "monthly": {"price": 20, "quota": 100000},
                    "small": {"price": 5, "max_chars": 5000},
                    "large": {"price_per_5k": 5},
                    "bulk": {"price": 100, "min_chars": 300000}
                }
            }
        else:
            response = {"message": "Minimal Proxy Server", "path": self.path}
        
        self.wfile.write(json.dumps(response).encode())

    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode())
        except:
            data = {}
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/api/mingdao/calculate-cost':
            char_count = data.get('charCount', 0)
            
            if char_count <= 5000:
                cost = 5
                payment_type = "按次付费"
            elif char_count >= 300000:
                cost = 100
                payment_type = "批量优惠"
            else:
                cost = ((char_count - 1) // 5000 + 1) * 5
                payment_type = "按次付费"
            
            response = {
                "success": True,
                "char_count": char_count,
                "cost": cost,
                "payment_type": payment_type,
                "description": f"{char_count}字符，{payment_type}"
            }
        
        elif self.path == '/api/mingdao/test':
            # 测试明道云连接
            try:
                mingdao_data = {
                    "appKey": "d88c1d2329c42504",
                    "sign": "YTJjZDM3ODMzYjc5ZTBiODI0NjYwOGNhOGU0YjZhODA4NTZkNTQ4NDM5Zjc0ZWY4NDI4MGM2ZjdmZjYzNjcyOA==",
                    "worksheetId": "6886e20ba849420e13f69b23"
                }
                
                mingdao_response = requests.post(
                    "https://dmit.duoningbio.com/api/v2/open/worksheet/getWorksheetInfo",
                    json=mingdao_data,
                    timeout=10
                )
                
                response = {
                    "success": True,
                    "message": "明道云连接测试成功",
                    "status_code": mingdao_response.status_code,
                    "mingdao_success": mingdao_response.json().get('success', False)
                }
            except Exception as e:
                response = {
                    "success": False,
                    "message": "明道云连接测试失败",
                    "error": str(e)
                }
        
        else:
            response = {"message": "POST endpoint", "path": self.path, "data": data}
        
        self.wfile.write(json.dumps(response).encode())

def run_server():
    server_address = ('', 9000)
    httpd = HTTPServer(server_address, ProxyHandler)
    print("🚀 最小化代理服务器启动")
    print("📖 访问: http://localhost:9000")
    print("🔗 健康检查: http://localhost:9000/health")
    print("=" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
